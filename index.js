const container = require('./src/container');

const app = container.resolve('server');
const fastify = container.resolve('fastify');
const puppeteerBrowser = container.resolve('puppeteerBrowser');

// Initialize the browser before starting the server
(async () => {
  try {
    // Pre-initialize the browser
    await puppeteerBrowser.getInstance();
    console.log('Puppeteer browser initialized successfully');
    
    // Start the server after browser is ready
    await app.startFastify();
  } catch (error) {
    fastify.log.error(`Error: ${error}`);
    process.exit(1);
  }
})();
