meta {
  name: Purchase Order PDF
  type: http
  seq: 4
}

get {
  url: {{base_url}}/v1/generate-template/purchase-order/:id
  body: none
  auth: bearer
}

params:path {
  id: 285
}

auth:bearer {
  token: {{access_token}}
}

vars:pre-request {
  download_filename: PO-{{purchaseOrderId}}.pdf
}

tests {
  // Test that we received a PDF file
  test("Response should be a PDF file", function() {
    const contentType = res.getHeader("content-type");
    expect(contentType).to.equal("application/pdf");
  });
  
  // Test that Content-Disposition header is present
  test("Content-Disposition header should be present", function() {
    const contentDisposition = res.getHeader("content-disposition");
    expect(contentDisposition).to.include("attachment");
    expect(contentDisposition).to.include("filename=");
  });
  
  // Test response status
  test("Response status should be 200", function() {
    expect(res.getStatus()).to.equal(200);
  });
  
  // Test that response body is not empty
  test("Response body should not be empty", function() {
    expect(res.getBody()).to.not.be.empty;
  });
}

docs {
  # Purchase Order PDF Download
  
  This endpoint generates and downloads a PDF version of a Purchase Order.
  
  ## Usage
  
  - Replace `:purchaseOrderId` with the ID of the purchase order you want to download
  - The response will be a PDF file with appropriate headers for download
  - The file will be automatically saved to the `./downloads` directory
  
  ## Response
  
  - Content-Type: application/pdf
  - Content-Disposition: attachment; filename="PO-xxx.pdf"
}
