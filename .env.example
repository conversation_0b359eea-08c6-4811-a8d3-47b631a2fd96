# Application config/secrets
PORT=4000
HOST=0.0.0.0
JWT_SECRET=secret
NODE_ENV=development
OTP_KEY=U29tZVNlY3JldEtleVdpdGg2NEJ5dGVz # Minimum of 32 characters - crypto encrypt
PASS_SECRET=12345
BYPASS_OTP=true

# Database configs
POSTGRES_HOST=postgres
POSTGRES_DB=prs
POSTGRES_PORT=5432
POSTGRES_USER=admin
POSTGRES_PASSWORD=admin
DIALECT=postgres
POOL_MIN=0
POOL_MAX=5
POOL_ACQUIRE=30000
POOL_IDLE=10000
POOL_EVICTION=20000 #20 secs

# Root User
ROOT_USER_NAME=rootuser
ROOT_USER_EMAIL=<EMAIL>
ROOT_USER_PASSWORD=rootuser

# API Integration
CITYLAND_API_URL=https://cmd-test.free.beeceptor.com
CITYLAND_ACCOUNTING_URL=https://cityland-accounting.free.beeceptor.com

# Department Association
ASSOCIATION_DEPARTMENT_CODE=10


# For macbook
CHROME_PATH=/opt/homebrew/bin/chromium