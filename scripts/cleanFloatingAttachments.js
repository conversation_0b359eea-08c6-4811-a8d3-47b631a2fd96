const { Sequelize } = require('sequelize');
const getConnection = require('../src/infra/database/config/dbConnection');
const fs = require('fs');
const path = require('path');

async function cleanAttachmentsTable() {
  let sequelize;
  try {
    sequelize = await getConnection();
    console.log('Connection has been established successfully.');

    const result = await sequelize.query(
      `SELECT id, path FROM attachments WHERE model_id = 0;`,
      { type: sequelize.QueryTypes.SELECT }
    );
    const transaction = await sequelize.transaction();

    const promises = result.map(async (attachment) => {
      const filePathToDelete = path.resolve('upload', `.${attachment.path}`);
      const isExistingFile = fs.existsSync(filePathToDelete);
      if (isExistingFile) {
        fs.unlinkSync(filePathToDelete);
      } else {
        console.log('File does not exist:', filePathToDelete);
      }

      await sequelize.query(
        `DELETE FROM attachments WHERE id = :id`,
        {
          replacements: { id: attachment.id },
          transaction,
        }
      );
    });

    try {
      await Promise.all(promises);
      await transaction.commit();
      sequelize.close();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
    console.log('Successfully cleaned up floating attachments.');
  }catch (error) {
    console.error('Unable to execute script:', error);
    sequelize?.close();
  }
}

cleanAttachmentsTable();
