'use strict';

const { Sequelize } = require('sequelize');
const getConnection = require('../src/infra/database/config/dbConnection');

async function cleanPaymentRequests(requisitionId) {
  if (!requisitionId) {
    console.error('Error: requisition_id parameter is required');
    process.exit(1);
  }

  try {
    const sequelize = await getConnection();
    console.log('Database connection established successfully.');
    
    console.log(`Starting cleanup process for requisition_id: ${requisitionId}`);
    
    // Begin transaction
    const transaction = await sequelize.transaction();
    
    try {
      // 1. Get payment request IDs for this requisition
      const paymentRequests = await sequelize.query(
        `SELECT id FROM rs_payment_requests WHERE requisition_id = :requisitionId`,
        {
          replacements: { requisitionId },
          type: Sequelize.QueryTypes.SELECT,
          transaction
        }
      );
      
      if (paymentRequests.length > 0) {
        // Delete rs_payment_request_approvers records
        await sequelize.query(
          `DELETE FROM rs_payment_request_approvers WHERE payment_request_id IN (
            SELECT id FROM rs_payment_requests WHERE requisition_id = :requisitionId
          )`,
          {
            replacements: { requisitionId },
            type: Sequelize.QueryTypes.DELETE,
            transaction
          }
        );
        const prIds = paymentRequests.map(pr => pr.id);
        console.log(`Found ${prIds.length} payment requests to process`);
        
        // 2. Set payment_request_id to null in related tables
        await sequelize.query(
          `UPDATE rs_payment_request_approvers SET payment_request_id = NULL 
           WHERE payment_request_id IN (:prIds)`,
          {
            replacements: { prIds },
            type: Sequelize.QueryTypes.UPDATE,
            transaction
          }
        );
        
        // 3. Delete payment request records
        const deletedPRs = await sequelize.query(
          `DELETE FROM rs_payment_requests WHERE id IN (:prIds) RETURNING id`,
          {
            replacements: { prIds },
            type: Sequelize.QueryTypes.DELETE,
            transaction
          }
        );
        
        console.log(`Deleted ${deletedPRs[0].length} payment request records`);
      }
      
      await transaction.commit();
      console.log('Cleanup completed successfully');
      
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
    
    await sequelize.close();
    console.log('Database connection closed.');
    
  } catch (error) {
    console.error('Error during cleanup:', error);
    process.exit(1);
  }
}

if (process.env.NODE_ENV === 'local') {
  const requisitionId = process.argv[2];
  cleanPaymentRequests(requisitionId);
} else {
  console.log('This script is only for local environment');
}
