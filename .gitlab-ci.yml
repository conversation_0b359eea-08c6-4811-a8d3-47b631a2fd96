include:
  - template: Jobs/SAST.gitlab-ci.yml
  - template: Security/Secret-Detection.gitlab-ci.yml

stages:
  - build
  - test
  - secure
  - metrics
  - deploy

variables:
  CI_IMAGE_NAME: $CI_REGISTRY_IMAGE
  CI_IMAGE_TAG: $CI_PIPELINE_ID
  CI_DOCKER_IMAGE_LATEST: $CI_IMAGE_NAME:latest
  CI_DOCKER_IMAGE_STAGING: $CI_IMAGE_NAME:staging
  CI_DOCKER_IMAGE_DEVELOP: $CI_IMAGE_NAME:develop
  CI_DOCKER_IMAGE_UAT: $CI_IMAGE_NAME:uat
  CI_DOCKER_IMAGE: $CI_IMAGE_NAME:$CI_IMAGE_TAG
  ECR_REPO_TAG: $CI_COMMIT_REF_NAME
  AWS_REGION: 'ap-southeast-1'
  NPM_CACHE_FOLDER: .npm-cache
  CI_RUNNER_TAGS: $CI_RUNNER_TAGS
  CI_SECURITY_TAG: $CI_SECURITY_TAG

default:
  tags:
    - $CI_SECURITY_TAG

.pre-install: &pre-install
  script:
    - npm install
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
      - .npm-cache/

build:clean:
  <<: *pre-install
  stage: build
  script:
    - npm install
    - npm run fix
    - npm run lint
  allow_failure: true # for development purposes but set value to false by default
  only:
    - uat
  tags:
    - $CI_RUNNER_TAGS

test:unit:
  <<: *pre-install
  stage: test
  script:
    - npm install
    - mkdir -p test-results
    - npm run test
    - npm test > test-results/test.log 2>&1
  artifacts:
    paths:
      - test-results/
    expire_in: 1 week
  allow_failure: true # for development purposes but set value to false by default
  only:
    - uat
  tags:
    - $CI_RUNNER_TAGS

# #Stage for running security audit with auditjs
secure:audit:
  <<: *pre-install
  stage: secure
  script:
    - npm install
    - echo "Running AuditJS Dependency Scan..."
    - npm install -g auditjs
    - cd $CI_PROJECT_DIR # Navigate to the project root directory
    - ls -la # List directory contents to confirm files are present
    - cat package.json # Confirm package.json content
    - set +e # Disable immediate exit on error
    - auditjs ossi -u $AUDITJS_EMAIL --password-stdin -q -w whitelist-auditjs.json --fail-on high < <(echo "$AUDITJS_PASSWORD")
    - set -e # Re-enable immediate exit on error
    - echo "AuditJS exited with code $EXIT_CODE"
    - if [[ $EXIT_CODE -ne 0 ]]; then
      echo "AuditJS found vulnerabilities and exited with code $EXIT_CODE";
      if [[ $EXIT_CODE -eq 4 || $EXIT_CODE -eq 3 ]]; then exit 1; fi;
      fi
  allow_failure: true # Allow failure for development purposes, set to false for production
  only:
    - uat
  tags:
    - $CI_RUNNER_TAGS

secure:sonarqube:
  stage: secure
  variables:
    SONAR_USER_HOME: '${CI_PROJECT_DIR}/.sonar'
    GIT_DEPTH: 0
  cache:
    key: '${CI_JOB_NAME}'
    paths:
      - .sonar/cache
  script:
    - npm install
    # - npm run test:coverage # to follow up
    - sonar-scanner -Dsonar.projectKey="$SONARQUBE_PROJECT_KEY" -Dsonar.sources=. -Dsonar.host.url="$SONAR_HOST_URL" -Dsonar.login="$SONAR_TOKEN"
  allow_failure: false
  only:
    - uat
  tags:
    - $CI_RUNNER_TAGS

secure:secret_detection:
  stage: secure
  variables:
    SECRET_DETECTION_HISTORIC_SCAN: 'true'
  script:
    - echo "Detecting Password, Secrets, and Tokens..."
  rules:
    - if: $CI_COMMIT_BRANCH
      when: always
  tags:
    - $CI_SECURITY_TAG

secure:sast:
  stage: secure
  script:
    - echo "Running Static Application Security Tests..."
  only:
    - uat
  tags:
    - $CI_SECURITY_TAG

semgrep-sast:
  stage: secure
  variables:
    SAST_ANALYZER_IMAGE_TAG: '3.7'
  allow_failure: true
  tags:
    - $CI_SECURITY_TAG

secure:dast:
  stage: secure
  script:
    - echo "Running Dynamic Application Security Tests..."
  only:
    - uat
  tags:
    - $CI_RUNNER_TAGS

metrics:scqs:
  stage: metrics
  script:
    - echo "Installing dependencies..."
    - npm i eslint scqs-sheets-stratpoint
    - cd $CI_PROJECT_DIR
    - echo "Fetching pipeline status..."
    - echo $PRIVATE_TOKEN
    - echo $PROJECT_ID
    - echo $GITLAB_HOSTNAME
    - echo $SONAR_PROJECT_KEY
    - echo $SONAR_USER_TOKEN
    - echo $SONAR_HOSTNAME
    - echo $SHEET_TAB_SCQS
    - echo $SHEET_ID_SCQS
    - echo $STRINGIFIED_SERVICE_ACCOUNT_CREDENTIALS
    - |
      PIPELINE_STATUS="$( \
        node ./node_modules/scqs-sheets-stratpoint/util/pipeline-status.js \
          --private_token=$PRIVATE_TOKEN \
          --project_id=$PROJECT_ID \
          --updated_after=$SPRINT_START \
          --updated_before=$SPRINT_END \
          --ref='develop' \ # If there is a production app use the production branch
          --hostname='sonarqube.stratpoint.dev')"

      echo "PIPELINE_STATUS: " $PIPELINE_STATUS

      IFS=','
      read -ra PARSED_STATUS <<< "$PIPELINE_STATUS"
    - |
      CODE_REVIEW="$( \
        node ./node_modules/scqs-sheets-stratpoint/util/code-review.js \
          --private_token=$PRIVATE_TOKEN \
          --project_id=$PROJECT_ID \
          --updated_after=$SPRINT_START \
          --updated_before=$SPRINT_END \
          --hostname=$GITLAB_HOSTNAME)"

      echo "CODE_REVIEW: " $CODE_REVIEW
      read -ra PARSED_CODE <<< "$CODE_REVIEW"
    - |
      TEST_COVERAGE="$( \
        node ./node_modules/scqs-sheets-stratpoint/util/sonarqube-metrics.js \
          --sonar_project_key=$SONAR_PROJECT_KEY \
          --sonar_user_token=$SONAR_USER_TOKEN \
          --metric_key='branch_coverage' \
          --sonar_hostname=$SONAR_HOSTNAME)"

      echo "TEST_COVERAGE: " $TEST_COVERAGE
      read -ra PARSED_TC <<< "$TEST_COVERAGE"
    - |
      CODE_DUPLICATION="$( \
        node ./node_modules/scqs-sheets-stratpoint/util/sonarqube-metrics.js \
          --sonar_project_key=$SONAR_PROJECT_KEY \
          --sonar_user_token=$SONAR_USER_TOKEN \
          --metric_key='duplicated_blocks' \
          --sonar_hostname=$SONAR_HOSTNAME)"

      echo "CODE_DUPLICATION: " $CODE_DUPLICATION
      read -ra PARSED_CD <<< "$CODE_DUPLICATION"
    - SCORE="$(bash ./node_modules/scqs-sheets-stratpoint/util/parseScore.sh)"
    - |
      DATA='[[
        "'$SPRINT'",
        "'${PARSED_STATUS[0]}'", "'$SCORE'", "'${PARSED_STATUS[0]}'", "'${PARSED_STATUS[1]}'", "'${PARSED_STATUS[2]}'", "'${PARSED_STATUS[3]}'",
        "'${PARSED_TC[0]}'", "'${PARSED_TC[1]}'",
        "'${PARSED_CD[0]}'", "'${PARSED_CD[1]}'",
        "", "",
        "YES", "5",
        "YES", "5",
        "'${PARSED_CODE[0]}'", "'${PARSED_CODE[1]}'", "'${PARSED_CODE[2]}'", "'${PARSED_CODE[3]}'"
      ]]'
    - |
      node ./node_modules/scqs-sheets-stratpoint/index.js \
        --scqsVersion 2 \
        --sheetName $SHEET_TAB_SCQS \
        --spreadsheetId $SHEET_ID_SCQS \
        --updateValues "$DATA" \
        --credsString "$STRINGIFIED_SERVICE_ACCOUNT_CREDENTIALS"
  only:
    - uat
  when: manual
  tags:
    - $CI_RUNNER_TAGS

metrics:dora:
  stage: metrics
  script:
    - echo "Installing dependencies..."
    - npm i eslint scqs-sheets-stratpoint
    - cd $CI_PROJECT_DIR
    - echo $SPRINT_START && echo $SPRINT_END
    - |
      BRANCHES=("develop" "staging") # replace the branches with the branch you want to tract
      for BRANCH in "${BRANCHES[@]}"; do
        echo "Fetching DORA metrics for branch: $BRANCH"
        DORA_METRICS="$(node ./node_modules/scqs-sheets-stratpoint/util/dora-metrics.js \
          --private_token=$PRIVATE_TOKEN \
          --project_id=$PROJECT_ID \
          --updated_after=$SPRINT_START \
          --updated_before=$SPRINT_END \
          --ref=$BRANCH \
          --prod_branch=$PROD_BRANCH \ # optional parameter, default value prod
          --hostname=$GITLAB_HOSTNAME)"

        echo $DORA_METRICS
        IFS=','
        read -ra PARSED_DORA <<< "$DORA_METRICS"
        DATE_NOW="$(date +"%B %-d, %Y %H:%M:%S")"
        DATA='[["'${BRANCH}'", "'${PARSED_DORA[0]}'", "'${PARSED_DORA[1]}'", "'${PARSED_DORA[2]}'", "'${PARSED_DORA[3]}'", "'${SPRINT}'", "'${DATE_NOW}'"]]'
        node ./node_modules/scqs-sheets-stratpoint \
          --scqsVersion 2 \
          --sheetName $SHEET_TAB_DORA \
          --spreadsheetId $SHEET_ID \
          --credsString "$STRINGIFIED_SERVICE_ACCOUNT_CREDENTIALS" \
          --updateValues "$DATA"
      done
  only:
    - uat
  when: manual
  tags:
    - $CI_RUNNER_TAGS

dev:deploy:container:
  stage: deploy
  script:
    - echo "[Develop] Building the Docker image..."
    - echo $CI_REGISTRY
    - echo $CI_PROJECT_DIR
    - echo ${CI_DOCKER_IMAGE_DEVELOP}
    - echo $CI_PROJECT_DIR/Dockerfile
    - mkdir -p /kaniko/.docker
    - export AWS_ECR_PASSWORD=$(aws ecr get-login-password --region $AWS_REGION)
    - echo "{\"auths\":{\"$CI_REGISTRY\":{\"auth\":\"$(echo -n \"AWS:$AWS_ECR_PASSWORD\" | base64)\"}}}" > /kaniko/.docker/config.json
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
      --destination "${CI_REGISTRY_IMAGE}:${CI_PIPELINE_ID}"
      --destination "${CI_DOCKER_IMAGE_DEVELOP}"
      --use-new-run
      --snapshot-mode=redo
      --cleanup
  only:
    - develop
  tags:
    - $CI_RUNNER_TAGS

stage:deploy:container:
  stage: deploy
  script:
    - echo "[Staging] Building the Docker image..."
    - echo $CI_REGISTRY
    - echo $CI_PROJECT_DIR
    - echo ${CI_DOCKER_IMAGE_STAGING}
    - echo $CI_PROJECT_DIR/Dockerfile
    - mkdir -p /kaniko/.docker
    - export AWS_ECR_PASSWORD=$(aws ecr get-login-password --region $AWS_REGION)
    - echo "{\"auths\":{\"$CI_REGISTRY\":{\"auth\":\"$(echo -n \"AWS:$AWS_ECR_PASSWORD\" | base64)\"}}}" > /kaniko/.docker/config.json
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
      --destination "${CI_REGISTRY_IMAGE}:${CI_PIPELINE_ID}"
      --destination "${CI_DOCKER_IMAGE_STAGING}"
      --use-new-run
      --snapshot-mode=redo
      --cleanup
  only:
    - staging
  tags:
    - $CI_RUNNER_TAGS

# [UAT] Deploy to ECR
uat:deploy:container:
  stage: deploy
  script:
    - echo "[UAT] Building the Docker image..."
    - echo $CI_REGISTRY
    - echo $CI_PROJECT_DIR
    - echo ${CI_DOCKER_IMAGE_UAT}
    - echo $CI_PROJECT_DIR/Dockerfile
    - mkdir -p /kaniko/.docker
    - export AWS_ECR_PASSWORD=$(aws ecr get-login-password --region $AWS_REGION)
    - echo "{\"auths\":{\"$CI_REGISTRY\":{\"auth\":\"$(echo -n \"AWS:$AWS_ECR_PASSWORD\" | base64)\"}}}" > /kaniko/.docker/config.json
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
      --destination "${CI_REGISTRY_IMAGE}:${CI_PIPELINE_ID}"
      --destination "${CI_DOCKER_IMAGE_UAT}"
      --use-new-run
      --snapshot-mode=redo
      --cleanup
  only:
    - uat
  tags:
    - $CI_RUNNER_TAGS

# main:deploy:container:
#   stage: deploy
#   script:
#     - echo "[Production] Building the Docker image..."
#     - echo $CI_REGISTRY
#     - echo $CI_PROJECT_DIR
#     - echo ${CI_DOCKER_IMAGE_LATEST}
#     - echo $CI_PROJECT_DIR/Dockerfile
#     - mkdir -p /kaniko/.docker
#     - export AWS_ECR_PASSWORD=$(aws ecr get-login-password --region $AWS_REGION)
#     - echo "{\"auths\":{\"$CI_REGISTRY\":{\"auth\":\"$(echo -n \"AWS:$AWS_ECR_PASSWORD\" | base64)\"}}}" > /kaniko/.docker/config.json
#     - /kaniko/executor
#       --context "${CI_PROJECT_DIR}"
#       --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
#       --destination "${CI_DOCKER_IMAGE_LATEST}"
#   only:
#     - main
#   tags:
#     - $CI_RUNNER_TAGS

dev:deploy:ec2:
  stage: deploy
  image: debian:stable
  only:
    - develop
  needs:
    - dev:deploy:container
  before_script:
    - rm -rf /builds/cityland/prs/prs-backend/.git/index.lock || true
    - find /builds/cityland/prs/prs-backend/.git -type f -name "*.lock" -exec rm -f {} \;
  script:
    - apt-get update && apt-get install -y openssh-client
    - eval $(ssh-agent)
    - echo "${DEVELOP_APPLICATION_PEM}" > dev-prs-kp.pem
    - chmod 600 dev-prs-kp.pem
    - ssh-add dev-prs-kp.pem
    - ssh -o StrictHostKeyChecking=no <EMAIL>
      'cd /home/<USER>/cityland-prs/prs-infra &&
      aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin 545009862155.dkr.ecr.ap-southeast-1.amazonaws.com &&
      echo "Cleaning up Docker system..." &&
      echo "Removing untagged frontend images..." &&
      docker rmi -f $(docker images "545009862155.dkr.ecr.ap-southeast-1.amazonaws.com/prs/prs-frontend" --filter "dangling=true" -q) || true &&
      echo "Removing untagged backend images..." &&
      docker rmi -f $(docker images "545009862155.dkr.ecr.ap-southeast-1.amazonaws.com/prs/prs-backend" --filter "dangling=true" -q) || true &&
      docker-compose pull backend &&
      docker-compose down backend &&
      docker-compose up -d backend'

stage:deploy:ec2:
  stage: deploy
  image: debian:stable
  only:
    - staging
  needs:
    - stage:deploy:container
  before_script:
    - rm -rf /builds/cityland/prs/prs-backend/.git/index.lock || true
    - find /builds/cityland/prs/prs-backend/.git -type f -name "*.lock" -exec rm -f {} \;
  script:
    - apt-get update && apt-get install -y openssh-client
    - eval "$(ssh-agent -s)"
    - echo "${STAGING_APPLICATION_PEM}" > stage-prs-kp.pem
    - chmod 600 stage-prs-kp.pem
    - ssh-add stage-prs-kp.pem
    - ssh -o StrictHostKeyChecking=no ec2-user@"$STAGING_EC2_INSTANCE_KEY"
      'cd /home/<USER>/cityland-prs/prs-infra &&
      aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin 545009862155.dkr.ecr.ap-southeast-1.amazonaws.com &&
      echo "Cleaning up Docker system..." &&
      echo "Removing untagged frontend images..." &&
      docker rmi -f $(docker images "545009862155.dkr.ecr.ap-southeast-1.amazonaws.com/prs/prs-frontend" --filter "dangling=true" -q) || true &&
      echo "Removing untagged backend images..." &&
      docker rmi -f $(docker images "545009862155.dkr.ecr.ap-southeast-1.amazonaws.com/prs/prs-backend" --filter "dangling=true" -q) || true &&
      docker-compose pull backend &&
      docker-compose down backend &&
      docker-compose up -d backend'

uat:deploy:ec2:
  stage: deploy
  image: debian:stable
  only:
    - uat
  needs:
    - uat:deploy:container
  before_script:
    - rm -rf /builds/cityland/prs/prs-backend/.git/index.lock || true
    - find /builds/cityland/prs/prs-backend/.git -type f -name "*.lock" -exec rm -f {} \;
  script:
    - apt-get update && apt-get install -y openssh-client
    - eval "$(ssh-agent -s)"
    - echo "${UAT_APPLICATION_PEM}" > uat-prs-kp.pem
    - chmod 600 uat-prs-kp.pem
    - ssh-add uat-prs-kp.pem
    - ssh -o StrictHostKeyChecking=no ec2-user@"$UAT_EC2_INSTANCE_KEY"
      'cd /home/<USER>/cityland-prs/prs-infra &&
      aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin 545009862155.dkr.ecr.ap-southeast-1.amazonaws.com &&
      echo "Cleaning up Docker system..." &&
      echo "Removing untagged frontend images..." &&
      docker rmi -f $(docker images "545009862155.dkr.ecr.ap-southeast-1.amazonaws.com/prs/prs-frontend" --filter "dangling=true" -q) || true &&
      echo "Removing untagged backend images..." &&
      docker rmi -f $(docker images "545009862155.dkr.ecr.ap-southeast-1.amazonaws.com/prs/prs-backend" --filter "dangling=true" -q) || true &&
      docker-compose pull backend &&
      docker-compose down backend &&
      docker-compose up -d backend'
