# City Land PRS API

## Project Description

Backend Repository for City Land PRS Project

## Repo Description

## Tech Stack Requirements

- [Node v20.18.0](http://nodejs.org/)
- [Fastify](https://www.fastify.io/)
- [PostgreSql](https://www.postgresql.org/)
- [Sequelize](https://sequelize.org)
- [Docker](https://www.docker.com)
- [Awilix](https://www.npmjs.com/package/@fastify/awilix)
- [Mo<PERSON>](https://www.npmjs.com/package/mocha)
- [Chai](https://www.npmjs.com/package/chai)

## Setup Guide via Local

Step 1: Clone the repository in the GitLab

    git clone https://gitlab.stratpoint.dev/cityland/prs/prs-backend

Step 2: Install the dependencies with npm. Command:

    npm install or npm i

Step 3: Create a .env file and copy the .env.copy and change the values

Step 4: Run the application.

    docker-compose up --build

## Sequelize Migration - Setup

Step 1: Check the list of containers

    docker ps

Step 2: Get the container id of the node-fastify-core application and run docker exec to open the application

    docker exec -it <container id> bash

Step 3: Run the sequelize migration. This will create the PRS DB tables.

    npm run sequelize:migrate:dev

Step 4: Run the sequelize seeder. This will create the initial data for the tables such as admin account and roles.

    npm run sequelize:run:seeder:all:dev

## Application Unit Testing

Run the command for unit testing

    npm run test

## Accessing the Fastify Swagger for API Testing

Step 1. Open the below url to access the Fastify Swagger in Local

    http://localhost:4000/documentation

Step 2. List of API's will be displayed. You may now test the application.

## Commitizen

- A standard way of committing rules. Simply use \`git cz\` instead of \`git commit\` when committing and choose what type of change you are committing.

  git cz

## Linting

This guide assumes you are using VS Code as your code editor

### Code Quality and Styles

This template uses Eslint for code quality rules and Prettier for stylistic rules.

The rules are found in .eslintrc.json and .prettier.json. These rules were based on Airbnb style guides.

To enable this, you just need to install Eslint and Prettier extension

- [Eslint extension](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint)
- [Prettier extension](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)

To enable formatting the file on every save, go to File for Windows/Linux, Code for Mac then Preferences > Settings, search for \`Format On Save\` on the Users tab if you want to enable it for all your projects, otherwise, enable it in the Workspace tab.

### Security

- Install the [Sonarlint extension](https://www.sonarlint.org/vscode). This will highlight bugs and security vulnerabilities as you write code, with clear remediation guidance so you can fix them before the code is even committed.

  Once installed, simply open any source file, start coding, and you will start seeing issues reported by SonarLint.

- Install the [Snyk extension](https://marketplace.visualstudio.com/items?itemName=snyk-security.snyk-vulnerability-scanner) to fix vulnerabilities in node & npm dependencies with a click.

  After installation, go to the Snyk tab in the activity bar and click on \`Connect VS Code with Snyk\`. Once connected, you will see an \`OPEN SOURCE SECURITY\` section in the Snyk tab.

## Sequelize Seeders

### Generating a Seeder

To generate a seeder file, run the following command:

    npm run sequelize:generate:seeder:dev --name <seeder-name>

### Running a Specific Seeder

To run a specific seeder, use the following command:

    npm run sequelize:run:seeder:dev --seed <seeder-file-name>

### Running All Seeders

To run all seeders, execute the command:

    npm run sequelize:run:seeder:all:dev

## Sequelize Migrations

### Generating a Migration

To generate a migration file, use the command:

    npm run sequelize:generate:migration --name <migration-name>

### Running Migrations

To run all pending migrations, execute:

    npm run sequelize:migrate:dev

### Undoing Migrations

To undo the last migration, run:

    npm run sequelize:migrate:dev:undo

## References

- [Fastify Documentation](https://www.fastify.io/)
- [Docker Documentation](https://www.docker.com)
- [Domain Driven Design - DDD File Structure](https://dev.to/stevescruz/domain-driven-design-ddd-file-structure-4pja)
- [Open API Specification](https://www.openapis.org/)
- [SonarLint for VS Code](https://www.sonarlint.org/vscode)
- [Snyk Vulnerability Scanner for VS Code](https://docs.snyk.io/ide-tools/visual-studio-code-extension-for-snyk-code)


## Bruno How to use
1. Download bruno: [https://www.usebruno.com/downloads](https://www.usebruno.com/downloads)
2. Install bruno
3. Open bruno
4. Set name for the collection
5. Choose the backend repo directory