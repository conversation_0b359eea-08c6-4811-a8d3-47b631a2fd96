require('dotenv').config();
const bcrypt = require('bcryptjs');
const { default: Fastify } = require('fastify');
const { asClass, asValue, Lifetime } = require('awilix');
const { diContainer, fastifyAwilixPlugin } = require('@fastify/awilix');
const { v7: uuidv7 } = require('uuid');
const OTPAuth = require('otpauth');
const path = require('path');
const fastifyRequestContext = require('@fastify/request-context');
const transactionHooks = require('./app/utils/transactionHooks');
const Handlebars = require('handlebars');
const registerHelpers = require('./app/utils/handlebarsHelper');
const puppeteer = require('puppeteer-core');

const {
  errorHandler,
  clientErrors,
  serverErrors,
  infraError,
  userErrors,
} = require('./app/errors');
const server = require('./server');
const utils = require('./app/utils');
const constants = require('./domain/constants');
const entities = require('./domain/entities');
const database = require('./infra/database/models');
const interfaceRoutes = require('./interfaces/router');
const corsSettings = require('./interfaces/cors');
const logSettings = require('./infra/logs');
const middlewares = require('./app/handlers/middlewares');

const REQUEST_ID = 'x-request-id';
const fastify = Fastify({
  logger: logSettings,
  requestIdHeader: REQUEST_ID,
  requestIdLogLabel: REQUEST_ID,
  genReqId: () => uuidv7(),
  bodyLimit: 25 * 1024 * 1024, // Set body limit to 25 MB
});

fastify.setErrorHandler(errorHandler);
fastify.register(fastifyRequestContext);
fastify.register(require('@fastify/helmet'));
fastify.register(require('@fastify/compress'));
fastify.register(require('@fastify/auth'));
fastify.register(require('@fastify/cors'), corsSettings);
fastify.register(require('@fastify/jwt'), {
  secret: process.env.JWT_SECRET,
});

fastify.register(require('@fastify/rate-limit'), {
  max: parseInt(process.env.RATE_LIMIT, 200),
  timeWindow: '1 minute',
});
fastify.register(require('@fastify/multipart'), [
  {
    limits: {
      fileSize: 5 * 1024 * 1024, // 5 MB
    },
  },
  { attachFieldsToBody: true },
]);

// serve static files from /upload
fastify.register(require('@fastify/static'), {
  root: path.join(__dirname, '../upload'),
  prefix: '/upload/',
});

/* Register Awilix plugin */
fastify.register(fastifyAwilixPlugin, {
  disposeOnClose: true,
  disposeOnResponse: true,
});

fastify.register(transactionHooks);

fastify.addHook('onRequest', (request, reply, done) => {
  request.requestContext.set('log', request.log);
  request.requestContext.set('x-request-id', request.id);
  done();
});

fastify.addHook('onSend', (request, reply, payload, done) => {
  reply.header('X-Request-Id', request.id);
  done();
});

/* Expose Swagger in Non-Prod Envs */
if (process.env.NODE_ENV !== 'production') {
  fastify.register(require('@fastify/swagger'), {
    mode: 'static',
    specification: {
      path: './api-doc-explorer/openapi.yaml',
      postProcessor: function (swaggerObject) {
        return swaggerObject;
      },
    },
    exposeRoute: true,
  });

  fastify.register(require('@fastify/swagger-ui'), {
    routePrefix: '/documentation',
  });
}

/* Database */
fastify.register(require('@fastify/postgres'), {
  connectionString: `postgres://${process.env.POSTGRES_USER}:${process.env.POSTGRES_PASSWORD}@${process.env.POSTGRES_SERVICE}:${process.env.POSTGRES_PORT}/${process.env.POSTGRES_DB}`,
  ssl: {
    require: true, // This will help you. But you will see new error
    rejectUnauthorized: false, // This line will fix new error
  },
});

/* ------  [START] Awilix Definitions  ------ */
diContainer.register({
  server: asClass(server, {
    lifetime: Lifetime.SINGLETON,
  }).singleton(),
  fastify: asValue(fastify, {
    lifetime: Lifetime.SINGLETON,
  }),
  bcrypt: asValue(bcrypt, {
    lifetime: Lifetime.SINGLETON,
  }),
  OTPAuth: asValue(OTPAuth, {
    lifetime: Lifetime.SINGLETON,
  }),
  uuid: asValue(uuidv7, {
    lifetime: Lifetime.SINGLETON,
  }),
  handlebars: asValue(Handlebars, {
    lifetime: Lifetime.SINGLETON,
  }),
});

/* Load Modules */
diContainer.loadModules(
  [
    'src/app/handlers/controllers/**/*.js',
    'src/infra/repositories/**/*.js',
    'src/app/services/**/*.js',
  ],
  {
    formatName: 'camelCase',
    resolverOptions: {
      lifetime: Lifetime.SINGLETON,
      register: asClass,
    },
  },
);

/* Inject Utils */
diContainer.register({
  utils: asValue(utils, {
    lifetime: Lifetime.SINGLETON,
  }),
  constants: asValue(constants, {
    lifetime: Lifetime.SINGLETON,
  }),
  entities: asValue(entities, {
    lifetime: Lifetime.SINGLETON,
  }),
  db: asValue(database, {
    lifetime: Lifetime.SINGLETON,
  }),
});

/* Inject Custom Errors */
diContainer.register({
  clientErrors: asValue(clientErrors),
  serverErrors: asValue(serverErrors),
  userErrors: asValue(userErrors),
  InfraError: asValue(infraError),
});

// Register Handlebars helpers
registerHelpers();
/* ------  [END] Awilix Definitions  ------ */

/* Resolve awilix -> diScope  */
fastify.decorate('diScope', diContainer.createScope());

/* Decorate Middlewares */
fastify.decorate('authenticate', middlewares.authenticate);
fastify.decorate('authorize', middlewares.authorize);
fastify.decorate('verifyOTPToken', middlewares.verifyOTPToken);
fastify.decorate('verifyPassToken', middlewares.verifyPassToken);
fastify.decorate('verifyRefreshToken', middlewares.verifyRefreshToken);
fastify.decorate('uploadFile', middlewares.uploadFile);
fastify.decorate('uploadLimit', middlewares.uploadLimit);
fastify.decorate('auditLogs', middlewares.auditLogs);

diContainer.register({
  puppeteerBrowser: asValue(
    {
      _instance: null,
      async getInstance() {
        if (!this._instance) {
          // Always use Chromium in Docker containers
          let puppeteerOptions = process.env.NODE_ENV === 'local' 
            ? {
                channel: 'chrome',
                headless: 'shell',
              }
            : {
                executablePath: process.env.CHROME_PATH || '/usr/bin/chromium',
                headless: 'shell',
              };

          this._instance = await puppeteer.launch({
            protocolTimeout: process.env.PUPPETEER_PROTOCOL_TIMEOUT || 30000,
            args: [
              '--no-sandbox',
              '--disable-setuid-sandbox',
              '--disable-dev-shm-usage',
              '--disable-gpu',
              '--disable-software-rasterizer',
              '--disable-extensions',
              '--no-first-run',
              '--no-zygote',
            ],
            ignoreDefaultArgs: ['--disable-extensions'],
            ...puppeteerOptions,
          });
        }
        return this._instance;
      },
      async close() {
        if (this._instance) {
          await this._instance.close();
          this._instance = null;
        }
      },
      async newPage() {
        const browser = await this.getInstance();
        return browser.newPage();
      },
    },
    {
      lifetime: Lifetime.SINGLETON,
    },
  ),
});

fastify.addHook('onClose', async (instance) => {
  const browser = diContainer.resolve('puppeteerBrowser');
  if (browser) {
    await browser.close();
  }
});

/* Register Routes */
fastify.register(interfaceRoutes);

module.exports = diContainer;
