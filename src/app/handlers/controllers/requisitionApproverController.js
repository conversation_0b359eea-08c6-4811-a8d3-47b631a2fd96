const { where } = require('sequelize');

class RequisitionApprover {
  constructor(container) {
    const {
      db,
      constants,
      userService,
      requisitionRepository,
      projectApprovalRepository,
      requisitionApproverRepository,
      requisitionApproverService,
      notificationService,
      userRepository,
      fastify,
      clientErrors,
    } = container;

    this.db = db;
    this.requisitionRepository = requisitionRepository;
    this.projectApprovalRepository = projectApprovalRepository;
    this.Sequelize = db.Sequelize;
    this.userConstants = constants.user;
    this.userService = userService;
    this.requisitionApproverRepository = requisitionApproverRepository;
    this.requisitionApproverService = requisitionApproverService;
    this.notificationService = notificationService;
    this.constants = constants;
    this.userRepository = userRepository;
    this.fastify = fastify;
    this.clientErrors = clientErrors;
  }

  async getRequisitionApprovers(request, reply) {
    const { APPROVERS } = this.userConstants;
    const { requisitionId } = request.params;

    const requisition = await this.requisitionApproverRepository.findAll({
      where: {
        requisitionId,
      },
    });

    if (!requisition) {
      return reply.status(404).send({ message: 'Requisition not found' });
    }

    const existingRSApprover = requisition.data.map(
      (requisition) => requisition.approverId,
    );

    const approvers = await this.userService.getUsersByRoleName({
      roleNames: Object.values(APPROVERS),
      paranoid: true,
      where: {
        id: {
          [this.Sequelize.Op.notIn]: existingRSApprover,
        },
      },
      attributes: [
        'id',
        [
          this.Sequelize.fn(
            'CONCAT',
            this.Sequelize.col('first_name'),
            ' ',
            this.Sequelize.col('last_name'),
          ),
          'fullName',
        ],
      ],
    });

    return reply.status(200).send({ ...approvers });
  }

  /**
   * Add additional approver to the requisition
   *
   * TODO: add checking if the approver is allowed to be added in the RS appover
   *
   * @param {Object} request - The request object containing requisitionId and user information.
   * @param {Object} reply - The reply object used to send the response.
   */
  async addRequisitionApprovers(request, reply) {
    const { NOTIFICATION_TYPES } = this.constants.notification;
    const { REQUISITION_STATUS } = this.constants.requisition;
    const { requisitionId } = request.params;
    const { approverId } = request.body;
    const { userFromToken } = request;

    const requisition = await this.requisitionRepository.getById(requisitionId);

    if (!requisition) {
      return reply.status(404).send({ message: 'Requisition not found' });
    }

    if (requisition.status === REQUISITION_STATUS.REJECTED) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Adding approvers is not allowed for rejected requisition',
      });
    }

    const approver = await this.userRepository.getById(approverId);

    if (!approver) {
      return reply.status(404).send({ message: 'Approver not found' });
    }

    const existingApprover = await this.requisitionApproverRepository.findOne({
      where: {
        requisitionId,
        approverId: approverId,
      },
    });

    if (existingApprover) {
      return reply.status(400).send({ message: 'Approver already exists' });
    }

    const transaction = await this.db.sequelize.transaction();
    try {
      const rsToApprove = await this.requisitionApproverRepository.findOne({
        where: {
          requisitionId,
          approverId: userFromToken.id,
          status: 'pending',
        },
      });

      await this.requisitionApproverService.addAdditionalApprover({
        approverId,
        requisitionId,
        transaction,
        approverLevel: String(rsToApprove.level),
        userId: userFromToken.id,
        rsToApprove,
      });
      await this.notificationService.sendAdditionalApproverNotification({
        transaction,
        userFromToken,
        approverId,
        requisitionId,
      });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }

    return reply.status(200).send({ message: 'Approver added successfully' });
  }

  async editRequisitionApprovers(request, reply) {
    this.fastify.log.info(`Editing Approvers...`);
    const { userFromToken } = request;
    const { requisitionId, id } = request.params;

    this.fastify.log.info(`Validating Requisition`);

    const requisition = await this.requisitionApproverRepository.findAll({
      where: {
        requisitionId,
      },
    });

    if (!requisition) {
      return reply
        .status(404)
        .send({ message: `Requisition of ID: ${requisitionId} not found` });
    }

    if (
      !this.requisitionApproverService.isDefaultApprover(
        requisition.data,
        userFromToken,
      )
    ) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'You are not allowed to edit this requisition',
      });
    }

    await this.requisitionApproverService.editApprovers(request);

    const allApproved =
      await this.requisitionApproverRepository.checkIfAllApprovedForRequisition(
        requisitionId,
      );

    if (allApproved) {
      const { REQUISITION_STATUS } = this.constants.requisition;
      await this.requisitionRepository.update(
        { id: requisitionId },
        { status: REQUISITION_STATUS.ASSIGNING },
        { transaction },
      );
    }

    return reply
      .status(200)
      .send({ message: `Successfully Updated Approver Record of ${id}` });
  }
}

module.exports = RequisitionApprover;
