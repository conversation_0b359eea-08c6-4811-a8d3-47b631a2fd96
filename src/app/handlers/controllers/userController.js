class User {
  constructor (container) {
    const {
      db,
      utils,
      entities,
      constants,
      userService,
      roleService,
      clientErrors,
      userRepository,
      canvassService,
      roleRepository,
      departmentService,
      rsPaymentRequestService,
    } = container;

    this.db = db;
    this.utils = utils;
    this.entities = entities;
    this.roleService = roleService;
    this.userService = userService;
    this.clientErrors = clientErrors;
    this.userConstants = constants.user;
    this.canvassService = canvassService;
    this.userRepository = userRepository;
    this.roleRepository = roleRepository;
    this.departmentService = departmentService;
    this.rsPaymentRequestService = rsPaymentRequestService;
  }

  async getUserList(request, reply) {
    const { userFromToken } = request;
    const requesterRole = userFromToken.role.name;

    const userList = await this.userService.getUserList(
      userFromToken.id,
      requesterRole,
      request.query,
    );

    return reply.status(200).send(userList);
  }

  async getUserRoles(request, reply) {
    const { userFromToken } = request;
    const roleList = await this.roleService.getRolesByUserType(
      userFromToken.role.name,
    );

    return reply.status(200).send(roleList);
  }

  async getUserByRole(request, reply) {
    const { roleName } = request;
    const { excludeIds } = request.query;
    const { excludeIdsSchema } = this.entities.user;
    const { USER_TYPES } = this.userConstants;
    const userTypeValues = Object.values(USER_TYPES);
    const excludeIdQuery = excludeIdsSchema.parse(excludeIds);

    const isRoleNameValid = Array.isArray(roleName)
      ? roleName.every((role) => userTypeValues.includes(role))
      : userTypeValues.includes(roleName);

    if (!isRoleNameValid) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Invalid role name - ${Array.isArray(roleName) ? roleName.join(', ') : roleName
          }`,
      });
    }

    const users = await this.userService.getUsersByRoleName({
      where: { id: { [this.db.Sequelize.Op.notIn]: excludeIdQuery } },
      roleNames: roleName,
    });

    return reply.status(200).send(users);
  }

  async createUser(request, reply) {
    const { USER_TYPES } = this.userConstants;
    const { supervisorId, departmentId } = request.body;
    const isRootUser =
      request.userFromToken?.role?.name === USER_TYPES.ROOT_USER;

    if (!isRootUser && !departmentId) {
      throw this.clientErrors.VALIDATION_ERROR({
        message: 'Department ID is required',
      });
    }

    if (supervisorId) {
      await this.userService.getExistingUser(supervisorId, {
        role: USER_TYPES.SUPERVISOR,
      });
    }

    const transaction = await this.db.sequelize.transaction();

    try {
      const createdUser = await this.userService.createUser(
        request.body,
        transaction,
      );

      /* Cascade Approver to Canvass */
      await this.canvassService.cascadeRoleApprover({
        transaction,
        userId: createdUser.id,
        roleId: createdUser.roleId,
      });

      /* Cascade Approver to PR */
      await this.rsPaymentRequestService.cascadeRoleApprover({
        transaction,
        userId: createdUser.id,
        roleId: createdUser.roleId,
      });

      await transaction.commit();
      return reply.status(201).send(createdUser);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async updateUser(request, reply) {
    const userId = parseInt(request.params.id);
    const { supervisorId } = request.body;
    const { USER_TYPES } = this.userConstants;

    if (supervisorId) {
      await this.userService.getExistingUser(supervisorId, {
        role: USER_TYPES.SUPERVISOR,
      });
    }

    const transaction = await this.db.sequelize.transaction();
    const previousValue = await this.userService.getUserDetails(
      userId,
      request.userFromToken,
    );

    try {
      const updatedUser = await this.userService.updateUser(
        userId,
        request.userFromToken,
        request.body,
        transaction,
      );

      if (updatedUser.supervisorId) {
        await this.canvassService.cascadeSupervisorId({
          userId,
          transaction,
          supervisorId: updatedUser.supervisorId,
        });

        await this.rsPaymentRequestService.cascadeSupervisorId({
          userId,
          transaction,
          supervisorId: updatedUser.supervisorId,
        });
      }

      if (updatedUser.roleId) {
        /* Cascade Approver to Canvass */
        await this.canvassService.cascadeRoleApprover({
          userId,
          transaction,
          roleId: updatedUser.roleId,
        });

        /* Cascade Approver to PR */
        await this.rsPaymentRequestService.cascadeRoleApprover({
          userId,
          transaction,
          roleId: updatedUser.roleId,
        });
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }

    return reply.status(200).send({
      message: 'User status updated successfully',
      previousValue: {
        ...previousValue,
        departmentId: previousValue?.department?.id,
        roleId: previousValue?.role?.id,
      },
    });
  }

  async updatePassword(request, reply) {
    const { userFromToken } = request;
    const { password: newPassword } = request.body;

    const { password } = await this.userService.getUserDetails(
      request.params.id,
      request.userFromToken,
    );

    await this.userService.updatePassword(
      userFromToken.id,
      userFromToken.password,
      newPassword,
    );

    return reply.status(200).send({
      message: 'Password updated successfully',
      previousValue: password,
    });
  }

  async resetPassword(request, reply) {
    const { userId } = request.body;
    const { userFromToken } = request;

    const defaultPassword = await this.userService.resetPassword(
      userId,
      userFromToken,
    );

    return reply.status(200).send({
      message: 'Password updated successfully',
      tempPass: defaultPassword,
    });
  }

  async getUserDetails(request, reply) {
    const userDetails = await this.userService.getUserDetails(
      request.params.id,
      request.userFromToken,
    );

    return reply.status(200).send(userDetails);
  }
}

module.exports = User;
