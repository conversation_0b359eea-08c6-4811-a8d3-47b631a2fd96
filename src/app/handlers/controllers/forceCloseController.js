class ForceCloseController {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      constants,
      clientErrors,
      serverErrors,
      fastify,
      forceCloseService,
    } = container;

    this.db = db;
    this.utils = utils;
    this.entities = entities;
    this.constants = constants;
    this.clientErrors = clientErrors;
    this.serverErrors = serverErrors;
    this.fastify = fastify;
    this.forceCloseService = forceCloseService;
    this.forceCloseEntity = entities.forceClose;
  }

  /**
   * Force close a requisition slip
   * Handles all 3 force close scenarios from requirements document:
   * 1. Active Purchase Orders with Partial Deliveries
   * 2. All Purchase Orders Closed/Cancelled with Remaining Quantities
   * 3. Closed Purchase Orders with Pending Canvass Sheet Approvals
   */
  async forceCloseRequisition(request, reply) {
    this.fastify.log.info(`Initiating Force Close for Requisition ID: ${request.params.requisitionId}`);

    const { userFromToken, body, params } = request;
    const { requisitionId } = params;
    const { notes } = body;

    // Start database transaction for data consistency
    const transaction = await this.db.sequelize.transaction();

    try {
      // Validate force close request
      const validationResult = await this.forceCloseService.validateForceCloseEligibility({
        requisitionId,
        userFromToken,
        notes,
      });

      if (!validationResult.isEligible) {
        await transaction.rollback();
        throw this.clientErrors.BAD_REQUEST({
          message: validationResult.reason || 'Requisition is not eligible for force close',
          details: validationResult.details,
        });
      }

      // Execute force close workflow
      const forceCloseResult = await this.forceCloseService.executeForceClose({
        requisitionId,
        userFromToken,
        notes,
        scenario: validationResult.scenario,
        transaction,
      });

      await transaction.commit();

      this.fastify.log.info(`Force Close completed successfully for Requisition ID: ${requisitionId}`);

      return reply.status(200).send({
        message: 'Requisition force closed successfully',
        requisitionId,
        scenario: validationResult.scenario,
        details: forceCloseResult,
      });

    } catch (error) {
      // Only rollback if transaction is still active
      if (transaction && !transaction.finished) {
        await transaction.rollback();
      }

      this.fastify.log.error(`Force Close failed for Requisition ID: ${requisitionId} - ${error.message}`);

      // Re-throw client errors as-is
      if (error.status && error.status < 500) {
        throw error;
      }

      // Handle unexpected server errors
      throw this.serverErrors.INTERNAL_SERVER_ERROR({
        message: 'Force close operation failed',
        details: error.message,
      });
    }
  }

  /**
   * Check if a requisition is eligible for force close
   * Returns eligibility status and scenario information
   */
  async checkForceCloseEligibility(request, reply) {
    this.fastify.log.info(`Checking Force Close eligibility for Requisition ID: ${request.params.requisitionId}`);

    const { userFromToken, params } = request;
    const { requisitionId } = params;

    try {
      console.log(`🚀 CONTROLLER: About to call validateForceCloseEligibility for RS: ${requisitionId}`);
      const eligibilityResult = await this.forceCloseService.validateForceCloseEligibility({
        requisitionId,
        userFromToken,
        checkOnly: true, // Only validate, don't prepare for execution
      });
      console.log(`🚀 CONTROLLER: validateForceCloseEligibility completed for RS: ${requisitionId}`, {
        isEligible: eligibilityResult?.isEligible,
        scenario: eligibilityResult?.scenario
      });

      this.fastify.log.info(`Force Close eligibility check completed for Requisition ID: ${requisitionId}`);

      return reply.status(200).send({
        requisitionId,
        isEligible: eligibilityResult.isEligible,
        scenario: eligibilityResult.scenario,
        reason: eligibilityResult.reason,
        details: eligibilityResult.details,
        buttonVisible: eligibilityResult.buttonVisible,
        impactSummary: eligibilityResult.impactSummary || [],
        validationPath: eligibilityResult.validationPath,
      });

    } catch (error) {
      this.fastify.log.error(`Force Close eligibility check failed for Requisition ID: ${requisitionId} - ${error.message}`);

      // Handle client errors
      if (error.status && error.status < 500) {
        throw error;
      }

      // Handle unexpected server errors
      throw this.serverErrors.INTERNAL_SERVER_ERROR({
        message: 'Failed to check force close eligibility',
        details: error.message,
      });
    }
  }

  /**
   * Get force close history for a requisition
   * Returns audit trail and history information
   */
  async getForceCloseHistory(request, reply) {
    this.fastify.log.info(`Getting Force Close history for Requisition ID: ${request.params.requisitionId}`);

    const { userFromToken, params } = request;
    const { requisitionId } = params;

    try {
      const historyResult = await this.forceCloseService.getForceCloseHistory({
        requisitionId,
        userFromToken,
      });

      this.fastify.log.info(`Force Close history retrieved for Requisition ID: ${requisitionId}`);

      return reply.status(200).send({
        requisitionId,
        forceCloseLog: historyResult.forceCloseLog,
        systemChanges: historyResult.systemChanges,
        auditTrail: historyResult.auditTrail,
        impactedDocuments: historyResult.impactedDocuments,
      });

    } catch (error) {
      this.fastify.log.error(`Force Close history retrieval failed for Requisition ID: ${requisitionId} - ${error.message}`);

      // Handle client errors
      if (error.status && error.status < 500) {
        throw error;
      }

      // Handle unexpected server errors
      throw this.serverErrors.INTERNAL_SERVER_ERROR({
        message: 'Failed to retrieve force close history',
        details: error.message,
      });
    }
  }
}

module.exports = ForceCloseController;
