const {
  PAGINATION_DEFAULTS,
} = require('../../../domain/constants/deliveryReceiptConstants');

class DeliveryReceiptItemController {
  constructor({ deliveryReceiptItemService, clientErrors }) {
    this.deliveryReceiptItemService = deliveryReceiptItemService;
    this.clientErrors = clientErrors;
  }

  async getItemHistory(request, reply) {
    const historyRecords =
      await this.deliveryReceiptItemService.getItemHistory(
        request.params.id,
        {
          page: request.query.page || PAGINATION_DEFAULTS.PAGE,
          limit: request.query.limit || PAGINATION_DEFAULTS.LIMIT,
        },
      );

    return reply.status(200).send({ ...historyRecords });
  }

  async updateItem(request, reply) {
    const { body, params, userFromToken } = request;
    const deliveryReceiptItem = await this.deliveryReceiptItemService.updateItem(
      params.id,
      body,
      userFromToken
    );

    if (!deliveryReceiptItem) {
      throw this.clientErrors.NOT_FOUND({
        message: `Delivery receipt item with id of ${request.params.id} not found`,
      });
    }

    return reply.status(200).send({ ...deliveryReceiptItem });
  }

  async cancelReturns(request, reply) {
    const { params, userFromToken } = request;
    const isReturnCancelled = await this.deliveryReceiptItemService.cancelReturns(
      params.id,
      userFromToken
    );

    if (isReturnCancelled) {
      return reply.status(200).send({
        message: 'Returns cancelled successfully',
      });
    }

    throw this.clientErrors.BAD_REQUEST({
      message: 'Failed to cancel returns.',
    });
  }
}

module.exports = DeliveryReceiptItemController;
