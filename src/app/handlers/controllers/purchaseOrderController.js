const fs = require('fs');

class Department {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      constants,
      clientErrors,
      purchaseOrderService,
      warrantyService,
      purchaseOrderItemService,
      notificationService,
      noteService,
      userService,
      requisitionItemListRepository,
      purchaseOrderCancelledItemsRepository,
      canvassItemRepository,
      templateService,
      invoiceReportService,
      rsPaymentRequestService,
    } = container;

    this.db = db;
    this.utils = utils;
    this.entities = entities;
    this.constants = constants;
    this.clientErrors = clientErrors;
    this.purchaseOrderService = purchaseOrderService;
    this.rsPaymentRequestService = rsPaymentRequestService;
    this.warrantyService = warrantyService;
    this.purchaseOrderItemService = purchaseOrderItemService;
    this.notificationService = notificationService;
    this.noteService = noteService;
    this.userService = userService;
    this.requisitionItemListRepository = requisitionItemListRepository;
    this.purchaseOrderCancelledItemsRepository =
      purchaseOrderCancelledItemsRepository;
    this.canvassItemRepository = canvassItemRepository;
    this.templateService = templateService;
    this.invoiceReportService = invoiceReportService;
  }

  async getAllPurchaseOrders(request, reply) {
    const purchaseOrderList =
      await this.purchaseOrderService.getAllPurchaseOrders({
        ...request.query,
        ...request.params,
      });
    return reply.status(200).send(purchaseOrderList);
  }

  async getPurchaseOrderApprovers(request, reply) {
    const { purchaseOrderId } = request.params;
    const existingPurchaseOrder =
      await this.purchaseOrderService.getExistingPurchaseOrder(
        parseInt(purchaseOrderId),
      );

    const purchaseOrderApprovers =
      await this.purchaseOrderService.getPurchaseOrderApprovers(
        existingPurchaseOrder.id,
      );

    return reply.status(200).send(purchaseOrderApprovers);
  }
  async getPurchaseOrderAssignee(request, reply) {
    const { purchaseOrderId } = request.params;
    const existingPurchaseOrder =
      await this.purchaseOrderService.getExistingPurchaseOrder(
        parseInt(purchaseOrderId),
      );

    const getPurchaseOrderAssignee =
      await this.purchaseOrderService.getPurchaseOrderAssignee(
        existingPurchaseOrder.id,
      );

    return reply.status(200).send(getPurchaseOrderAssignee);
  }

  async getPOById(request, reply) {
    const { purchaseOrderId } = request.params;
    const purchaseOrder =
      await this.purchaseOrderService.getPODetails(purchaseOrderId);

    if (!purchaseOrder) {
      return reply.status(404).send({
        message: 'Purchase order not found',
      });
    }
    return reply.status(200).send(purchaseOrder);
  }

  async getWarranties(request, reply) {
    const warranties = await this.warrantyService.getWarranties(
      request.query.search,
    );
    return reply.status(200).send(warranties);
  }

  async createWarranty(request, reply) {
    const warranty = await this.warrantyService.createWarranty(request.body);
    return reply.status(200).send(warranty);
  }

  async getPOItemsById(request, reply) {
    const poItems = await this.purchaseOrderItemService.getPOItemsById({
      ...request.params,
      ...request.query,
    });
    return reply.status(200).send(poItems);
  }

  async updatePurchaseOrder(request, reply) {
    const { purchaseOrderId } = request.params;
    const { body } = request;

    const purchaseOrder = await this.purchaseOrderService.updatePurchaseOrder({
      purchaseOrderId,
      body,
    });

    if (!purchaseOrder) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Failed to update purchase order item',
      });
    }

    return reply.status(200).send({
      message: 'Successfully updated purchase order item',
    });
  }

  async submitPurchaseOrder(request, reply) {
    const { userFromToken, transaction } = request;
    const { purchaseOrderId } = request.params;
    const purchaseOrder = await this.purchaseOrderService.submitPurchaseOrder({
      purchaseOrderId,
      userFromToken,
      warrantyId: request.body?.warrantyId || null,
      isNewDeliveryAddress: request.body?.isNewDeliveryAddress || false,
      newDeliveryAddress: request.body?.newDeliveryAddress || null,
      addedDiscount: request.body?.addedDiscount,
      isAddedDiscountFixedAmount:
        request.body?.isAddedDiscountFixedAmount || false,
      isAddedDiscountPercentage:
        request.body?.isAddedDiscountPercentage || false,
    }, transaction);

    if (purchaseOrder[0] === 0) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Failed to submit purchase order',
      });
    }

    return reply.status(200).send({
      message: 'Successfully submitted purchase order',
    });
  }

  async cancelPurchaseOrder(request, reply) {
    const { purchaseOrderId } = request.params;

    const { userFromToken, transaction } = request;

    const assignee =
      await this.purchaseOrderService.getPurchaseOrderAssignee(purchaseOrderId);
    const isUserAssignee = assignee.data.approver.id === userFromToken.id;

    if (!isUserAssignee) {
      throw this.clientErrors.BAD_REQUEST({
        message: `You are not allowed to cancel this purchase order`,
      });
    }

    await this.purchaseOrderService.cancelPurchaseOrder(
      parseInt(purchaseOrderId),
      transaction,
    );

    return reply.status(200).send({
      message: 'Purchase order cancelled successfully',
    });
  }

  async approvePurchaseOrder(request, reply) {
    const { userFromToken } = request;
    const { approveReason } = request.body || {};
    const { purchaseOrderId } = request.params;
    const { MODELS, USER_TYPES, COMMENT_TYPES } = this.constants.note;

    const transaction = await this.db.sequelize.transaction();
    try {
      const existingPurchaseOrder =
        await this.purchaseOrderService.getExistingPurchaseOrder(
          parseInt(purchaseOrderId),
        );

      const isAllApproved =
        await this.purchaseOrderService.approvePurchaseOrder({
          transaction,
          existingPurchaseOrder,
          approver: userFromToken,
        });

      if (isAllApproved) {
        // for delivery
        // TODO: create delivery receipt
      }
      if (approveReason) {
        await this.noteService.createNote(
          {
            model: MODELS.PURCHASE_ORDER,
            modelId: existingPurchaseOrder.id,
            userName: userFromToken.fullNameUser,
            userType: USER_TYPES.APPROVER,
            commentType: COMMENT_TYPES.APPROVAL,
            note: approveReason,
          },
          {
            transaction,
          },
        );
      }

      await transaction.commit();

      return reply.status(200).send({
        message: 'Purchase order successfully approved',
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async rejectPurchaseOrder(request, reply) {
    const { userFromToken, transaction } = request;
    const { rejectReason } = request.body;
    const { purchaseOrderId } = request.params;
    const { MODELS, USER_TYPES, COMMENT_TYPES } = this.constants.note;
    const { NOTIFICATION_TYPES } = this.constants.notification;

    const existingPurchaseOrder =
      await this.purchaseOrderService.getExistingPurchaseOrder(
        parseInt(purchaseOrderId),
      );

    const { data: existingAssignedPurchasing } =
      await this.purchaseOrderService.getPurchaseOrderAssignee(
        existingPurchaseOrder.id,
      );

    await this.purchaseOrderService.rejectPurchaseOrder({
      transaction,
      existingPurchaseOrder,
      approverId: userFromToken.id,
    });

      await this.noteService.createNote({
        model: MODELS.PURCHASE_ORDER,
        modelId: existingPurchaseOrder.id,
        userName: userFromToken.fullNameUser,
        userType: USER_TYPES.APPROVER,
        commentType: COMMENT_TYPES.DISAPPROVAL,
        note: rejectReason,
      });
      await this.notificationService.sendNotification({
        transaction,
        senderId: userFromToken.id,
        type: NOTIFICATION_TYPES.PURCHASE_ORDER,
        title: `PO-${existingPurchaseOrder?.canvassRequisition?.requisition?.companyCode}${existingPurchaseOrder.poLetter}${existingPurchaseOrder.poNumber} has been rejected`,
        message: `Requisition Slip with a Purchase Order Number of PO-${existingPurchaseOrder?.canvassRequisition.requisition?.companyCode}${existingPurchaseOrder.poLetter}${existingPurchaseOrder.poNumber} has been rejected by one of the Approvers. Click here or through Dashboard to review`,
        recipientUserIds: [
          existingPurchaseOrder.canvassRequisition.requisition.createdBy,
          existingAssignedPurchasing.approver.id,
        ],
        metaData: {
          addedBy: userFromToken.id,
          rejectReason: rejectReason,
          canvassId: existingPurchaseOrder.canvassRequisitionId,
          requisitionId: existingPurchaseOrder.requisitionId,
          purchaseOrderId: existingPurchaseOrder.id,
        },
      });


    return reply.status(200).send({
      message: 'Purhcase Order has been rejected successfully.',
    });
  }

  async addPurchaseOrderAdhocApprover(request, reply) {
    const { userFromToken, transaction } = request;
    const { approverId } = request.body;
    const { purchaseOrderId } = request.params;
    const { USER_TYPES } = this.constants.user;
    const { NOTIFICATION_TYPES } = this.constants.notification;

    const existingPurchaseOrder =
      await this.purchaseOrderService.getExistingPurchaseOrder(
        parseInt(purchaseOrderId),
      );

    /* Ensure that user exist with valid role at first index */
    const approverRoleList = [
      USER_TYPES.PURCHASING_STAFF,
      USER_TYPES.ENGINEERS,
      USER_TYPES.SUPERVISOR,
      USER_TYPES.ASSISTANT_MANAGER,
      USER_TYPES.DEPARTMENT_HEAD,
      USER_TYPES.DEPARTMENT_SECRETARY,
      USER_TYPES.DEPARTMENT_DIVISION_HEAD,
      USER_TYPES.AREA_STAFF,
      USER_TYPES.PURCHASING_STAFF,
      USER_TYPES.PURCHASING_HEAD,
      USER_TYPES.MANAGEMENT,
    ];
    const users = await this.userService.validateMultipleUsers([approverId], {
      roleNames: approverRoleList,
    });

    await this.purchaseOrderService.addPurchaseOrderAdhocApprover({
      transaction,
      approver: users[0],
      creatorId: userFromToken.id,
      purchaseOrderId: existingPurchaseOrder.id,
      requisitionId: existingPurchaseOrder.requisitionId,
      defaultApproverName: userFromToken.fullNameUser,
    });

    await this.notificationService.sendNotification({
      transaction,
      senderId: userFromToken.id,
      type: NOTIFICATION_TYPES.CANVASS,
      title: 'Assigned as an Additional Approver',
      message: `${userFromToken.firstName} ${userFromToken.lastName} has added you to review the Requisition Slip and have it Approved. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.`,
      recipientUserIds: [approverId],
      metaData: {
        addedBy: userFromToken.id,
        adhocApprover: approverId,
        canvassId: existingPurchaseOrder.canvassRequisitionId,
        requisitionId: existingPurchaseOrder.requisitionId,
        purchaseOrderId: existingPurchaseOrder.id,
      },
    });

    return reply.status(200).send({
      message: 'Purchase order adhoc approver was added successfully.',
    });
  }

  async removePurchaseOrderAdhocApprover(request, reply) {
    const { userFromToken } = request;
    const { purchaseOrderId } = request.params;
    const existingPurchaseOrder =
      await this.purchaseOrderService.getExistingPurchaseOrder(
        parseInt(purchaseOrderId),
      );

    await this.purchaseOrderService.removePurchaseOrderAdhocApprover({
      purchaseOrderId: existingPurchaseOrder.id,
      primaryApproverId: userFromToken.id,
    });

    return reply.status(200).send({
      message: 'Purchase order adhoc approver was successfully removed.',
    });
  }

  async getAllAllowedPOAdhocApprovers(request, reply) {
    const allowedPOAdhocApproversList =
      await this.purchaseOrderService.getAllAllowedPOAdhocApprovers();

    return reply.status(200).send(allowedPOAdhocApproversList);
  }

  async resubmitRejectedPurchaseOrder(request, reply) {
    const { purchaseOrderId } = request.params;

    const existingPurchaseOrder =
      await this.purchaseOrderService.getExistingPurchaseOrder(
        parseInt(purchaseOrderId),
      );

    await this.purchaseOrderService.resubmitRejectedPurchaseOrder({
      existingPurchaseOrder,
    });

    return reply.status(200).send({
      message: 'Rejected purchase order was successfully resubmitted.',
    });
  }

  async getPOListForDelivery(request, reply) {
    const { requisitionId } = request.params;
    const poList =
      await this.purchaseOrderService.getPOListForDelivery(requisitionId);
    return reply.status(200).send(poList);
  }

  async getPODetailsForDelivery(request, reply) {
    const { purchaseOrderId } = request.params;
    const purchaseOrder =
      await this.purchaseOrderService.getPODetailsForDelivery(
        parseInt(purchaseOrderId),
      );

    return reply.status(200).send(purchaseOrder);
  }

  async getCancelledItemsByPOIds(request, reply) {
    const { purchaseOrderIds } = request.query;

    const cancelledItems =
      await this.purchaseOrderCancelledItemsRepository.getAllCancelledItemsByPOIds(
        JSON.parse(purchaseOrderIds),
      );

    return reply.status(200).send(cancelledItems);
  }

  async closePurchaseOrder(request, reply) {
    const { id } = request.params;
    const { RS_PAYMENT_REQUEST_STATUS } = this.constants.rsPaymentRequest;

    try {
      const { rsPaymentRequest: existingPaymentRequest } =
        await this.rsPaymentRequestService.getRsPaymentRequestByPk(
          id,
        );

      if (
        existingPaymentRequest.status !== RS_PAYMENT_REQUEST_STATUS.APPROVED
      ) {
        return;
      }

      const result = await this.purchaseOrderService.closePurchaseOrder(
        parseInt(existingPaymentRequest?.purchaseOrderId),
      );

      return reply.status(200).send({
        message: 'Purchase order closed successfully',
        data: result,
      });
    } catch (error) {
      request.log.error(error);
      throw error;
    }
  }
  async generatePurchaseOrderPdf(request, reply) {
    const { id: purchaseOrderId } = request.params;

    try {
      // Get template data from service
      const { templateData, fileName } =
        await this.purchaseOrderService.generatePurchaseOrderPdf(
          purchaseOrderId,
        );

      // Check if templateService is available
      if (!this.templateService) {
        throw this.clientErrors.INTERNAL_SERVER_ERROR({
          message: 'Template service is not available',
        });
      }

      // Generate PDF using template service
      const result = await this.templateService.generateDynamicTemplate(
        templateData,
        'purchase-order.hbs', // Using the existing template
        'purchase_order_downloads',
        fileName,
      );

      // Return file stream
      return reply
        .header('Content-Type', 'application/pdf')
        .header(
          'Content-Disposition',
          `attachment; filename="${result.fileName}"`,
        )
        .send(fs.createReadStream(result.filePath));
    } catch (error) {
      request.log.error(error);
      throw error;
    }
  }

  async updatePurchaseOrderForDelivery(request, reply) {
    const { purchaseOrderId } = request.params;
    const { userFromToken, transaction } = request;

    await this.purchaseOrderService.updatePurchaseOrderForDelivery(
      purchaseOrderId,
      userFromToken.id,
      transaction,
    );


    return reply.status(200).send({
      message: 'Purchase order updated to "for delivery" successfully',
    });
  }

  async getInvoiceReportsFromPurchaseOrderId(request, reply) {
    const { purchaseOrderId } = request.params;
    const invoiceReports =
      await this.invoiceReportService.getInvoiceReportsFromPurchaseOrderId(
        purchaseOrderId,
        request.query.paymentRequestId,
      );

    return reply.status(200).send(invoiceReports);
  }

  async addAdditionalFees(request, reply) {
    const { purchaseOrderId } = request.params;
    const { userFromToken, transaction } = request;
    const { withholdingTaxDeduction, deliveryFee, tip, extraCharges } =
      request.body;

    await this.purchaseOrderService.addAdditionalFees(
      purchaseOrderId,
      { withholdingTaxDeduction, deliveryFee, tip, extraCharges },
      userFromToken,
      transaction,
    );

    return reply.status(200).send({
      message: 'Additional fees updated successfully.',
    });
  }
}

module.exports = Department;
