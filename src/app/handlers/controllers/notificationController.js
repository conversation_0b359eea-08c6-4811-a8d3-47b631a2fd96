class Notification {
  constructor (container) {
    const { notificationService } = container;

    this.notificationService = notificationService;
  }

  async getNotifications(request, reply) {
    const { userFromToken } = request;

    const notifications = await this.notificationService.getNotifications(
      userFromToken,
      request.query,
    );

    return reply.status(200).send(notifications);
  }

  async seenNotification(request, reply) {
    const { id } = request.params;
    const { userFromToken } = request;

    await this.notificationService.seenNotification(id, userFromToken);

    return reply.status(200).send({ message: 'Notification seen successfully' });
  }
}

module.exports = Notification;
