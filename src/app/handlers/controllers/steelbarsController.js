class SteelbarsController {
  constructor(container) {
    const {
      db,
      utils,
      steelbarsService,
      steelbarsRepository,
      clientErrors,
      entities,
    } = container;

    this.db = db;
    this.steelbarsService = steelbarsService;
    this.steelbarsRepository = steelbarsRepository;
    this.clientErrors = clientErrors;
    this.steelbarsEntity = entities.steelbars;
    this.utils = utils;
  }

  async getSteelbars(request, reply) {
    const steelbars = await this.steelbarsService.getAllSteelbars(
      request.query,
    );
    return reply.status(200).send(steelbars);
  }

  async updateOfmAcctCd(request, reply) {
    const { steelbarsId, ofmItemAcctCd } = request.body;
    await this.steelbarsService.updateOfmAcctCd(steelbarsId, ofmItemAcctCd);
    return reply.status(200).send({
      message: `Steelbars item with id ${steelbarsId} updated successfully`,
    });
  }

  async createSteelbar(request, reply) {
    const transaction = await this.db.sequelize.transaction();

    try {
      const { result, action } = await this.steelbarsService.createSteelbar(
        request.body,
        { transaction },
      );

      await transaction.commit();
      return reply.status(action === 'created' ? 201 : 200).send({
        message: `Steelbar ${action} successfully`,
        data: result,
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}

module.exports = SteelbarsController;
