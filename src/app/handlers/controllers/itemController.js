class ItemController {
  constructor (container) {
    const {
      db,
      utils,
      itemService,
      itemRepository,
      ofmItemListRepository,
      clientErrors,
      entities,
      constants,
      syncRepository,
      tradeRepository,
      requisitionItemListRepository,
    } = container;

    this.db = db;
    this.itemService = itemService;
    this.itemRepository = itemRepository;
    this.ofmItemListRepository = ofmItemListRepository;
    this.clientErrors = clientErrors;
    this.itemEntity = entities.item;
    this.tradeEntity = entities.trade;
    this.historyEntity = entities.history;
    this.syncRepository = syncRepository;
    this.tradeRepository = tradeRepository;
    this.utils = utils;
    this.constants = constants;
    this.requisitionItemListRepository = requisitionItemListRepository;
  }

  async syncItems(request, reply) {
    const lastSyncedAt = await this.itemService.syncItems(
      request.userFromToken,
    );
    return reply.status(200).send({ lastSyncedAt });
  }

  async getItems(request, reply) {
    let whereClause = {};
    const { sortBy, filterBy, ...queries } = request.query;
    const { itemSortSchema, itemFilterSchema } = this.itemEntity;

    const parsedSortBy = itemSortSchema.parse(sortBy);
    const orderClauses = parsedSortBy?.map(([field, direction]) => {
      if (field === 'trade') {
        return [this.db.Sequelize.literal('"trade.tradeName"'), direction];
      }

      return [field, direction];
    });

    const parsedFilterBy = itemFilterSchema.parse(filterBy);
    const { itmDes, itemCd, acctCd, trade, unit } =
      this.utils.buildFilterWhereClause(parsedFilterBy);

    if (itmDes) {
      whereClause.itmDes = { [this.db.Sequelize.Op.iLike]: `%${itmDes}%` };
    }

    if (itemCd) {
      whereClause.itemCd = { [this.db.Sequelize.Op.eq]: itemCd };
    }

    if (acctCd) {
      whereClause.acctCd = { [this.db.Sequelize.Op.eq]: acctCd };
    }

    if (trade) {
      whereClause['$trade.trade_name$'] = {
        [this.db.Sequelize.Op.eq]: trade,
      };
    }

    if (unit) {
      whereClause.unit = { [this.db.Sequelize.Op.eq]: unit };
    }

    const items = await this.itemRepository.getAllItems({
      ...queries,
      order: orderClauses,
      whereClause,
    });

    const itemSync = await this.syncRepository.findByModel('item');

    return reply.status(200).send({
      ...items,
      lastSyncedAt: itemSync?.lastSyncedAt || null,
    });
  }

  async getOfmItemListById(request, reply) {
    const ofmItemList = await this.itemService.getOfmItemListById(
      request.params.id,
    );

    return reply.status(200).send(ofmItemList);
  }

  async createNonOfmItem(request, reply) {
    const { userFromToken } = request;
    const { sortBy, ...payload } = request.body;

    const nonOfmItem = await this.itemService.createNonOfmItem({
      ...payload,
      userId: userFromToken.id,
    });

    return reply.status(201).send(nonOfmItem);
  }

  async getAllNonOfmItems(request, reply) {
    const nonOfmItems = await this.itemService.getAllNonOfmItems(request.query);

    return reply.status(200).send(nonOfmItems);
  }

  async getNonOfmItemById(request, reply) {
    const nonOfmItem = await this.itemService.getNonOfmItemById(
      request.params.id,
    );

    return reply.status(200).send(nonOfmItem);
  }

  async updateNonOfmItem(request, reply) {
    const { userFromToken } = request;
    const transaction = await this.db.sequelize.transaction();

    try {
      const previousValue = await this.itemService.getNonOfmItemById(
        request.params.id,
      );
      const updatedNonOfmItem = await this.itemService.updateNonOfmItem(
        request.params.id,
        {
          ...request.body,
          userId: userFromToken.id,
          dateModified: new Date(),
        },
      );

      await transaction.commit();
      return reply.status(200).send({ ...updatedNonOfmItem, previousValue });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async getOfmItemById(request, reply) {
    const { id } = request.params;
    const item = await this.itemService.getItemById(id);
    return reply.status(200).send(item);
  }

  async getAllOfmItemLists(request, reply) {
    const { sortBy, sort: sortDirection, filterBy, ...queries } = request.query;
    const { ofmItemListSortSchema, itemListsFilterSchema } = this.itemEntity;
    const { userFromToken } = request;

    const parsedSortBy = ofmItemListSortSchema.parse(sortBy);
    const orderClauses = parsedSortBy?.map(([field, direction]) => {

      if (['trade', 'company', 'project'].includes(field)) {
        let name;
        field === 'trade' ? (name = 'trade_name') : (name = 'name');
        return [this.db.Sequelize.literal(`${field}.${name}`), direction];
      }
      return [field, direction];
    });

    const parsedFilterBy = itemListsFilterSchema.parse(filterBy);

    const ofmItemLists = await this.itemService.getAllOfmItemLists({
      ...queries,
      order: orderClauses,
      filterBy: parsedFilterBy,
      userId: userFromToken.id,
    });

    return reply.status(200).send(ofmItemLists);
  }

  async getAllOfmListItems(request, reply) {
    try {
      const { sortBy, sort, ...queries } = request.query;
      const { ofmListItemSortSchema } = this.itemEntity;

      let orderBy = [['created_at', 'DESC']];
      if (sortBy && sort) {
        const parsedSortBy = ofmListItemSortSchema.parse(sortBy);
        orderBy = [[parsedSortBy, sort.toUpperCase()]];
      }

      const ofmListItems = await this.itemService.getAllOfmListItems({
        ...queries,
        order: orderBy,
      });

      return reply.status(200).send(ofmListItems);
    } catch (error) {
      console.error('Error in getAllOfmListItems:', error);
      throw error;
    }
  }

  async getAllTrades(request, reply) {
    const { sortBy, ...queries } = request.query;
    const { tradeSortSchema } = this.tradeEntity;

    const parsedSortBy = tradeSortSchema.parse(sortBy);

    const trades = await this.tradeRepository.getAllTrades({
      ...queries,
      order: parsedSortBy,
    });

    return reply.status(200).send(trades);
  }

  async getOfmItemsByListId(request, reply) {
    const { id } = request.params;
    const { sortBy, filterBy, ...queries } = request.query;
    const { ofmListItemSortSchema, itemFilterSchema } = this.itemEntity;

    const parsedSortBy = ofmListItemSortSchema.parse(sortBy);
    const parsedFilterBy = itemFilterSchema.parse(filterBy);
    const filterByWhereClause = this.utils.buildFilterWhereClause(parsedFilterBy);

    const ofmListItems = await this.ofmItemListRepository.getOfmItemsByListIdDetails(id, {
      ...queries,
      filterBy: filterByWhereClause,
      order: parsedSortBy,
    });

    return reply.status(200).send({
      ...ofmListItems,
    });
  }

  async itemRequisitionHistory(request, reply) {
    const { id } = request.params;

    const item = await this.itemRepository.getById(id);

    if (!item) {
      throw this.clientErrors.NOT_FOUND({ message: 'Item not found' });
    }

    const { data } = await this.itemService.getHistory(request);

    return reply.status(200).send({
      message: `Successfully Retrieved Item History`,
      result: data,
    });
  }

  async itemHistory(request, reply) {
    try {
      const { id } = request.params;
      const { sortBy, filterBy, ...queries } = request.query;
      const { historySortSchema, historyFilterSchema } = this.historyEntity;

      const filterByWhereClause = this.utils.buildFilterWhereClause(
        historyFilterSchema.parse(filterBy),
      );

      const items = await this.itemService.getItemHistory({
        ...queries,
        order: historySortSchema.parse(sortBy),
        filterBy: filterByWhereClause,
        id,
      });

      return reply.status(200).send(items);
    } catch (error) {
      console.error('Error in getAllOfmListItems:', error);
      throw error;
    }
  }

  async updateOfmItem(request, reply) {
    const { id } = request.params;
    const result = await this.itemService.updateOfmItem(id, request.body);

    const transaction = await this.db.sequelize.transaction();

    try {
      const result = await this.itemService.updateOfmItem(id, request.body);

      await this.requisitionItemListRepository.update(
        { itemId: id },
        request.body,
      );

      await transaction.commit();
      return reply.status(200).send({
        message: `Item with id ${id} updated successfully`,
        data: result,
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async bulkUpdateOfmItems(request, reply) {
    const result = await this.itemService.bulkUpdateOfmItems(request.body);
    return reply.status(200).send({
      message: `${result.length} items updated successfully`,
      data: result,
    });
  }

  async getOfmUniqueUnits(request, reply) {
    const { REQUEST_TYPES } = this.constants.item;
    const { OFM } = REQUEST_TYPES;

    const units = await this.itemService.getItemsUniqueUnits({ type: OFM });

    return reply.status(200).send(units);
  }

  async getNonOfmUniqueUnits(request, reply) {
    const { REQUEST_TYPES } = this.constants.item;
    const { NON_OFM } = REQUEST_TYPES;

    const units = await this.itemService.getItemsUniqueUnits({ type: NON_OFM });
    return reply.status(200).send(units);
  }

  async itemPurchaseHistory(type, request, reply) {
    try {
      const { id } = request.params;
      const { sortBy, filterBy, ...queries } = request.query;
      const { purchaseHistorySortSchema } = this.historyEntity;

      const purchaseHistory = await this.itemService.getItemPurchaseHistory({
        ...queries,
        order: purchaseHistorySortSchema.parse(sortBy),
        id,
        type,
      });

      return reply.status(200).send(purchaseHistory);
    } catch (error) {
      console.error(`Error in ${type} itemPurchaseHistory:`, error);
      throw error;
    }
  }
}

module.exports = ItemController;
