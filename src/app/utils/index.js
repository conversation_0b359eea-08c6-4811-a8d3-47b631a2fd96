const { z } = require('zod');
const Crypto = require('crypto');
const Sequelize = require('sequelize');
const HTTPClient = require('./httpClient');
const clientErrors = require('../errors/clientErrors');
const addAuditHooks = require('./auditHooks');
const Terms = require('./terms');
const fs = require('fs/promises');

/**
 * Compile schema validator using Zod
 * (validatorCompiler from fastify route option)
 * @param {Zod schema} schema
 * @returns {object}
 */
const validatorCompiler = (schema) => {
  return (data) => {
    const result = schema.safeParse(data);

    if (!result?.success) {
      const errorDescription = result?.error?.issues?.reduce(
        (issues, current) => {
          issues[current.path[0] ?? 'error'] = current.message;
          return issues;
        },
        {},
      );

      throw clientErrors.VALIDATION_ERROR({
        message: result?.error?.issues[0]?.message,
        description: JSON.stringify(errorDescription),
      });
    }

    return data;
  };
};

/**
 * Parses a domain and payload using a Zod schema.
 * Validates the payload against the schema and throws a validation error if it fails.
 *
 * @param {Object} domain - The Zod schema to validate against.
 * @param {Object} payload - The data to be validated.
 * @returns {Object} - The validated data if successful.
 * @throws {Error} - Throws a validation error if the payload does not conform to the schema.
 */
const parseDomain = function (domain, payload) {
  const parsedData = domain.safeParse(payload);

  if (!parsedData.success) {
    const errorDescription = parsedData?.error.issues.reduce(
      (issues, current) => {
        issues[current.path[0] ?? 'error'] = current.message;
        return issues;
      },
      {},
    );

    throw clientErrors.VALIDATION_ERROR({
      message: parsedData?.error?.issues[0]?.message,
      description: JSON.stringify(errorDescription),
    });
  }

  return parsedData.data;
};

/**
 * Generate secret key using crypto and uuid
 * @param {string} secretWord
 * @returns {string}
 */
const generateSecret = (secretWord = '') => {
  const randomNumber = Math.floor(Math.random() * 10000);
  const cryptoSecret = Crypto.createHash('SHA256')
    .update(new Date().getTime() + secretWord)
    .digest('base64');

  return `${randomNumber}#${cryptoSecret}`;
};

/**
 * Encrypts a given text using AES-256-CBC encryption.
 * The function generates a random initialization vector (IV) and combines it with the encrypted text.
 * The output format is "iv:encryptedText".
 *
 * @param {string} text - The plaintext string to be encrypted.
 * @returns {string} - The encrypted text in the format "iv:encryptedText".
 * @throws {Error} - Throws an error if the encryption process fails.
 */
const encrypt = (text) => {
  const iv = Crypto.randomBytes(16);
  const cipher = Crypto.createCipheriv(
    'aes-256-cbc',
    Buffer.from(process.env.OTP_KEY),
    iv,
  );
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return iv.toString('hex') + ':' + encrypted;
};

/**
 * Decrypts a given encrypted text using AES-256-CBC decryption.
 * The function expects the input format to be "iv:encryptedText" and extracts the IV for decryption.
 *
 * @param {string} text - The encrypted text in the format "iv:encryptedText".
 * @returns {string} - The decrypted plaintext string.
 * @throws {Error} - Throws an error if the decryption process fails.
 */
const decrypt = (text) => {
  const parts = text.split(':');
  const iv = Buffer.from(parts.shift(), 'hex');
  const encryptedText = Buffer.from(parts.join(':'), 'hex');
  const decipher = Crypto.createDecipheriv(
    'aes-256-cbc',
    Buffer.from(process.env.OTP_KEY),
    iv,
  );
  let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
};

/**
 * Creates an object with error messages for string field validation.
 *
 * @param {string} [fieldName='This field'] - The name of the field to be used in error messages.
 * @returns {Object} An object containing error messages for required and invalid type scenarios.
 * @property {string} required_error - Error message when the field is required but missing.
 * @property {string} invalid_type_error - Error message when the field is not a string.
 */
const stringFieldError = (fieldName = 'This field') => ({
  required_error: `${fieldName} is required`,
  invalid_type_error: `${fieldName} must be a string`,
});

/**
 * Builds a WHERE clause for filtering based on parsed filters.
 *
 * @param {Object} parsedFilters - The parsed filters object.
 * @returns {Object} - The WHERE clause object.
 */
const buildFilterWhereClause = (parsedFilters) => {
  if (!parsedFilters) return {};

  return Object.entries(parsedFilters).reduce((whereClause, [key, value]) => {
    whereClause[key] = Array.isArray(value)
      ? { [Sequelize.Op.in]: value }
      : value;
    return whereClause;
  }, {});
};

/**
 * Creates a Zod schema for validating number fields with custom error messages.
 *
 * @param {string} fieldName - The name of the field to be used in error messages
 * @returns {z.ZodNumber} A Zod number schema configured with validation rules and error messages
 * @example
 * const idSchema = createNumberSchema('User ID');
 * Creates a schema that validates:
 * - Value must be a number
 * - Value must be an integer
 * - Value must be positive
 */
const createNumberSchema = (fieldName, message = false) => {
  const invalidMessage = {
    message: message ? fieldName : `Invalid ${fieldName}`,
  };

  return z
    .number({
      invalid_type_error: `${fieldName} must be a number`,
      required_error: `${fieldName} is required`,
    })
    .int(invalidMessage)
    .positive(invalidMessage);
};

/**
 * Creates a Zod schema for validating and transforming ID parameters from string to number.
 *
 * @param {string} fieldName - The name of the field to be used in error messages
 * @returns {z.ZodEffects<z.ZodString, number>} A Zod schema that validates string IDs and transforms them to numbers
 * @example
 * const userIdSchema = createIdParamsSchema('User ID');
 *
 *  Creates a schema that:
 *  - Validates required string input
 *  - Ensures string can be converted to number
 *  - Transforms string to number
 */
const createIdParamsSchema = (fieldName, message = false) => {
  return z
    .string({
      required_error: message ? fieldName : `${fieldName} is required`,
    })
    .refine(
      (val) => {
        return val !== undefined && val !== '' && !isNaN(Number(val));
      },
      {
        message: `Invalid ${fieldName}`,
      },
    )
    .transform((val) => Number(val));
};

const createOptionalIdParamsSchema = (fieldName) => {
  return z
    .string()
    .refine((val) => !isNaN(Number(val)), {
      message: `Invalid ${fieldName}`,
    })
    .transform((val) => Number(val))
    .optional();
};

/**
 * Increments a string of uppercase letters following alphabetical order with carry.
 * When Z is reached, it carries over to add a new letter A at the start.
 *
 * @param {string} letters - The string of uppercase letters to increment
 * @returns {string} The incremented letter string
 * @example
 * incrementLetters('AA')  // Returns 'AB'
 * incrementLetters('AZ')  // Returns 'BA'
 */
const incrementLetters = (letters) => {
  let carry = 1;
  let incremented = '';

  for (let i = letters.length - 1; i >= 0; i--) {
    let charCode = letters.charCodeAt(i);

    if (carry) {
      if (charCode === 90) {
        incremented = 'A' + incremented;
      } else {
        incremented = String.fromCharCode(charCode + 1) + incremented;
        carry = 0;
      }
    } else {
      incremented = letters[i] + incremented;
    }
  }

  if (carry) {
    incremented = 'A' + incremented;
  }

  return incremented;
};

const incrementLettersV2 = (letters) => {
  const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const maxChar = alphabet.length;

  let numericValue = 0;
  for (const element of letters) {
    numericValue = numericValue * maxChar + (alphabet.indexOf(element) + 1);
  }

  numericValue += 1;

  let newLetters = '';
  while (numericValue > 0) {
    const remainder = (numericValue - 1) % maxChar;
    newLetters = alphabet[remainder] + newLetters;
    numericValue = Math.floor((numericValue - 1) / maxChar);
  }

  return newLetters;
};

/**
 * Generates a unique number for a given company code based on the given current number.
 * If currentNumber is not provided, 'AA00000000' is used as the default.
 * The generated number is in the format of (companyCode)(Letters)(Numbers).
 * The <Letters> part is incremented by one (AA, AB, ..., ZZ) if the <Numbers> part exceeds 99999999.
 * The <Numbers> part is zero-padded to 8 digits.
 * @param {string} companyCode - The company code.
 * @param {string} [currentNumber='AA00000000'] - The current number.
 * @returns {string} The generated number.
 */
const generateNumber = (companyCode, currentNumber = 'AA00000000') => {
  const currentId =
    currentNumber === 'AA00000000'
      ? `${companyCode}${currentNumber}`
      : currentNumber;
  const restOfId = currentId.slice(companyCode.length);

  const lettersMatch = RegExp(/[A-Z]+/).exec(restOfId);
  const letters = lettersMatch ? lettersMatch[0] : '';
  const numbers = parseInt(restOfId.slice(letters.length), 10);

  let newNumbers = numbers + 1;
  let newLetters = letters;

  if (newNumbers > 99999999) {
    newNumbers = 0;
    newLetters = incrementLettersV2(newLetters);
  }

  const formattedNumbers = newNumbers.toString().padStart(8, '0'); // Ensure 8 digits
  return `${companyCode}${newLetters}${formattedNumbers}`;
};

const convertToUSDateFormat = (dateString) => {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: '2-digit',
    year: 'numeric',
  }).format(new Date(dateString));
};

const convertDateToDDMMMYYYY = (date, shortMonth = false) => {
  if (date) {
    const formattedDate = new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: shortMonth ? 'short' : 'long',
      day: '2-digit',
    }).formatToParts(new Date(date));

    return `${formattedDate.find((part) => part.type === 'day').value} ${formattedDate.find((part) => part.type === 'month').value} ${formattedDate.find((part) => part.type === 'year').value}`;
  }

  return '';
};

const formatDateYYYYMMDD = (date = new Date(), timeZone = 'Asia/Manila') => {
  const formatter = new Intl.DateTimeFormat('en-CA', {
    timeZone,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });

  const [year, month, day] = formatter.format(new Date(date)).split('-');
  return `${year}${month}${day}`;
};

const newAttachmentSchema = z.object({
  buffer: z.instanceof(Buffer),
  encoding: z.string(),
  fieldname: z.string(),
  filePath: z.string(),
  mimetype: z.string(),
  originalname: z.string(),
  size: z.number(),
});
const getNotificationType = (status, NOTIFICATION_TYPES, canvassStatus) => {
  if (canvassStatus.includes(status)) {
    return NOTIFICATION_TYPES.CANVASS;
  }

  return NOTIFICATION_TYPES.REQUISITION_SLIP;
};

const positiveIntegerSchema = (fieldName) => {
  return z
    .string({
      required_error: `${fieldName} is required.`,
    })
    .refine(
      (val) => {
        const numberValue = Number(val);
        return (
          !isNaN(numberValue) &&
          Number.isInteger(numberValue) &&
          numberValue >= 1
        );
      },
      {
        message: `${fieldName} must be a valid whole number and cannot be less than 1.`,
      },
    )
    .transform((val) => Number(val));
};

const positiveDecimalSchema = (fieldName, allowZero = false) => {
  return z
    .string({
      required_error: `${fieldName} is required.`,
    })
    .refine(
      (val) => {
        const numberValue = Number(val);
        return (
          !isNaN(numberValue) &&
          (allowZero ? numberValue >= 0 : numberValue >= 0.01)
        );
      },
      {
        message: allowZero
          ? `${fieldName} must be a valid number greater than or equal to 0.`
          : `${fieldName} must be a valid number greater than or equal to 0.01.`,
      },
    )
    .transform((val) => {
      const numberValue = Number(val);
      return Number(numberValue.toFixed(2));
    });
};

const positiveIntegerSchemaV2 = (fieldName) => {
  return z
    .number({
      required_error: `${fieldName} is required.`,
      invalid_type_error: `${fieldName} must be a number.`,
    })
    .int(`${fieldName} must be a whole number.`)
    .min(1, `${fieldName} cannot be less than 1.`)
    .transform((val) => Number(val));
};

const positiveDecimalSchemaV2 = (
  fieldName,
  allowZero = false,
  decimals = 2,
) => {
  const minValue = allowZero
    ? 0
    : Number((1 / 10 ** decimals).toFixed(decimals));

  const minMessage = allowZero
    ? `${fieldName} must be greater than or equal to 0.`
    : `${fieldName} must be greater than or equal to ${minValue}.`;

  return z.preprocess(
    (value) => {
      const parsed = parseFloat(value);
      if (isNaN(parsed)) return value;

      return Number(parsed.toFixed(decimals));
    },
    z
      .number({
        required_error: `${fieldName} is required.`,
        invalid_type_error: `${fieldName} must be a number.`,
      })
      .min(minValue, minMessage)
      .transform((val) => Number(val.toFixed(decimals))),
  );
};

const getNextNumberAndLetter = (lastNumber = '0', lastLetter = 'AA') => {
  const nextNumber = (parseInt(lastNumber) + 1).toString().padStart(8, '0');
  const nextLetter =
    nextNumber === '100000000' ? incrementLetters(lastLetter) : lastLetter;
  const finalNumber = nextNumber === '100000000' ? '00000001' : nextNumber;

  return {
    nextLetter,
    nextNumber: finalNumber,
  };
};

const decimalFormat = (fieldName) =>
  z.string().refine(
    (val) => {
      const numberValue = Number(val);
      return (
        !isNaN(numberValue) &&
        numberValue >= 0 &&
        val.indexOf('.') !== -1 &&
        val.split('.')[1].length === 2
      );
    },
    {
      message: `${fieldName} must be a valid value with 2 decimal places.`,
    },
  );

const formatCurrency = (amount, decimal = 2, style = 'currency') => {
  return new Intl.NumberFormat('en-PH', {
    style,
    currency: 'PHP',
    minimumFractionDigits: decimal,
  }).format(amount);
};

const convertToHHMMSS = (date, timeZone = 'Asia/Manila') => {
  const formatter = new Intl.DateTimeFormat('en-US', {
    hour12: false,
    timeZone,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });

  const parts = formatter.formatToParts(new Date(date));
  const hh = parts.find((p) => p.type === 'hour')?.value;
  const mm = parts.find((p) => p.type === 'minute')?.value;
  const ss = parts.find((p) => p.type === 'second')?.value;

  return `${hh}${mm}${ss}`;
};

function amountInWordsPHP(number) {
  const ones = [
    '',
    'One',
    'Two',
    'Three',
    'Four',
    'Five',
    'Six',
    'Seven',
    'Eight',
    'Nine',
    'Ten',
    'Eleven',
    'Twelve',
    'Thirteen',
    'Fourteen',
    'Fifteen',
    'Sixteen',
    'Seventeen',
    'Eighteen',
    'Nineteen',
  ];

  const tens = [
    '',
    '',
    'Twenty',
    'Thirty',
    'Forty',
    'Fifty',
    'Sixty',
    'Seventy',
    'Eighty',
    'Ninety',
  ];

  const scales = ['', 'Thousand', 'Million', 'Billion'];

  function convertHundreds(num) {
    let word = '';

    if (num > 99) {
      word += ones[Math.floor(num / 100)] + ' Hundred';
      num %= 100;
      if (num) word += ' ';
    }

    if (num > 0 && num < 20) {
      word += ones[num];
    } else if (num >= 20) {
      word += tens[Math.floor(num / 10)];
      if (num % 10) word += '-' + ones[num % 10];
    }

    return word;
  }

  function convertNumberToWords(num) {
    if (num === 0) return 'Zero';

    let result = '';
    let scaleIndex = 0;

    while (num > 0) {
      const chunk = num % 1000;
      if (chunk) {
        const chunkWords = convertHundreds(chunk);
        result =
          chunkWords +
          (scales[scaleIndex] ? ' ' + scales[scaleIndex] : '') +
          (result ? ' ' + result : '');
      }
      num = Math.floor(num / 1000);
      scaleIndex++;
    }

    return result.trim();
  }

  number = parseFloat(number).toFixed(2);
  const [pesoPart, centavoPart] = number.split('.').map((n) => parseInt(n));

  const pesoWords = convertNumberToWords(pesoPart);
  const centavoWords = centavoPart > 0 ? convertNumberToWords(centavoPart) : '';

  let result = '';

  if (pesoPart > 0) {
    result += pesoWords + (pesoPart === 1 ? ' Peso' : ' Pesos');
  }

  if (centavoPart > 0) {
    result +=
      (pesoPart > 0 ? ' and ' : '') +
      centavoWords +
      (centavoPart === 1 ? ' Centavo' : ' Centavos');
  }

  return result || 'Zero Pesos';
}

function paginateItems(items, pageSize = 15) {
  const paginated = [];

  for (let i = 0; i < items.length; i += pageSize) {
    paginated.push(items.slice(i, i + pageSize));
  }

  return paginated;
}

function paginateItemsWithDifferentSizes(items, firstPageSize, nextPageSize) {
  if (!items.length) return [];

  const totalItems = items.length;
  const pagesData = [];

  const firstPage = items.slice(0, firstPageSize);
  pagesData.push({
    page: 1,
    from: 1,
    to: Math.min(firstPageSize, totalItems),
    data: firstPage,
    isLastPage: items.length <= firstPageSize,
  });

  let remainingItems = items.slice(firstPageSize);
  let currentPage = 2;
  let startIndex = firstPageSize + 1;

  for (let i = 0; i < remainingItems.length; i += nextPageSize) {
    const pageItems = remainingItems.slice(i, i + nextPageSize);
    const endIndex = Math.min(startIndex + pageItems.length - 1, totalItems);

    pagesData.push({
      page: currentPage,
      from: startIndex,
      to: endIndex,
      data: pageItems,
      isLastPage: endIndex === totalItems,
    });

    startIndex += pageItems.length;
    currentPage++;
  }

  return {
    pagesData,
    totalPages: pagesData.length,
    totalItems,
  };
}

const calculateDiscountedPrice = (
  unitPrice,
  discountType,
  discountValue,
  quantity,
) => {
  if (!unitPrice) return 0;

  if (discountType === 'percent') {
    const discounted =
      quantity * (unitPrice - (unitPrice * discountValue) / 100);
    return discounted.toFixed(2);
  }

  const discounted = quantity * (unitPrice - discountValue);
  return discounted.toFixed(2);
};

async function readFile(filePath, encoding = 'utf-8', safe = false) {
  try {
    return await fs.readFile(filePath, encoding);
  } catch (err) {
    if (err.code === 'ENOENT' && safe) return undefined;
    throw err;
  }
}

function convertDateYYYYMMDDHHMMSS(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}${month}${day}-${hours}${minutes}${seconds}`;
}

function formatToMonthDayYear(dateString) {
  try {
    const date = new Date(dateString);

    if (isNaN(date.getTime())) {
      console.error('Invalid date string provided:', dateString);
      return 'Invalid Date';
    }

    const options = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    return date.toLocaleDateString('en-US', options);
  } catch (error) {
    console.error('An error occurred during date formatting:', error);
    return 'Error formatting date';
  }
}
module.exports = {
  getNextNumberAndLetter,
  encrypt,
  decrypt,
  HTTPClient,
  parseDomain,
  addAuditHooks,
  generateSecret,
  incrementLetters,
  stringFieldError,
  validatorCompiler,
  createNumberSchema,
  createIdParamsSchema,
  generateNumber,
  buildFilterWhereClause,
  convertToUSDateFormat,
  newAttachmentSchema,
  formatDateYYYYMMDD,
  convertDateToDDMMMYYYY,
  getNotificationType,
  createOptionalIdParamsSchema,
  Terms,
  positiveIntegerSchema,
  positiveDecimalSchema,
  positiveIntegerSchemaV2,
  positiveDecimalSchemaV2,
  decimalFormat,
  formatCurrency,
  amountInWordsPHP,
  convertToHHMMSS,
  paginateItems,
  paginateItemsWithDifferentSizes,
  calculateDiscountedPrice,
  readFile,
  convertDateYYYYMMDDHHMMSS,
  formatToMonthDayYear,
};
