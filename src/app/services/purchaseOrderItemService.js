class PurchaseOrderItemService {
  constructor(container) {
    const {
      db,
      entities,
      constants,
      clientErrors,
      fastify,
      purchaseOrderItemRepository,
      purchaseOrderRepository,
    } = container;

    this.db = db;
    this.Sequelize = db.Sequelize;
    this.constants = constants;
    this.clientErrors = clientErrors;
    this.fastify = fastify;
    this.purchaseOrderItemRepository = purchaseOrderItemRepository;
    this.purchaseOrderRepository = purchaseOrderRepository;
  }

  async getPOItemsById(payload) {
    let poItems;
    
    if (payload.type === 'receiving') {
      poItems = await this.purchaseOrderItemRepository.getPOItemsForReceiving(payload);
    } else {
      poItems = await this.purchaseOrderItemRepository.getPOItemsById(payload);
      
      const totalAmounts = await this.purchaseOrderRepository.getPurchaseOrderTotals(payload.purchaseOrderId);
      poItems['summary'] = {
        id: payload.purchaseOrderId,
        amount: totalAmounts.totalBaseAmount + totalAmounts.totalAdditionalFees,
        discount: parseFloat(totalAmounts.totalDiscountAmount),
        totalAmount: parseFloat(totalAmounts.grandTotal).toFixed(2),
        total_amount: totalAmounts.grandTotal,
      }
    }
    
    return poItems;
  }
}

module.exports = PurchaseOrderItemService;
