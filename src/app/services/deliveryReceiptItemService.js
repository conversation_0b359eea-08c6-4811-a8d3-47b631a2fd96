const { convertToUSDateFormat } = require('../utils');
const {
  deliveryReceiptItem: DELIVERY_RECEIPT_ITEM,
} = require('../../domain/constants');
class DeliveryReceiptItemService {
  constructor({
    deliveryReceiptRepository,
    deliveryReceiptItemRepository,
    deliveryReceiptItemHistoryRepository,
    purchaseOrderItemRepository,
    clientErrors,
    db,
  }) {
    this.deliveryReceiptRepository = deliveryReceiptRepository;
    this.deliveryReceiptItemRepository = deliveryReceiptItemRepository;
    this.deliveryReceiptItemHistoryRepository =
      deliveryReceiptItemHistoryRepository;
    this.clientErrors = clientErrors;
    this.purchaseOrderItemRepository = purchaseOrderItemRepository;
    this.db = db;
    this.Sequelize = db.Sequelize;
  }

  async getAllDeliveryReceiptItemsByPOId(purchaseOrderId, options = {}) {
    return this.deliveryReceiptItemRepository.getAllDeliveryReceiptItemsByPOId(
      purchaseOrderId,
      options,
    );
  }

  async getItemHistory(itemId, { page, limit }) {
    const history =
      await this.deliveryReceiptItemHistoryRepository.getHistoryByDeliveryReceiptItemId(
        itemId,
        { page, limit },
      );

    if (history.count === 0) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Delivery receipt item with id of ' + itemId + ' not found.',
      });
    }

    const deliveryReceiptItem =
      await this.deliveryReceiptItemRepository.getById(itemId);

    const deliveryReceiptItemName = deliveryReceiptItem
      ? deliveryReceiptItem.itemDes
      : null;

    return {
      start: (page - 1) * limit + 1,
      end: Math.min(page * limit, history.count),
      total: history.count,
      name: deliveryReceiptItemName,
      rows: history.rows.map((row) => ({
        createdAt: row.createdAt,
        qtyOrdered: row.qtyOrdered,
        qtyDelivered: row.qtyDelivered,
        qtyReturned: row.qtyReturned,
        dateDelivered: convertToUSDateFormat(row.dateDelivered),
        status: row.status,
      })),
    };
  }

  async updateItem(id, data, userFromToken) {
    const deliveryReceiptItem =
      await this.deliveryReceiptItemRepository.getById(id);

    if (!deliveryReceiptItem) {
      throw this.clientErrors.NOT_FOUND({
        message: `Delivery receipt item with id of ${id} not found`,
      });
    }

    if (
      deliveryReceiptItem.deliveryStatus ===
        DELIVERY_RECEIPT_ITEM.DELIVERY_ITEM_STATUSES.FULLY_DELIVERED ||
      deliveryReceiptItem.deliveryStatus ===
        DELIVERY_RECEIPT_ITEM.DELIVERY_ITEM_STATUSES
          .FULLY_DELIVERED_WITH_RETURNS
    ) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Delivery receipt item with id of ${id} is already fully delivered.`,
      });
    }

    const updateItem = await this.deliveryReceiptItemRepository.updateItem(
      id,
      data,
      { userId: userFromToken.id },
    );

    return updateItem.length ? updateItem[1][0]?.dataValues : null;
  }

  async cancelReturns(id, userFromToken) {
    const deliveryReceiptItem =
      await this.deliveryReceiptItemRepository.getById(id);

    if (!deliveryReceiptItem) {
      throw this.clientErrors.NOT_FOUND({
        message: `Delivery receipt item with id of ${id} not found`,
      });
    }

    const updateItem = await this.deliveryReceiptItemRepository.updateItem(
      id,
      {
        qtyReturned: 0,
        hasReturns: false,
      },
      { userId: userFromToken.id },
    );

    return updateItem.length ? updateItem[1][0]?.dataValues : null;
  }

  async validateQtyDelivered(data) {
    if (!Array.isArray(data) || data.length === 0) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Invalid delivery items data',
      });
    }

    // Get all PO item IDs from the data
    const poItemIds = data.map((item) => item.poItemId);

    // Extract delivery receipt item IDs for exclusion (if they exist)
    const drItemIds = data.filter((item) => item.id).map((item) => item.id);
    const excludeClause =
      drItemIds.length > 0
        ? `AND "delivery_receipt_items"."id" NOT IN (${drItemIds.join(',')})`
        : '';

    // Fetch the current remaining quantities for all PO items
    const poItems = await this.purchaseOrderItemRepository.findAll({
      paginate: false,
      where: {
        id: { [this.Sequelize.Op.in]: poItemIds },
      },
      attributes: [
        'id',
        [
          this.Sequelize.literal(
            `CAST("quantity_purchased" - COALESCE((SELECT SUM("delivery_receipt_items"."qty_delivered") 
            FROM "delivery_receipt_items" 
            JOIN "delivery_receipts" ON "delivery_receipt_items"."dr_id" = "delivery_receipts"."id" 
            WHERE "delivery_receipt_items"."po_item_id" = "purchase_order_items"."id" 
            AND "delivery_receipts"."is_draft" = false
            ${excludeClause}), 0) AS FLOAT)`,
          ),
          'remainingQtyForDelivery',
        ],
      ],
      raw: true,
    });

    // Check each item's quantity
    const invalidItems = [];

    for (const [index, item] of data.entries()) {
      const poItem = poItems.data.find((po) => po.id === item.poItemId);

      if (!poItem) {
        invalidItems.push({
          index: index + 1,
          poItemId: item.poItemId,
          itemDes: item.itemDes,
          error: 'Purchase order item not found',
        });
        continue;
      }

      // If updating an existing item, add its current quantity to the remaining quantity
      const adjustedRemainingQty = parseFloat(
        parseFloat(poItem.remainingQtyForDelivery).toFixed(3),
      );
      const qtyDelivered = parseFloat(parseFloat(item.qtyDelivered).toFixed(3));

      if (qtyDelivered > adjustedRemainingQty) {
        invalidItems.push({
          index: index + 1,
          poItemId: item.poItemId,
          itemDes: item.itemDes,
          qtyDelivered,
          remainingQty: adjustedRemainingQty,
          error: `Quantity delivered (${qtyDelivered}) exceeds remaining quantity (${adjustedRemainingQty})`,
        });
      }
    }

    if (invalidItems.length > 0) {
      const itemNumbers = invalidItems
        .map((item) => `#${item.index}`)
        .join(', ');
      throw this.clientErrors.UNPROCESSABLE_ENTITY({
        message: `Some items (${itemNumbers}) exceed their maximum remaining delivery quantity.\nDetails:\n ${invalidItems
          .map(
            (item) =>
              `Item #${item.index}: Max remaining qty ${item.remainingQty}, Attempted delivery ${item.qtyDelivered}`,
          )
          .join(';\n')}`,
      });
    }

    return true;
  }
}

module.exports = DeliveryReceiptItemService;
