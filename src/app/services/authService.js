class AuthService {
  constructor (container) {
    const {
      userRepository,
      bcrypt,
      userErrors,
      clientErrors,
      fastify,
      OTPAuth,
      utils,
      constants,
    } = container;

    this.utils = utils;
    this.fastify = fastify;
    this.bcrypt = bcrypt;
    this.userErrors = userErrors;
    this.userConstants = constants.user;
    this.clientErrors = clientErrors;
    this.userRepository = userRepository;
    this.OTPAuth = OTPAuth;
    this.OTP_CONFIG = {
      issuer: 'CityLand-PRS',
      digits: 6,
      period: 30,
      algorithm: 'SHA1',
    };
  }

  async verifyUser(payload) {
    const { username, password } = payload;
    const { USER_STATUS } = this.userConstants;

    const existingUser = await this.userRepository.getUser({
      username,
    });

    if (!existingUser || existingUser?.status === USER_STATUS.INACTIVE) {
      throw this.clientErrors.UNAUTHORIZED({
        message: 'Invalid username or password',
      });
    }

    const isMatch = this.bcrypt.compareSync(password, existingUser.password);

    if (!isMatch) {
      throw this.clientErrors.UNAUTHORIZED({
        message: 'Invalid username or password',
      });
    }

    return existingUser;
  }

  verifyOTP(userFromToken, otp) {
    if (!userFromToken.otpSecret) {
      throw this.clientErrors.UNPROCESSABLE_ENTITY({
        message: 'User MFA is not yet setup',
      });
    }

    const plainOtpSecret = this.utils.decrypt(userFromToken.otpSecret);
    const totp = this.createTOTP(userFromToken.username, plainOtpSecret);
    const delta = totp.validate({ token: otp, window: 1 });

    if (delta !== 0) {
      throw this.clientErrors.UNPROCESSABLE_ENTITY({
        message: 'Invalid OTP provided',
      });
    }

    return userFromToken;
  }

  async saveOTPSecret(userFromToken, payload) {
    const { otp, otpSecret } = payload;

    if (userFromToken.otpSecret) {
      throw this.clientErrors.UNPROCESSABLE_ENTITY({
        message: 'User OTP already setup',
      });
    }

    try {
      /* throws an error if otp secret is invalid / in wrong format */
      const totp = this.createTOTP(userFromToken.username, otpSecret);
      const delta = totp.validate({ token: otp, window: 1 });

      if (delta !== 0) throw new Error();
    } catch (error) {
      this.fastify.log.error(error);
      throw this.clientErrors.UNPROCESSABLE_ENTITY({
        message: 'Invalid OTP provided',
      });
    }

    const encryptedOtpSecret = this.utils.encrypt(otpSecret);
    await this.userRepository.update(
      { username: userFromToken.username },
      { otpSecret: encryptedOtpSecret },
    );

    return userFromToken;
  }

  async updateTempPassword(userFromToken, newPassword) {
    if (!userFromToken.isPasswordTemporary) {
      throw this.clientErrors.UNPROCESSABLE_ENTITY({
        message: 'Temporary password has been already updated',
      });
    }

    /* New password must not be the same as the old password */
    const isMatch = this.bcrypt.compareSync(
      newPassword,
      userFromToken.password,
    );

    if (isMatch) {
      throw this.clientErrors.UNPROCESSABLE_ENTITY({
        message: 'New password cannot be the same as the old password',
      });
    }

    const hashedPassword = this.bcrypt.hashSync(newPassword, 8);
    await this.userRepository.updateUserById(userFromToken.id, {
      password: hashedPassword,
      isPasswordTemporary: false,
      tempPass: null,
    });

    return userFromToken;
  }

  createTOTP(username, secret) {
    return new this.OTPAuth.TOTP({
      ...this.OTP_CONFIG,
      label: username,
      secret: secret,
    });
  }

  generateJWT(payload, expiresInMinutes) {
    const currentTime = Math.floor(Date.now() / 1000);
    const expiresAt = currentTime + expiresInMinutes * 60;
    const token = this.fastify.jwt.sign({
      ...payload,
      iat: currentTime,
      exp: expiresAt,
    });

    return { token, expiresAt };
  }

  /* Generate otp token setup - 10 mins */
  sendUserOtpSecret(userId, username, reply) {
    const { base32: otpSecret } = new this.OTPAuth.Secret();
    const totp = this.createTOTP(username, otpSecret);
    const jwtResult = this.generateJWT({ id: userId, isForOTP: true }, 10);

    return reply.status(200).send({
      requireOTPSetup: true,
      otpSecret: otpSecret,
      otpAuthUrl: totp.toString(),
      accessToken: jwtResult.token,
      expiresAt: jwtResult.expiresAt,
    });
  }

  /* Generate Temp Pass Token - 5 mins */
  sendTempPassToken(userId, reply) {
    const jwtResult = this.generateJWT({ id: userId, isForTempPass: true }, 5);

    return reply.status(200).send({
      requireUpdatePassword: true,
      accessToken: jwtResult.token,
      expiresAt: jwtResult.expiresAt,
    });
  }

  /**
   * Generate Access Token - 60 mins
   * Refresh token valid for 7 days (10080 minutes)
   */
  async sendAccessToken(userId, reply) {
    const userDetails = await this.userRepository.getUserById(userId);
    const jwtResult = this.generateJWT({ id: userId, ...userDetails }, 60);
    const refreshTokenResult = this.generateJWT(
      { id: userId, isForRefresh: true },
      10080,
    );

    return reply.status(200).send({
      accessToken: jwtResult.token,
      refreshToken: refreshTokenResult.token,
      expiredAt: jwtResult.expiresAt,
    });
  }

  sendVerifyOTPToken(userId, reply) {
    const jwtResult = this.generateJWT({ id: userId, isForOTP: true }, 5);

    return reply.status(200).send({
      requireOTPVerification: true,
      accessToken: jwtResult.token,
      expiresAt: jwtResult.expiresAt,
    });
  }
}

module.exports = AuthService;
