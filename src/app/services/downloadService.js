const converter = require('json-2-csv');
class DownloadService {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      clientErrors,
      leaveRepository,
      fastify,
      requisitionRepository,
      nonRequisitionRepository,
      historyRepository,
      constants,
    } = container;

    this.db = db;
    this.utils = utils;
    this.clientErrors = clientErrors;
    this.entities = entities;
    this.leaveRepository = leaveRepository;
    this.fastify = fastify;
    this.Sequelize = db.Sequelize;
    this.requisitionRepository = requisitionRepository;
    this.nonRequisitionRepository = nonRequisitionRepository;
    this.historyRepository = historyRepository;
    this.constants = constants;
  }

  #generateNewLine() {
    return '\r\n';
  }

  #generateDateLine() {
    const formattedDate = this.utils.formatDateYYYYMMDD();
    const dateLine = { [`Extract as of ${formattedDate}`]: '' };

    return converter.json2csv(dateLine);
  }

  #formatExcelFile(...lines) {
    return lines.join('');
  }

  async downloadDashboardExcel(payload) {
    const {
      limit = 1_000_000,
      requestType = 'all',
      userId,
      ...options
    } = payload;
    const data = await this.requisitionRepository.getAllRequisitionsV2({
      limit,
      userId,
      ...options,
    });

    const transformData = [];

    data[requestType]?.forEach((data) =>
      transformData.push({
        [`Ref No`]: `${data.ref_number}`,
        Type: `${data.doc_type}`,
        Requester: `${data.requestor_name}`,
        Company: `${data.company_name}`,
        Department: `${data.department_name || '---'}`,
        Project: `${data.project_name || '---'}`,
        ['Last Updated']: `${this.utils.formatToMonthDayYear(data.updated_at)}`,
        Status: data.status.replaceAll('_', ' ').toUpperCase(),
      }),
    );

    return this.#formatExcelFile(
      this.#generateDateLine(),
      this.#generateNewLine(),
      converter.json2csv(transformData),
    );
  }

  generateExcelFile(data) {
    return this.#formatExcelFile(
      this.#generateDateLine(),
      this.#generateNewLine(),
      converter.json2csv(data),
    );
  }

  async downloadItemHistory(payload) {
    const historyData = await this.getItemPurchaseHistoryReport(payload);

    if (!historyData || historyData.length === 0) {
      // Return empty CSV if no data
      return this.#formatExcelFile(
        this.#generateDateLine(),
        this.#generateNewLine(),
        converter.json2csv([]),
      );
    }

    const transformData = [];

    for (const data of historyData) {
      const transformedData = {
        'RS Number': data.rsNumber || ' --- ',
        'Supplier ': data.supplierName || ' --- ',
        'Price per Unit': data.pricePerUnit || ' --- ',
        'Qty ': data.quantity || ' --- ',
        'Date Purchased': data.datePurchased ? 
          this.utils.formatToMonthDayYear(data.datePurchased) : ' --- ',
      };

      transformData.push(transformedData);
    }

    // Generate CSV file
    return this.#formatExcelFile(
      this.#generateDateLine(),
      this.#generateNewLine(),
      converter.json2csv(transformData),
    );
  }

  async getItemPurchaseHistoryReport(params) {
    const {
      id,
      type,
      filterBy = null,
      sortBy = { updatedAt: 'desc' },
    } = params;

    const order = Object.entries(sortBy).map(([key, value]) => [
      key,
      value.toUpperCase(),
    ]);

    const { data: histories } = await this.historyRepository.getAllItemsHistory(
      {
        id,
        type,
        filterBy,
        order,
        paginate: false,
      },
    );

    // If no histories, return immediately
    if (!histories.length) return [];

    // Get valid PO statuses
    const { PO_STATUS } = this.constants.purchaseOrder;
    const validStatuses = [
      PO_STATUS.FOR_SENDING,
      PO_STATUS.FOR_DELIVERY,
      PO_STATUS.CLOSED_PO,
      PO_STATUS.CANCELLED_PO,
    ]
      .map((status) => `'${status}'`)
      .join(', ');

    // PO data query (without joining to histories)
    const poDataQuery = `
    WITH supplier_data AS (
      SELECT 
        po.id as po_id,
        CASE 
          WHEN po.supplier_name IS NOT NULL THEN po.supplier_name
          WHEN po.supplier_type = 'company' THEN c.name
          WHEN po.supplier_type = 'project' THEN p.name
          WHEN po.supplier_type = 'supplier' THEN s.name
          ELSE 'N/A'
        END as supplier_name
      FROM purchase_orders po
      LEFT JOIN companies c ON po.supplier_id = c.id AND po.supplier_type = 'company'
      LEFT JOIN projects p ON po.supplier_id = p.id AND po.supplier_type = 'project'
      LEFT JOIN suppliers s ON po.supplier_id = s.id AND po.supplier_type = 'supplier'
    )
    SELECT 
      ril.item_id,
      CONCAT('RS-', CASE WHEN LENGTH(CAST(r.company_code AS VARCHAR)) = 1 THEN CONCAT('0', r.company_code) ELSE CAST(r.company_code AS VARCHAR) END, r.rs_letter, r.rs_number) as rs_number,
      CONCAT(
        CASE 
          WHEN LENGTH(CAST(r.company_code AS VARCHAR)) = 1 THEN CONCAT('0', r.company_code) 
          ELSE CAST(r.company_code AS VARCHAR) 
        END, 
        r.rs_letter, 
        r.rs_number
      ) AS rs_number_raw,
      sd.supplier_name,
      CASE 
        WHEN cis.discount_type = 'percent' THEN 
          ROUND(CAST(cis.unit_price * (1 - cis.discount_value / 100) AS numeric), 2)
        ELSE  
          ROUND(CAST(cis.unit_price - (cis.discount_value / poi.quantity_purchased) AS numeric), 2)
      END as unit_price,
      poi.quantity_purchased,
      ril.quantity as requested_quantity, 
      cis.quantity as approved_quantity,
      po.created_at as date_purchased,
      r.id as requisition_id
    FROM requisition_item_lists ril
    JOIN purchase_order_items poi ON poi.requisition_item_list_id = ril.id
    JOIN purchase_orders po ON poi.purchase_order_id = po.id
    JOIN requisitions r ON po.requisition_id = r.id
    JOIN canvass_item_suppliers cis ON poi.canvass_item_supplier_id = cis.id
    JOIN supplier_data sd ON po.id = sd.po_id
    WHERE ril.item_id = :id
    AND po.status IN (${validStatuses})
  `;

    const poData = await this.db.sequelize.query(poDataQuery, {
      replacements: { id },
      type: this.db.Sequelize.QueryTypes.SELECT,
    });

    // Match each history record to PO data
    const formattedResults = histories.map((history) => {
      // Find matching PO using rs number and requested quantity
      const purchaseOrderitemHistory = poData.filter(
        (po) =>
          po.rs_number_raw === history.rsNumber &&
          po.requested_quantity === history.quantityRequested,
      )[0];

      return {
        id: history.id,
        rsNumber:
          purchaseOrderitemHistory?.rs_number || `RS-${history.rsNumber}`,
        supplierName: purchaseOrderitemHistory?.supplier_name || ' --- ',
        pricePerUnit: purchaseOrderitemHistory?.unit_price
          ? parseFloat(purchaseOrderitemHistory.unit_price).toFixed(2) + ''
          : history.price? parseFloat(history.price).toFixed(2) + '' : ' --- ',
        quantity: purchaseOrderitemHistory?.quantity_purchased
          ? parseFloat(purchaseOrderitemHistory.quantity_purchased).toFixed(3)
          : history.quantityRequested? parseFloat(history.quantityRequested).toFixed(3) : ' --- ',
        datePurchased: purchaseOrderitemHistory?.date_purchased
          ? purchaseOrderitemHistory.date_purchased
          : null,
        requisitionId: purchaseOrderitemHistory
          ? purchaseOrderitemHistory.requisition_id
          : ''
      };
    });

    return formattedResults;
  }
}

module.exports = DownloadService;
