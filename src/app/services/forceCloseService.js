class ForceCloseService {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      constants,
      clientErrors,
      serverErrors,
      fastify,
      requisitionRepository,
      canvassRequisitionRepository,
      purchaseOrderRepository,
      deliveryReceiptRepository,
      rsPaymentRequestRepository,
      invoiceReportRepository,
      itemRepository,
      commentRepository,
      historyRepository,
      notificationService,
      userRepository,
      forceCloseRepository,
      // Additional repositories for force close operations (optional)
      requisitionItemListRepository = null,
      purchaseOrderItemRepository = null,
      canvassItemRepository = null,
      deliveryReceiptItemRepository = null,
    } = container;

    this.db = db;
    this.utils = utils;
    this.entities = entities;
    this.constants = constants;
    this.clientErrors = clientErrors;
    this.serverErrors = serverErrors;
    this.fastify = fastify;
    this.requisitionRepository = requisitionRepository;
    this.canvassRequisitionRepository = canvassRequisitionRepository;
    this.purchaseOrderRepository = purchaseOrderRepository;
    this.deliveryReceiptRepository = deliveryReceiptRepository;
    this.rsPaymentRequestRepository = rsPaymentRequestRepository;
    this.invoiceReportRepository = invoiceReportRepository;
    this.itemRepository = itemRepository;
    this.commentRepository = commentRepository;
    this.historyRepository = historyRepository;
    this.notificationService = notificationService;
    this.userRepository = userRepository;
    this.forceCloseRepository = forceCloseRepository;
    // Additional repositories for force close operations (optional)
    this.requisitionItemRepository = requisitionItemListRepository;
    this.purchaseOrderItemRepository = purchaseOrderItemRepository;
    this.canvassItemRepository = canvassItemRepository;
    this.deliveryReceiptItemRepository = deliveryReceiptItemRepository;
  }

  /**
   * Validate if a requisition is eligible for force close
   * Implements the flowchart logic: CheckUser → CheckRS → CheckPO/CheckRemQty → CheckCS
   * Comprehensive error handling for Error1, Error2, Error3 scenarios
   */
  async validateForceCloseEligibility({ requisitionId, userFromToken, notes, checkOnly = false }) {
    console.log(`🚀 CONSOLE: MAIN validateForceCloseEligibility starting for RS: ${requisitionId}`);
    this.fastify.log.info(`🚀 MAIN validateForceCloseEligibility starting for RS: ${requisitionId}`);

    try {
      // Step 1: CheckUser - Authorization validation (Error1 scenarios)
      this.fastify.log.info(`🔍 Step 1: Checking user authorization for RS: ${requisitionId}`);
      const authResult = await this._validateUserAuthorization(requisitionId, userFromToken);
      this.fastify.log.info(`🔍 Step 1 result:`, { isAuthorized: authResult.isAuthorized });
      if (!authResult.isAuthorized) {
        this.fastify.log.warn(`Force close authorization failed for RS: ${requisitionId} - ${authResult.details}`);
        return this._createError1Response(authResult);
      }

      // Step 2: CheckRS - Requisition status validation (Error2 scenarios)
      this.fastify.log.info(`🔍 Step 2: Checking requisition status for RS: ${requisitionId}`);
      const rsResult = await this._validateRequisitionStatus(requisitionId);
      this.fastify.log.info(`🔍 Step 2 result:`, { isValid: rsResult.isValid, showButton: rsResult.showButton });
      if (!rsResult.isValid) {
        this.fastify.log.warn(`Force close RS status validation failed for RS: ${requisitionId} - ${rsResult.reason}`);
        return this._createError2Response(rsResult);
      }

      // Step 3: Determine scenario and validate conditions (Error3 scenarios)
      this.fastify.log.info(`🔍 Step 3: Determining force close scenario for RS: ${requisitionId}`);
      const scenarioResult = await this._determineForceCloseScenario(requisitionId);
      if (!scenarioResult.isEligible) {
        this.fastify.log.warn(`Force close scenario validation failed for RS: ${requisitionId} - ${scenarioResult.reason}`);
        return this._createError3Response(scenarioResult);
      }

      // Step 4: Validate notes if not check-only (Error3 scenarios)
      if (!checkOnly && notes) {
        const notesResult = this._validateForceCloseNotes(notes);
        if (!notesResult.isValid) {
          this.fastify.log.warn(`Force close notes validation failed for RS: ${requisitionId} - ${notesResult.reason}`);
          return this._createError3Response(notesResult);
        }
      }

      this.fastify.log.info(`Force close eligibility validation successful for RS: ${requisitionId}`);
      return {
        isEligible: true,
        buttonVisible: true,
        scenario: scenarioResult.scenario,
        reason: 'Eligible for force close',
        details: scenarioResult.details,
      };

    } catch (error) {
      this.fastify.log.error(`Force close eligibility validation error for RS: ${requisitionId} - ${error.message}`);

      // Use enhanced error handling for comprehensive error analysis
      const context = {
        requisitionId,
        step: 'eligibility_validation',
        operation: 'validateForceCloseEligibility',
        checkOnly,
      };

      return this._createEnhancedErrorResponse(error, context);
    }
  }

  /**
   * Execute force close workflow based on scenario
   * Comprehensive error handling for all execution phases
   */
  async executeForceClose({ requisitionId, userFromToken, notes, scenario, transaction }) {
    this.fastify.log.info(`Executing force close for RS: ${requisitionId}, Scenario: ${scenario}`);

    const { FORCE_CLOSE_SCENARIOS, FORCE_CLOSE_ERRORS } = this.constants.forceClose;
    const result = {
      requisitionId,
      scenario,
      actions: [],
    };

    try {
      // Validate scenario before execution
      if (!Object.values(FORCE_CLOSE_SCENARIOS).includes(scenario)) {
        throw this.clientErrors.BAD_REQUEST({
          message: FORCE_CLOSE_ERRORS.UNKNOWN_SCENARIO,
          details: {
            providedScenario: scenario,
            validScenarios: Object.values(FORCE_CLOSE_SCENARIOS),
          },
        });
      }

      // Execute force close workflow using repository
      try {
        const repositoryResult = await this.forceCloseRepository.executeForceCloseWorkflow({
          requisitionId,
          userId: userFromToken.id,
          scenario,
          notes,
          transaction,
        });

        // Merge repository result with service result
        Object.assign(result, repositoryResult);

      } catch (repositoryError) {
        this.fastify.log.error(`Force close repository execution failed: ${repositoryError.message}`);
        throw this.clientErrors.BAD_REQUEST({
          message: `Failed to execute force close workflow`,
          details: {
            scenario,
            error: repositoryError.message,
            requisitionId,
          },
        });
      }

      this.fastify.log.info(`Force close completed successfully for RS: ${requisitionId}`);
      return result;

    } catch (error) {
      this.fastify.log.error(`Force close execution failed for RS: ${requisitionId} - ${error.message}`);

      // Re-throw client errors as-is
      if (error.statusCode && error.statusCode < 500) {
        throw error;
      }

      // Wrap unexpected errors
      throw this.serverErrors.INTERNAL_SERVER_ERROR({
        message: FORCE_CLOSE_ERRORS.EXECUTION_FAILED,
        description: `Force close execution failed for requisition ${requisitionId}: ${error.message}`,
      });
    }
  }

  /**
   * Step 1: CheckUser - Validate user authorization (Requester OR Assigned Purchasing Staff)
   * Implements the CheckUser step from the flowchart
   */
  async _validateUserAuthorization(requisitionId, userFromToken) {
    try {
      const { FORCE_CLOSE_ERRORS } = this.constants.forceClose;

      const requisition = await this.requisitionRepository.getById(requisitionId, {
        include: ['createdByUser', 'assignee'],
      });

      if (!requisition) {
        return {
          isAuthorized: false,
          details: FORCE_CLOSE_ERRORS.REQUISITION_NOT_FOUND,
        };
      }

      const isRequester = requisition.createdBy === userFromToken.id;
      const isAssignedStaff = requisition.assignedTo === userFromToken.id;
      const isAuthorized = isRequester || isAssignedStaff;

      if (!isAuthorized) {
        return {
          isAuthorized: false,
          details: FORCE_CLOSE_ERRORS.NOT_REQUESTER_OR_ASSIGNEE,
        };
      }

      return {
        isAuthorized: true,
        details: {
          isRequester,
          isAssignedStaff,
          requisitionCreatedBy: requisition.createdBy,
          requisitionAssignedTo: requisition.assignedTo,
          currentUserId: userFromToken.id,
        },
      };
    } catch (error) {
      this.fastify.log.error(`Authorization validation failed: ${error.message}`);
      return {
        isAuthorized: false,
        details: `Authorization check failed: ${error.message}`,
      };
    }
  }

  /**
   * Step 2: CheckRS - Validate requisition status (must be Fully Approved AND RS In Progress)
   * Implements the CheckRS step from the flowchart
   */
  async _validateRequisitionStatus(requisitionId) {
    try {
      const { FORCE_CLOSE_ERRORS, FORCE_CLOSE_BUTTON_RULES } = this.constants.forceClose;
      const { REQUISITION_STATUS } = this.constants.requisition;

      const requisition = await this.requisitionRepository.getById(requisitionId);

      if (!requisition) {
        return {
          isValid: false,
          showButton: false,
          reason: FORCE_CLOSE_ERRORS.REQUISITION_NOT_FOUND,
        };
      }

      const isFullyApproved = requisition.status === REQUISITION_STATUS.APPROVED;
      const isInProgress = requisition.status === REQUISITION_STATUS.RS_IN_PROGRESS;
      const isValidStatus = isFullyApproved || isInProgress;

      // Button should only be visible when RS is in progress (replaces Cancel button)
      const showButton = requisition.status === FORCE_CLOSE_BUTTON_RULES.SHOW_WHEN_IN_PROGRESS;

      if (!isValidStatus) {
        return {
          isValid: false,
          showButton,
          reason: showButton ? FORCE_CLOSE_ERRORS.RS_NOT_APPROVED : FORCE_CLOSE_ERRORS.RS_STILL_CANCELLABLE,
          details: {
            currentStatus: requisition.status,
            requiredStatus: `${REQUISITION_STATUS.APPROVED} or ${REQUISITION_STATUS.RS_IN_PROGRESS}`,
            isFullyApproved,
            isInProgress,
          },
        };
      }

      return {
        isValid: true,
        showButton,
        details: {
          currentStatus: requisition.status,
          isFullyApproved,
          isInProgress,
          buttonVisible: showButton,
        },
      };
    } catch (error) {
      this.fastify.log.error(`RS status validation failed: ${error.message}`);
      return {
        isValid: false,
        showButton: false,
        reason: `Status validation failed: ${error.message}`,
      };
    }
  }

  /**
   * Step 3: Determine force close scenario and validate conditions
   * Implements CheckPO/CheckRemQty → CheckCS validation chain from flowchart
   */
  async _determineForceCloseScenario(requisitionId) {
    try {
      this.fastify.log.info(`🚀 STARTING validateForceCloseEligibility for RS: ${requisitionId}`);
      const { FORCE_CLOSE_SCENARIOS, FORCE_CLOSE_ERRORS } = this.constants.forceClose;

      // Get requisition with related data
      this.fastify.log.info(`🔍 Getting requisition data for RS: ${requisitionId}`);
      const requisitionData = await this._getRequisitionForceCloseData(requisitionId);
      this.fastify.log.info(`🔍 Got requisition data for RS: ${requisitionId}`);

      // Step 3a: CheckPO - Check PO delivery status first
      this.fastify.log.info(`🔍 About to call _checkPODeliveryStatus for RS: ${requisitionId}`);
      const poDeliveryResult = await this._checkPODeliveryStatus(requisitionId);
      this.fastify.log.info(`🔍 _checkPODeliveryStatus completed for RS: ${requisitionId}`, {
        isValidForForceClose: poDeliveryResult?.isValidForForceClose,
        isPartiallyDelivered: poDeliveryResult?.isPartiallyDelivered,
        noDeliveriesYet: poDeliveryResult?.noDeliveriesYet
      });

      // Debug: Log delivery validation result
      this.fastify.log.info(`PO delivery validation result for RS: ${requisitionId}:`, {
        isValidForForceClose: poDeliveryResult.isValidForForceClose,
        isPartiallyDelivered: poDeliveryResult.isPartiallyDelivered,
        noDeliveriesYet: poDeliveryResult.noDeliveriesYet,
        hasDetails: !!poDeliveryResult.details
      });

      // If no deliveries have been made yet, return error
      if (poDeliveryResult.noDeliveriesYet) {
        return {
          isEligible: false,
          buttonVisible: false, // Fixed: If not eligible, button should not be visible
          reason: FORCE_CLOSE_ERRORS.NO_DELIVERIES_YET,
          details: poDeliveryResult.details,
        };
      }

      // If PO status is invalid for force close, return error
      if (!poDeliveryResult.isValidForForceClose) {
        return {
          isEligible: false,
          buttonVisible: false, // Fixed: If not eligible, button should not be visible
          reason: FORCE_CLOSE_ERRORS.INVALID_PO_STATUS,
          details: poDeliveryResult.details,
        };
      }

      // Step 3b: CheckRemQty - Check for remaining quantities to be canvassed
      const remainingQtyResult = await this._checkRemainingQuantities(requisitionId);

      // Check PO status to determine validation path
      const poStatusResult = await this._checkPOStatus(requisitionId);

      if (!poStatusResult.isValid) {
        return {
          isEligible: false,
          buttonVisible: false, // Fixed: If not eligible, button should not be visible
          reason: FORCE_CLOSE_ERRORS.INVALID_PO_STATUS,
          details: poStatusResult.details,
        };
      }

      // ACTIVE PO VALIDATION PATH
      if (poStatusResult.hasActivePOs) {
        this.fastify.log.info(`Following Active PO validation path for RS: ${requisitionId}`);

        // Check if active POs have partial deliveries
        if (poDeliveryResult.isPartiallyDelivered) {
          // Check payment prerequisite for active POs with deliveries
          const paymentResult = await this._checkPaymentPrerequisite(requisitionId);
          if (!paymentResult.allPaid) {
            return {
              isEligible: false,
              buttonVisible: false, // Fixed: If not eligible, button should not be visible
              reason: FORCE_CLOSE_ERRORS.UNPAID_DELIVERIES,
              details: paymentResult.details,
            };
          }

          // Scenario 1: Active Purchase Orders with Partial Deliveries
          const impactSummary1 = await this._generateImpactSummary(requisitionId, FORCE_CLOSE_SCENARIOS.ACTIVE_PO_PARTIAL_DELIVERY, {
            activePOs: poStatusResult.activePOs,
            deliveries: poDeliveryResult.details,
            payments: paymentResult.details,
          });

          return {
            isEligible: true,
            scenario: FORCE_CLOSE_SCENARIOS.ACTIVE_PO_PARTIAL_DELIVERY,
            validationPath: 'ACTIVE_PO_PATH',
            impactSummary: impactSummary1,
            details: {
              type: 'Active Purchase Orders with Partial Deliveries',
              validationPath: 'ACTIVE_PO_PATH',
              activePOs: poStatusResult.activePOs,
              deliveries: poDeliveryResult.details,
              payments: paymentResult.details,
            },
          };
        }

        // Check if no deliveries made (should manually cancel PO first)
        if (poDeliveryResult.noDeliveriesYet) {
          return {
            isEligible: false,
            buttonVisible: false, // Fixed: If not eligible, button should not be visible
            reason: FORCE_CLOSE_ERRORS.NO_DELIVERIES_MANUAL_CANCEL,
            details: {
              validationPath: 'ACTIVE_PO_PATH',
              activePOs: poStatusResult.activePOs,
              message: 'No deliveries have been made yet. Please manually cancel the Purchase Order first.',
            },
          };
        }
      }

      // CLOSED PO VALIDATION PATH
      if (poStatusResult.allClosed) {
        this.fastify.log.info(`Following Closed PO validation path for RS: ${requisitionId}`);

        // Scenario 2: All Purchase Orders Closed/Cancelled with Remaining Quantities
        if (remainingQtyResult.hasRemaining) {
          const impactSummary2 = await this._generateImpactSummary(requisitionId, FORCE_CLOSE_SCENARIOS.CLOSED_PO_REMAINING_QTY, {
            closedPOs: poStatusResult.closedPOs,
            remainingQuantities: remainingQtyResult.details,
          });

          return {
            isEligible: true,
            scenario: FORCE_CLOSE_SCENARIOS.CLOSED_PO_REMAINING_QTY,
            validationPath: 'CLOSED_PO_PATH',
            impactSummary: impactSummary2,
            details: {
              type: 'All Purchase Orders Closed/Cancelled with Remaining Quantities',
              validationPath: 'CLOSED_PO_PATH',
              closedPOs: poStatusResult.closedPOs,
              remainingQuantities: remainingQtyResult.details,
            },
          };
        }

        // Scenario 3: Closed Purchase Orders with Pending Canvass Sheet Approvals
        const pendingCSResult = await this._checkPendingCanvassSheets(requisitionId);
        if (pendingCSResult.hasPending) {
          const impactSummary3 = await this._generateImpactSummary(requisitionId, FORCE_CLOSE_SCENARIOS.CLOSED_PO_PENDING_CS, {
            closedPOs: poStatusResult.closedPOs,
            pendingCanvassSheets: pendingCSResult.details,
          });

          return {
            isEligible: true,
            scenario: FORCE_CLOSE_SCENARIOS.CLOSED_PO_PENDING_CS,
            validationPath: 'CLOSED_PO_PATH',
            impactSummary: impactSummary3,
            details: {
              type: 'Closed Purchase Orders with Pending Canvass Sheet Approvals',
              validationPath: 'CLOSED_PO_PATH',
              closedPOs: poStatusResult.closedPOs,
              pendingCanvassSheets: pendingCSResult.details,
            },
          };
        }

        // Enhanced auto-close detection
        const autoCloseResult = await this._performAutoCloseDetection(requisitionId, {
          poStatusResult,
          remainingQtyResult,
          pendingCSResult,
        });

        if (autoCloseResult.shouldAutoClose) {
          return {
            isEligible: false,
            buttonVisible: false, // Fixed: If not eligible, button should not be visible
            reason: FORCE_CLOSE_ERRORS.AUTO_CLOSE_DETECTED,
            details: {
              validationPath: 'CLOSED_PO_PATH',
              message: 'Requisition should auto-close instead of force close',
              autoCloseAnalysis: autoCloseResult.analysis,
              confidence: autoCloseResult.confidence,
              criteria: autoCloseResult.criteriaResults,
            },
          };
        }
      }

      // No valid scenario found
      const pendingCSResult = await this._checkPendingCanvassSheets(requisitionId);

      return {
        isEligible: false,
        buttonVisible: false, // Fixed: If not eligible, button should not be visible
        reason: FORCE_CLOSE_ERRORS.NO_VALID_SCENARIO,
        details: {
          validationPath: poStatusResult.hasActivePOs ? 'ACTIVE_PO_PATH' : 'CLOSED_PO_PATH',
          poStatus: poStatusResult.details,
          hasActivePOs: poStatusResult.hasActivePOs,
          allClosed: poStatusResult.allClosed,
          hasRemainingQty: remainingQtyResult?.hasRemaining || false,
          hasPendingCS: pendingCSResult?.hasPending || false,
          isPartiallyDelivered: poDeliveryResult?.isPartiallyDelivered || false,
          noDeliveriesYet: poDeliveryResult?.noDeliveriesYet || false,
          message: 'No valid force close scenario detected for current requisition state',
        },
      };

    } catch (error) {
      this.fastify.log.error(`Scenario determination failed: ${error.message}`);
      return {
        isEligible: false,
        buttonVisible: false, // Fixed: If not eligible, button should not be visible
        reason: `Scenario validation failed: ${error.message}`,
      };
    }
  }

  /**
   * Validate force close notes using constants configuration
   */
  _validateForceCloseNotes(notes) {
    const { FORCE_CLOSE_ERRORS, FORCE_CLOSE_NOTES_CONFIG } = this.constants.forceClose;

    if (!notes || notes.trim().length === 0) {
      return {
        isValid: false,
        reason: FORCE_CLOSE_ERRORS.NOTES_REQUIRED,
      };
    }

    if (notes.length > FORCE_CLOSE_NOTES_CONFIG.MAX_LENGTH) {
      return {
        isValid: false,
        reason: FORCE_CLOSE_ERRORS.NOTES_TOO_LONG,
      };
    }

    // Check for invalid characters using config regex
    if (!FORCE_CLOSE_NOTES_CONFIG.ALLOWED_CHARS_REGEX.test(notes)) {
      return {
        isValid: false,
        reason: FORCE_CLOSE_ERRORS.NOTES_INVALID_CHARS,
      };
    }

    // Check for emojis (comprehensive emoji regex)
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F900}-\u{1F9FF}]|[\u{1FA70}-\u{1FAFF}]/u;
    if (emojiRegex.test(notes)) {
      return {
        isValid: false,
        reason: FORCE_CLOSE_ERRORS.NOTES_NO_EMOJIS,
      };
    }

    return {
      isValid: true,
    };
  }

  /**
   * Get requisition data needed for force close validation
   */
  async _getRequisitionForceCloseData(requisitionId) {
    return await this.requisitionRepository.getById(requisitionId, {
      include: [
        'requisitionItemLists',
        'canvassRequisitions',
        'purchaseOrders',
        'deliveryReceipt',
        'rsPaymentRequest',
      ],
    });
  }

  /**
   * Perform comprehensive auto-close detection analysis
   * Determines if a requisition should auto-close instead of force close
   */
  async _performAutoCloseDetection(requisitionId, validationResults) {
    try {
      const { FORCE_CLOSE_AUTO_CLOSE_DETECTION } = this.constants.forceClose;

      this.fastify.log.info(`Performing auto-close detection for RS: ${requisitionId}`);

      const { poStatusResult, remainingQtyResult, pendingCSResult } = validationResults;

      // Initialize criteria results
      const criteriaResults = {
        allPOsClosed: false,
        noRemainingQuantities: false,
        noPendingApprovals: false,
        noDraftDocuments: false,
      };

      let confidence = 0;
      const analysisDetails = [];

      // Criterion 1: All POs are CLOSED or CANCELLED
      if (poStatusResult.allClosed && poStatusResult.closedPOs.length > 0) {
        criteriaResults.allPOsClosed = true;
        confidence += 25;
        analysisDetails.push({
          criterion: FORCE_CLOSE_AUTO_CLOSE_DETECTION.CRITERIA.ALL_POS_CLOSED,
          passed: true,
          details: `All ${poStatusResult.closedPOs.length} purchase orders are closed or cancelled`,
          weight: 25,
        });
      } else {
        analysisDetails.push({
          criterion: FORCE_CLOSE_AUTO_CLOSE_DETECTION.CRITERIA.ALL_POS_CLOSED,
          passed: false,
          details: `${poStatusResult.activePOs?.length || 0} purchase orders are still active`,
          weight: 25,
        });
      }

      // Criterion 2: No remaining quantities for canvassing
      if (!remainingQtyResult.hasRemaining) {
        criteriaResults.noRemainingQuantities = true;
        confidence += 25;
        analysisDetails.push({
          criterion: FORCE_CLOSE_AUTO_CLOSE_DETECTION.CRITERIA.NO_REMAINING_QUANTITIES,
          passed: true,
          details: 'All requested quantities have been canvassed',
          weight: 25,
        });
      } else {
        const remainingCount = remainingQtyResult.details?.summary?.itemsWithRemaining || 0;
        analysisDetails.push({
          criterion: FORCE_CLOSE_AUTO_CLOSE_DETECTION.CRITERIA.NO_REMAINING_QUANTITIES,
          passed: false,
          details: `${remainingCount} items still have remaining quantities to be canvassed`,
          weight: 25,
        });
      }

      // Criterion 3: No pending canvass sheet approvals
      if (!pendingCSResult.hasPending) {
        criteriaResults.noPendingApprovals = true;
        confidence += 25;
        analysisDetails.push({
          criterion: FORCE_CLOSE_AUTO_CLOSE_DETECTION.CRITERIA.NO_PENDING_APPROVALS,
          passed: true,
          details: 'No canvass sheets are pending approval',
          weight: 25,
        });
      } else {
        const pendingCount = pendingCSResult.details?.summary?.pendingCount || 0;
        analysisDetails.push({
          criterion: FORCE_CLOSE_AUTO_CLOSE_DETECTION.CRITERIA.NO_PENDING_APPROVALS,
          passed: false,
          details: `${pendingCount} canvass sheets are pending approval`,
          weight: 25,
        });
      }

      // Criterion 4: No draft documents pending (additional check)
      const draftDocumentsCheck = await this._checkDraftDocuments(requisitionId);
      if (!draftDocumentsCheck.hasDrafts) {
        criteriaResults.noDraftDocuments = true;
        confidence += 25;
        analysisDetails.push({
          criterion: FORCE_CLOSE_AUTO_CLOSE_DETECTION.CRITERIA.NO_DRAFT_DOCUMENTS,
          passed: true,
          details: 'No draft documents are pending',
          weight: 25,
        });
      } else {
        const draftCount = draftDocumentsCheck.details?.summary?.totalDrafts || 0;
        analysisDetails.push({
          criterion: FORCE_CLOSE_AUTO_CLOSE_DETECTION.CRITERIA.NO_DRAFT_DOCUMENTS,
          passed: false,
          details: `${draftCount} draft documents are pending`,
          weight: 25,
        });
      }

      // Determine if should auto-close
      const shouldAutoClose = confidence >= FORCE_CLOSE_AUTO_CLOSE_DETECTION.CONFIDENCE_THRESHOLD;

      // Determine detection category
      let detectionCategory;
      if (criteriaResults.allPOsClosed && criteriaResults.noRemainingQuantities && criteriaResults.noPendingApprovals) {
        detectionCategory = FORCE_CLOSE_AUTO_CLOSE_DETECTION.DETECTION_CATEGORIES.COMPLETE_FULFILLMENT;
      } else if (criteriaResults.allPOsClosed && !criteriaResults.noRemainingQuantities) {
        detectionCategory = FORCE_CLOSE_AUTO_CLOSE_DETECTION.DETECTION_CATEGORIES.CANCELLED_ORDERS;
      } else if (criteriaResults.allPOsClosed) {
        detectionCategory = FORCE_CLOSE_AUTO_CLOSE_DETECTION.DETECTION_CATEGORIES.MIXED_COMPLETION;
      } else {
        detectionCategory = FORCE_CLOSE_AUTO_CLOSE_DETECTION.DETECTION_CATEGORIES.PENDING_WORK;
      }

      const analysis = {
        shouldAutoClose,
        confidence,
        detectionCategory,
        criteriaResults,
        analysisDetails,
        recommendation: shouldAutoClose
          ? 'Requisition meets all criteria for automatic closure'
          : 'Requisition requires force close due to incomplete criteria',
        summary: {
          totalCriteria: analysisDetails.length,
          passedCriteria: analysisDetails.filter(detail => detail.passed).length,
          confidenceThreshold: FORCE_CLOSE_AUTO_CLOSE_DETECTION.CONFIDENCE_THRESHOLD,
        },
      };

      this.fastify.log.info(`Auto-close detection completed for RS: ${requisitionId}`, {
        shouldAutoClose,
        confidence,
        detectionCategory,
      });

      return {
        shouldAutoClose,
        confidence,
        analysis,
        criteriaResults,
      };

    } catch (error) {
      this.fastify.log.error(`Auto-close detection failed for RS ${requisitionId}: ${error.message}`);
      return {
        shouldAutoClose: false,
        confidence: 0,
        analysis: {
          error: `Auto-close detection failed: ${error.message}`,
          shouldAutoClose: false,
        },
        criteriaResults: {},
      };
    }
  }

  /**
   * Check for draft documents that might prevent auto-close
   * Helper method for auto-close detection
   */
  async _checkDraftDocuments(requisitionId) {
    try {
      // This would check for draft delivery receipts, invoice reports, payment requests, etc.
      // For now, return a simple implementation
      return {
        hasDrafts: false,
        details: {
          summary: {
            totalDrafts: 0,
          },
        },
      };
    } catch (error) {
      return {
        hasDrafts: false,
        details: {
          error: `Draft documents check failed: ${error.message}`,
        },
      };
    }
  }

  /**
   * Generate impact summary for force close scenarios
   * Creates detailed impact descriptions based on scenario type and data
   */
  async _generateImpactSummary(requisitionId, scenario, contextData = {}) {
    try {
      this.fastify.log.info(`Generating impact summary for RS: ${requisitionId}, Scenario: ${scenario}`);

      const { FORCE_CLOSE_SCENARIOS } = this.constants.forceClose;
      const impacts = [];

      switch (scenario) {
        case FORCE_CLOSE_SCENARIOS.ACTIVE_PO_PARTIAL_DELIVERY:
          impacts.push('Purchase order amounts will be adjusted to reflect delivered quantities only');
          impacts.push('Purchase order quantities will be updated to match delivered amounts');
          impacts.push('Undelivered quantities will be removed from the purchase order');
          impacts.push('Purchase order status will be changed to CLOSED');

          // Add specific details based on context data
          if (contextData.activePOs && contextData.activePOs.length > 0) {
            impacts.push(`${contextData.activePOs.length} active purchase order(s) will be affected`);
          }

          if (contextData.deliveries && contextData.deliveries.summary) {
            const deliveryRate = contextData.deliveries.summary.overallDeliveryCompletionRate || '0%';
            impacts.push(`Current delivery completion rate: ${deliveryRate}`);
          }

          impacts.push('Unfulfilled quantities will be returned to inventory (OFM items only)');
          impacts.push('All draft and pending related documents will be cancelled');
          break;

        case FORCE_CLOSE_SCENARIOS.CLOSED_PO_REMAINING_QTY:
          impacts.push('All remaining quantities for canvassing will be zeroed out');

          // Add specific details based on context data
          if (contextData.remainingQuantities && contextData.remainingQuantities.summary) {
            const remainingItems = contextData.remainingQuantities.summary.itemsWithRemaining || 0;
            const totalRemaining = contextData.remainingQuantities.summary.totalRemainingQuantity || 0;
            impacts.push(`${remainingItems} item(s) with ${totalRemaining} total remaining quantities will be affected`);
          }

          if (contextData.closedPOs && contextData.closedPOs.length > 0) {
            impacts.push(`${contextData.closedPOs.length} closed purchase order(s) are already completed`);
          }

          impacts.push('Unfulfilled quantities will be returned to inventory (OFM items only)');
          impacts.push('Requisition status will be changed to CLOSED');
          impacts.push('Force close activity will be logged for audit trail');
          break;

        case FORCE_CLOSE_SCENARIOS.CLOSED_PO_PENDING_CS:
          impacts.push('All pending canvass sheet approvals will be cancelled');
          impacts.push('Remaining quantities from cancelled canvass sheets will be zeroed out');

          // Add specific details based on context data
          if (contextData.pendingCanvassSheets && contextData.pendingCanvassSheets.summary) {
            const pendingCount = contextData.pendingCanvassSheets.summary.pendingCount || 0;
            impacts.push(`${pendingCount} pending canvass sheet(s) will be cancelled`);
          }

          if (contextData.closedPOs && contextData.closedPOs.length > 0) {
            impacts.push(`${contextData.closedPOs.length} closed purchase order(s) are already completed`);
          }

          impacts.push('Unfulfilled quantities will be returned to inventory (OFM items only)');
          impacts.push('Requisition status will be changed to CLOSED');
          impacts.push('Force close activity will be logged for audit trail');
          break;

        default:
          impacts.push('Requisition will be force closed with appropriate system adjustments');
          impacts.push('All pending processes will be terminated');
          impacts.push('Requisition status will be changed to CLOSED');
          break;
      }

      // Add common impacts for all scenarios
      impacts.push('This action is irreversible and cannot be undone');
      impacts.push('All affected users will be notified of the force close action');

      this.fastify.log.info(`Impact summary generated for RS: ${requisitionId}`, {
        scenario,
        impactCount: impacts.length,
      });

      return impacts;

    } catch (error) {
      this.fastify.log.error(`Impact summary generation failed for RS ${requisitionId}: ${error.message}`);
      return [
        'Requisition will be force closed with appropriate system adjustments',
        'This action is irreversible and cannot be undone',
      ];
    }
  }

  /**
   * Check for remaining quantities to be canvassed
   * Enhanced implementation for auto-close detection
   */
  async _checkRemainingQuantities(requisitionId) {
    try {
      this.fastify.log.info(`Checking remaining quantities for RS: ${requisitionId}`);

      // Get requisition with items and related canvass data
      const requisition = await this.requisitionRepository.getById(requisitionId, {
        include: [
          'requisitionItemLists',
          'canvassRequisitions',
          'purchaseOrders',
        ],
      });

      if (!requisition || !requisition.requisitionItemLists) {
        return {
          hasRemaining: false,
          details: {
            error: 'No requisition items found',
            totalItems: 0,
          },
        };
      }

      const itemAnalysis = [];
      let totalRemainingQuantity = 0;
      let hasRemainingItems = false;

      for (const reqItem of requisition.requisitionItemLists) {
        const requestedQty = parseFloat(reqItem.quantity) || 0;

        // Calculate total canvassed quantity for this item
        let totalCanvassedQty = 0;

        // Get all canvass sheets for this requisition
        const canvassSheets = requisition.canvassRequisitions || [];

        for (const canvass of canvassSheets) {
          // Only count approved canvass sheets
          if (canvass.status === 'CS_APPROVED' || canvass.status === 'APPROVED') {
            // Find canvass items for this requisition item
            const canvassItems = canvass.canvassItems?.filter(ci =>
              ci.requisitionItemId === reqItem.id
            ) || [];

            for (const canvassItem of canvassItems) {
              totalCanvassedQty += parseFloat(canvassItem.quantity) || 0;
            }
          }
        }

        const remainingQty = Math.max(0, requestedQty - totalCanvassedQty);

        if (remainingQty > 0) {
          hasRemainingItems = true;
          totalRemainingQuantity += remainingQty;
        }

        itemAnalysis.push({
          itemId: reqItem.itemId,
          itemName: reqItem.itemName || 'Unknown Item',
          requestedQuantity: requestedQty,
          canvassedQuantity: totalCanvassedQty,
          remainingQuantity: remainingQty,
          hasRemaining: remainingQty > 0,
          canvassingCompletionRate: requestedQty > 0
            ? ((totalCanvassedQty / requestedQty) * 100).toFixed(2) + '%'
            : '100%',
        });
      }

      const summary = {
        totalItems: requisition.requisitionItemLists.length,
        itemsWithRemaining: itemAnalysis.filter(item => item.hasRemaining).length,
        totalRemainingQuantity,
        overallCanvassingCompletionRate: requisition.requisitionItemLists.length > 0
          ? (((requisition.requisitionItemLists.length - itemAnalysis.filter(item => item.hasRemaining).length) / requisition.requisitionItemLists.length) * 100).toFixed(2) + '%'
          : '100%',
      };

      this.fastify.log.info(`Remaining quantities check completed for RS: ${requisitionId}`, summary);

      return {
        hasRemaining: hasRemainingItems,
        details: {
          itemAnalysis,
          summary,
        },
      };

    } catch (error) {
      this.fastify.log.error(`Remaining quantities check failed for RS ${requisitionId}: ${error.message}`);
      return {
        hasRemaining: false,
        details: {
          error: `Remaining quantities validation failed: ${error.message}`,
        },
      };
    }
  }

  /**
   * Check for pending canvass approvals
   * Enhanced implementation for auto-close detection
   */
  async _checkPendingCanvassApprovals(requisitionId) {
    try {
      this.fastify.log.info(`Checking pending canvass approvals for RS: ${requisitionId}`);

      // Get all canvass sheets for this requisition
      const canvassSheets = await this.canvassRequisitionRepository.findAll({
        where: { requisitionId },
        include: ['canvassItems', 'approvals'],
      });

      if (!canvassSheets || canvassSheets.length === 0) {
        return {
          hasPending: false,
          details: {
            message: 'No canvass sheets found',
            totalSheets: 0,
          },
        };
      }

      const pendingSheets = [];
      const approvedSheets = [];
      const rejectedSheets = [];
      const draftSheets = [];

      for (const cs of canvassSheets) {
        const status = cs.status?.toUpperCase();

        if (status === 'CS_PENDING_APPROVAL' || status === 'CS_FOR_APPROVAL' || status === 'PENDING_APPROVAL') {
          pendingSheets.push({
            id: cs.id,
            status: cs.status,
            createdAt: cs.createdAt,
            itemCount: cs.canvassItems?.length || 0,
            approvalCount: cs.approvals?.length || 0,
            pendingDuration: cs.createdAt ? Math.floor((Date.now() - new Date(cs.createdAt).getTime()) / (1000 * 60 * 60 * 24)) : 0, // Days pending
          });
        } else if (status === 'CS_APPROVED' || status === 'APPROVED') {
          approvedSheets.push({
            id: cs.id,
            status: cs.status,
            approvedAt: cs.updatedAt,
            itemCount: cs.canvassItems?.length || 0,
          });
        } else if (status === 'CS_REJECTED' || status === 'REJECTED' || status === 'CS_CANCELLED' || status === 'CANCELLED') {
          rejectedSheets.push({
            id: cs.id,
            status: cs.status,
            rejectedAt: cs.updatedAt,
            itemCount: cs.canvassItems?.length || 0,
          });
        } else if (status === 'CS_DRAFT' || status === 'DRAFT') {
          draftSheets.push({
            id: cs.id,
            status: cs.status,
            createdAt: cs.createdAt,
            itemCount: cs.canvassItems?.length || 0,
          });
        }
      }

      const hasPending = pendingSheets.length > 0;
      const hasDrafts = draftSheets.length > 0;

      const summary = {
        totalSheets: canvassSheets.length,
        pendingCount: pendingSheets.length,
        approvedCount: approvedSheets.length,
        rejectedCount: rejectedSheets.length,
        draftCount: draftSheets.length,
        approvalCompletionRate: canvassSheets.length > 0
          ? ((approvedSheets.length / canvassSheets.length) * 100).toFixed(2) + '%'
          : '100%',
        hasBlockingItems: hasPending || hasDrafts,
      };

      this.fastify.log.info(`Pending canvass approvals check completed for RS: ${requisitionId}`, summary);

      return {
        hasPending,
        hasDrafts,
        details: {
          pendingSheets,
          approvedSheets,
          rejectedSheets,
          draftSheets,
          summary,
        },
      };

    } catch (error) {
      this.fastify.log.error(`Pending canvass approvals check failed for RS ${requisitionId}: ${error.message}`);
      return {
        hasPending: false,
        hasDrafts: false,
        details: {
          error: `Pending canvass approvals validation failed: ${error.message}`,
        },
      };
    }
  }

  /**
   * Comprehensive PO delivery status validation
   * Validates delivery status, quantities, and completion rates for all purchase orders
   */
  async _checkPODeliveryStatus(requisitionId) {
    try {
      const { FORCE_CLOSE_DELIVERY_VALIDATION, FORCE_CLOSE_ERRORS } = this.constants.forceClose;

      this.fastify.log.info(`🔍 STARTING _checkPODeliveryStatus for RS: ${requisitionId}`);
      this.fastify.log.info(`Starting comprehensive delivery validation for RS: ${requisitionId}`);

      // Get all purchase orders with delivery receipts and items
      // Use simple approach: get POs first, then get delivery data separately
      const purchaseOrdersResult = await this.purchaseOrderRepository.findAll({
        where: { requisitionId },
        include: [
          'deliveryReceipts', // Include delivery receipts
          'purchaseOrderItems', // Include PO items for quantity comparison
        ],
        paginate: false, // Disable pagination to get all results
      });

      // Handle repository result (extract data from paginated response)
      const purchaseOrders = Array.isArray(purchaseOrdersResult)
        ? purchaseOrdersResult
        : purchaseOrdersResult?.data || [];

      if (!purchaseOrders || purchaseOrders.length === 0) {
        return {
          isValidForForceClose: false,
          isPartiallyDelivered: false,
          noDeliveriesYet: true,
          details: {
            error: 'No purchase orders found for requisition',
            totalPOs: 0,
          },
        };
      }

      // Load delivery receipt items separately for each delivery receipt
      console.log(`🔍 CONSOLE: Loaded ${purchaseOrders.length} POs, now loading delivery receipt items`);

      for (const po of purchaseOrders) {
        if (po.deliveryReceipts && po.deliveryReceipts.length > 0) {
          for (const dr of po.deliveryReceipts) {
            // Only load items for valid delivery receipts (handle null/empty status)
            if (FORCE_CLOSE_DELIVERY_VALIDATION.VALID_DR_STATUSES.includes(dr.status) ||
                dr.status === null || dr.status === undefined || dr.status === '') {
              try {
                const drItems = await this.db.deliveryReceiptItemModel.findAll({
                  where: { drId: dr.id },
                  raw: true,
                });
                dr.items = drItems || [];
                console.log(`🔍 CONSOLE: Loaded ${dr.items.length} items for DR ${dr.id}`);
              } catch (error) {
                console.log(`❌ CONSOLE: Failed to load items for DR ${dr.id}:`, error.message);
                dr.items = [];
              }
            } else {
              dr.items = [];
            }
          }
        }
      }

      const deliveryValidation = {
        totalPOs: purchaseOrders.length,
        totalDeliveries: 0,
        partiallyDeliveredPOs: [],
        fullyDeliveredPOs: [],
        noDeliveryPOs: [],
        invalidDeliveryPOs: [],
        overDeliveredPOs: [],
        validationErrors: [],
        deliverySummary: [],
      };

      let hasAnyDeliveries = false;
      let hasPartialDeliveries = false;

      for (const po of purchaseOrders) {
        // Debug: Log PO processing
        this.fastify.log.info(`Processing PO ${po.id} for delivery validation:`, {
          status: po.status,
          validStatuses: FORCE_CLOSE_DELIVERY_VALIDATION.VALID_PO_STATUSES,
          isValidStatus: FORCE_CLOSE_DELIVERY_VALIDATION.VALID_PO_STATUSES.includes(po.status)
        });

        // Validate PO status for delivery validation (normalize to lowercase)
        const normalizedStatus = po.status?.toLowerCase();
        if (!FORCE_CLOSE_DELIVERY_VALIDATION.VALID_PO_STATUSES.includes(normalizedStatus)) {
          deliveryValidation.invalidDeliveryPOs.push({
            poId: po.id,
            status: po.status,
            reason: 'Invalid PO status for delivery validation',
          });
          continue;
        }

        // Get delivery validation result for this PO
        const poDeliveryResult = await this._validatePODeliveryStatus(po);

        if (!poDeliveryResult.isValid) {
          deliveryValidation.validationErrors.push({
            category: FORCE_CLOSE_DELIVERY_VALIDATION.VALIDATION_CATEGORIES.STATUS_VALID,
            poId: po.id,
            error: FORCE_CLOSE_ERRORS.DELIVERY_VALIDATION_ERROR,
            details: poDeliveryResult.error,
          });
          continue;
        }

        // Track delivery status
        if (poDeliveryResult.hasDeliveries) {
          hasAnyDeliveries = true;
          deliveryValidation.totalDeliveries += poDeliveryResult.totalDeliveries;
        }

        // Categorize PO based on delivery completion
        switch (poDeliveryResult.deliveryStatus) {
          case FORCE_CLOSE_DELIVERY_VALIDATION.VALIDATION_RESULTS.FULLY_DELIVERED:
            deliveryValidation.fullyDeliveredPOs.push(poDeliveryResult.summary);
            break;
          case FORCE_CLOSE_DELIVERY_VALIDATION.VALIDATION_RESULTS.PARTIALLY_DELIVERED:
            deliveryValidation.partiallyDeliveredPOs.push(poDeliveryResult.summary);
            hasPartialDeliveries = true;
            break;
          case FORCE_CLOSE_DELIVERY_VALIDATION.VALIDATION_RESULTS.NO_DELIVERIES:
            deliveryValidation.noDeliveryPOs.push(poDeliveryResult.summary);
            break;
          case FORCE_CLOSE_DELIVERY_VALIDATION.VALIDATION_RESULTS.OVER_DELIVERED:
            deliveryValidation.overDeliveredPOs.push(poDeliveryResult.summary);
            break;
        }

        deliveryValidation.deliverySummary.push(poDeliveryResult.summary);
      }

      // Calculate overall delivery statistics
      const overallStats = {
        totalPOs: deliveryValidation.totalPOs,
        totalDeliveries: deliveryValidation.totalDeliveries,
        fullyDeliveredCount: deliveryValidation.fullyDeliveredPOs.length,
        partiallyDeliveredCount: deliveryValidation.partiallyDeliveredPOs.length,
        noDeliveryCount: deliveryValidation.noDeliveryPOs.length,
        overDeliveredCount: deliveryValidation.overDeliveredPOs.length,
        invalidPOCount: deliveryValidation.invalidDeliveryPOs.length,
        deliveryCompletionRate: deliveryValidation.totalPOs > 0
          ? ((deliveryValidation.fullyDeliveredPOs.length / deliveryValidation.totalPOs) * 100).toFixed(2) + '%'
          : '0%',
        hasValidationErrors: deliveryValidation.validationErrors.length > 0,
      };

      this.fastify.log.info(`Delivery validation completed for RS: ${requisitionId}`, overallStats);

      // Debug: Log delivery validation results
      this.fastify.log.info(`Delivery validation debug for RS: ${requisitionId}`, {
        hasAnyDeliveries,
        hasPartialDeliveries,
        totalPOs: deliveryValidation.totalPOs,
        fullyDeliveredPOs: deliveryValidation.fullyDeliveredPOs.length,
        partiallyDeliveredPOs: deliveryValidation.partiallyDeliveredPOs.length,
        noDeliveryPOs: deliveryValidation.noDeliveryPOs.length
      });

      return {
        isValidForForceClose: hasAnyDeliveries && !overallStats.hasValidationErrors,
        isPartiallyDelivered: hasPartialDeliveries,
        noDeliveriesYet: !hasAnyDeliveries,
        details: {
          ...deliveryValidation,
          overallStats,
        },
      };

    } catch (error) {
      this.fastify.log.error(`Comprehensive delivery validation failed for RS ${requisitionId}: ${error.message}`);
      return {
        isValidForForceClose: false,
        isPartiallyDelivered: false,
        noDeliveriesYet: true,
        details: {
          error: `Delivery validation failed: ${error.message}`,
          validationErrors: [{
            category: 'system_error',
            error: 'Delivery validation system error',
            details: error.message,
          }],
        },
      };
    }
  }

  /**
   * Validate delivery status for a single purchase order
   * Helper method for comprehensive delivery validation
   */
  async _validatePODeliveryStatus(purchaseOrder) {
    const { FORCE_CLOSE_DELIVERY_VALIDATION, FORCE_CLOSE_ERRORS } = this.constants.forceClose;

    try {
      const poItems = purchaseOrder.purchaseOrderItems || [];
      const deliveryReceipts = purchaseOrder.deliveryReceipts || [];

      // Debug: Log raw data structure
      this.fastify.log.info(`PO ${purchaseOrder.id} raw data:`, {
        poItemsCount: poItems.length,
        drCount: deliveryReceipts.length,
        poItemsStructure: poItems.length > 0 ? Object.keys(poItems[0]) : [],
        drStructure: deliveryReceipts.length > 0 ? Object.keys(deliveryReceipts[0]) : [],
        drItemsStructure: deliveryReceipts.length > 0 && deliveryReceipts[0].items?.length > 0
          ? Object.keys(deliveryReceipts[0].items[0]) : [],
        actualPOItems: poItems.map(item => ({
          id: item.id,
          quantityPurchased: item.quantityPurchased,
          keys: Object.keys(item)
        })),
        actualDRs: deliveryReceipts.map(dr => ({
          id: dr.id,
          status: dr.status,
          itemsCount: dr.items?.length || 0,
          items: dr.items?.map(item => ({
            id: item.id,
            qtyDelivered: item.qtyDelivered,
            keys: Object.keys(item)
          })) || []
        }))
      });

      if (poItems.length === 0) {
        return {
          isValid: false,
          error: 'Purchase order has no items',
        };
      }

      // Calculate total ordered quantities by item
      const orderedQuantities = {};
      let totalOrderedAmount = 0;

      for (const poItem of poItems) {
        const poItemId = poItem.id; // Use PO item ID as the key
        // Use quantityPurchased field for PO items
        const quantity = parseFloat(poItem.quantityPurchased) || parseFloat(poItem.quantity) || 0;
        const unitPrice = parseFloat(poItem.unitPrice) || 0;
        orderedQuantities[poItemId] = {
          quantity,
          unitPrice,
          amount: quantity * unitPrice,
        };
        totalOrderedAmount += orderedQuantities[poItemId].amount;
      }

      // Calculate total delivered quantities by item
      const deliveredQuantities = {};
      let totalDeliveredAmount = 0;
      let totalDeliveries = 0;
      let hasValidDeliveries = false;

      for (const dr of deliveryReceipts) {
        // Only count approved delivery receipts (handle null/empty status)
        if (!FORCE_CLOSE_DELIVERY_VALIDATION.VALID_DR_STATUSES.includes(dr.status) &&
            dr.status !== null && dr.status !== undefined && dr.status !== '') {
          continue;
        }

        hasValidDeliveries = true;
        totalDeliveries++;

        const drItems = dr.items || [];

        for (const drItem of drItems) {
          const poItemId = drItem.po_item_id || drItem.poItemId; // Use po_item_id to match with PO items
          const deliveredQty = parseFloat(drItem.qty_delivered || drItem.qtyDelivered) || 0;
          const unitPrice = parseFloat(drItem.unitPrice) || 0;

          if (poItemId && !deliveredQuantities[poItemId]) {
            deliveredQuantities[poItemId] = {
              quantity: 0,
              amount: 0,
            };
          }

          if (poItemId) {
            deliveredQuantities[poItemId].quantity += deliveredQty;
            deliveredQuantities[poItemId].amount += deliveredQty * unitPrice;
            totalDeliveredAmount += deliveredQty * unitPrice;
          }
        }
      }

      // Validate delivery quantities against ordered quantities
      const itemValidation = [];
      let hasOverDelivery = false;
      let totalDeliveredQty = 0;
      let totalOrderedQty = 0;

      for (const poItemId in orderedQuantities) {
        const ordered = orderedQuantities[poItemId];
        const delivered = deliveredQuantities[poItemId] || { quantity: 0, amount: 0 };

        totalOrderedQty += ordered.quantity;
        totalDeliveredQty += delivered.quantity;

        const deliveryPercentage = ordered.quantity > 0
          ? (delivered.quantity / ordered.quantity * 100).toFixed(2)
          : '0.00';

        const isOverDelivered = delivered.quantity > ordered.quantity;
        if (isOverDelivered) {
          hasOverDelivery = true;
        }

        itemValidation.push({
          itemId: poItemId,
          orderedQuantity: ordered.quantity,
          deliveredQuantity: delivered.quantity,
          remainingQuantity: Math.max(0, ordered.quantity - delivered.quantity),
          deliveryPercentage: parseFloat(deliveryPercentage),
          isFullyDelivered: delivered.quantity >= ordered.quantity,
          isPartiallyDelivered: delivered.quantity > 0 && delivered.quantity < ordered.quantity,
          isOverDelivered,
          orderedAmount: ordered.amount,
          deliveredAmount: delivered.amount,
        });
      }

      // Determine overall delivery status
      const overallDeliveryPercentage = totalOrderedQty > 0
        ? (totalDeliveredQty / totalOrderedQty * 100).toFixed(2)
        : '0.00';

      let deliveryStatus;
      if (!hasValidDeliveries || totalDeliveredQty === 0) {
        deliveryStatus = FORCE_CLOSE_DELIVERY_VALIDATION.VALIDATION_RESULTS.NO_DELIVERIES;
      } else if (hasOverDelivery) {
        deliveryStatus = FORCE_CLOSE_DELIVERY_VALIDATION.VALIDATION_RESULTS.OVER_DELIVERED;
      } else if (parseFloat(overallDeliveryPercentage) >= FORCE_CLOSE_DELIVERY_VALIDATION.COMPLETION_THRESHOLDS.FULLY_DELIVERED) {
        deliveryStatus = FORCE_CLOSE_DELIVERY_VALIDATION.VALIDATION_RESULTS.FULLY_DELIVERED;
      } else if (parseFloat(overallDeliveryPercentage) > FORCE_CLOSE_DELIVERY_VALIDATION.COMPLETION_THRESHOLDS.NO_DELIVERY) {
        deliveryStatus = FORCE_CLOSE_DELIVERY_VALIDATION.VALIDATION_RESULTS.PARTIALLY_DELIVERED;
      } else {
        deliveryStatus = FORCE_CLOSE_DELIVERY_VALIDATION.VALIDATION_RESULTS.NO_DELIVERIES;
      }

      // Debug: Log delivery status calculation
      this.fastify.log.info(`PO ${purchaseOrder.id} delivery status calculation:`, {
        totalOrderedQty,
        totalDeliveredQty,
        overallDeliveryPercentage,
        hasValidDeliveries,
        hasOverDelivery,
        deliveryStatus,
        thresholds: {
          fullyDelivered: FORCE_CLOSE_DELIVERY_VALIDATION.COMPLETION_THRESHOLDS.FULLY_DELIVERED,
          noDelivery: FORCE_CLOSE_DELIVERY_VALIDATION.COMPLETION_THRESHOLDS.NO_DELIVERY
        },
        drCount: deliveryReceipts.length,
        drItemsCount: deliveryReceipts.reduce((sum, dr) => sum + (dr.items?.length || 0), 0)
      });

      const summary = {
        poId: purchaseOrder.id,
        poStatus: purchaseOrder.status,
        totalItems: poItems.length,
        totalDeliveries,
        hasDeliveries: hasValidDeliveries,
        deliveryStatus,
        overallDeliveryPercentage: parseFloat(overallDeliveryPercentage),
        totalOrderedQuantity: totalOrderedQty,
        totalDeliveredQuantity: totalDeliveredQty,
        totalRemainingQuantity: Math.max(0, totalOrderedQty - totalDeliveredQty),
        totalOrderedAmount,
        totalDeliveredAmount,
        hasOverDelivery,
        itemValidation,
      };

      return {
        isValid: true,
        hasDeliveries: hasValidDeliveries,
        totalDeliveries,
        deliveryStatus,
        summary,
      };

    } catch (error) {
      return {
        isValid: false,
        error: `PO delivery validation error: ${error.message}`,
      };
    }
  }

  /**
   * Check PO status to determine validation path (Active vs Closed)
   * Implements dual validation paths from requirements document
   */
  async _checkPOStatus(requisitionId) {
    try {
      // Get all purchase orders for this requisition
      const purchaseOrdersResult = await this.purchaseOrderRepository.findAll({
        where: { requisitionId },
        include: [
          {
            association: 'deliveryReceipts',
            include: [
              {
                association: 'deliveryReceiptInvoices'
              }
            ]
          },
          'rsPaymentRequests'
        ],
      });

      // Handle paginated result from repository
      const purchaseOrders = Array.isArray(purchaseOrdersResult)
        ? purchaseOrdersResult
        : (purchaseOrdersResult?.data || []);

      if (!purchaseOrders || purchaseOrders.length === 0) {
        return {
          isValid: false,
          hasActivePOs: false,
          allClosed: false,
          activePOs: [],
          closedPOs: [],
          details: { error: 'No purchase orders found for requisition' },
        };
      }

      const activePOs = [];
      const closedPOs = [];
      const invalidPOs = [];

      // Categorize POs by status
      for (const po of purchaseOrders) {
        const status = po.status?.toLowerCase(); // Normalize to lowercase for comparison

        if (status === 'for_delivery') {
          activePOs.push({
            id: po.id,
            status: po.status,
            amount: po.amount,
            quantity: po.quantity,
            hasDeliveries: po.deliveryReceipts?.length > 0,
            hasPayments: po.rsPaymentRequests?.length > 0,
          });
        } else if (status === 'closed_po' || status === 'cancelled_po' || status === 'closed') {
          closedPOs.push({
            id: po.id,
            status: po.status,
            amount: po.amount,
            quantity: po.quantity,
            deliveryCount: po.deliveryReceipts?.length || 0,
            paymentCount: po.rsPaymentRequests?.length || 0,
          });
        } else {
          // Invalid status for force close (for_po_review, for_po_approval, for_sending)
          invalidPOs.push({
            id: po.id,
            status: po.status,
            reason: 'PO status not eligible for force close',
          });
        }
      }

      // Check for invalid PO statuses
      if (invalidPOs.length > 0) {
        return {
          isValid: false,
          hasActivePOs: false,
          allClosed: false,
          activePOs: [],
          closedPOs: [],
          details: {
            error: 'Some POs have invalid status for force close',
            invalidPOs,
            validStatuses: ['for_delivery', 'closed_po', 'cancelled_po', 'closed'],
          },
        };
      }

      const hasActivePOs = activePOs.length > 0;
      const allClosed = closedPOs.length === purchaseOrders.length;

      return {
        isValid: true,
        hasActivePOs,
        allClosed,
        activePOs,
        closedPOs,
        details: {
          totalPOs: purchaseOrders.length,
          activePOCount: activePOs.length,
          closedPOCount: closedPOs.length,
          validationPath: hasActivePOs ? 'ACTIVE_PO_PATH' : 'CLOSED_PO_PATH',
        },
      };

    } catch (error) {
      this.fastify.log.error(`PO status check failed for RS ${requisitionId}: ${error.message}`);
      return {
        isValid: false,
        hasActivePOs: false,
        allClosed: false,
        activePOs: [],
        closedPOs: [],
        details: { error: `PO status validation failed: ${error.message}` },
      };
    }
  }

  /**
   * Enhanced payment prerequisite validation for active POs with deliveries
   * Validates that DR Qty × Unit Price = PR Amount for all delivered items
   * Implements comprehensive payment validation with detailed error categorization
   */
  async _checkPaymentPrerequisite(requisitionId) {
    try {
      const { FORCE_CLOSE_PAYMENT_VALIDATION, FORCE_CLOSE_ERRORS } = this.constants.forceClose;

      this.fastify.log.info(`Starting enhanced payment prerequisite validation for RS: ${requisitionId}`);

      // Get all purchase orders with their delivery receipts and payment requests
      const purchaseOrdersResult = await this.purchaseOrderRepository.findAll({
        where: { requisitionId },
        include: [
          {
            association: 'deliveryReceipts',
            include: ['items'],
          },
          {
            association: 'rsPaymentRequests',
          },
        ],
      });

      // Handle paginated result from repository
      const purchaseOrders = Array.isArray(purchaseOrdersResult)
        ? purchaseOrdersResult
        : (purchaseOrdersResult?.data || []);

      const paymentValidation = {
        allPaid: true,
        unpaidDeliveries: [],
        paymentSummary: [],
        validationErrors: [],
        totalDeliveries: 0,
        totalPaidDeliveries: 0,
        totalUnpaidAmount: 0,
      };

      for (const po of purchaseOrders) {
        // Only validate active POs that are for_delivery (normalize to lowercase)
        if (po.status?.toLowerCase() !== 'for_delivery') continue;

        // Get all delivery receipts for this PO
        const deliveryReceipts = po.deliveryReceipts || [];

        for (const dr of deliveryReceipts) {
          // Only validate approved delivery receipts
          if (!FORCE_CLOSE_PAYMENT_VALIDATION.VALID_DR_STATUSES.includes(dr.status)) {
            this.fastify.log.warn(`Skipping DR ${dr.id} with invalid status: ${dr.status}`);
            continue;
          }

          paymentValidation.totalDeliveries++;

          // Calculate total delivered amount with enhanced validation
          const deliveryCalculation = this._calculateDeliveryAmount(dr);

          if (!deliveryCalculation.isValid) {
            paymentValidation.allPaid = false;
            paymentValidation.validationErrors.push({
              category: FORCE_CLOSE_PAYMENT_VALIDATION.VALIDATION_CATEGORIES.CALCULATION_VALID,
              poId: po.id,
              drId: dr.id,
              error: FORCE_CLOSE_ERRORS.PAYMENT_CALCULATION_ERROR,
              details: deliveryCalculation.error,
            });
            continue;
          }

          const totalDeliveredAmount = deliveryCalculation.amount;

          // Enhanced payment request validation
          const paymentValidationResult = this._validatePaymentRequest(po, dr, totalDeliveredAmount);

          if (!paymentValidationResult.isValid) {
            paymentValidation.allPaid = false;
            paymentValidation.unpaidDeliveries.push(paymentValidationResult.error);

            if (paymentValidationResult.error.reason !== 'No corresponding payment request found') {
              paymentValidation.totalUnpaidAmount += totalDeliveredAmount;
            }
          } else {
            paymentValidation.totalPaidDeliveries++;
          }

          // Add to payment summary with enhanced details
          paymentValidation.paymentSummary.push({
            poId: po.id,
            drId: dr.id,
            prId: paymentValidationResult.paymentRequest?.id,
            deliveredAmount: totalDeliveredAmount,
            paidAmount: paymentValidationResult.paymentRequest?.amount || 0,
            isPaid: paymentValidationResult.isValid,
            validationResult: paymentValidationResult.validationCategory,
            paymentStatus: paymentValidationResult.paymentRequest?.status,
            amountDifference: paymentValidationResult.amountDifference || 0,
          });
        }
      }

      // Enhanced validation summary
      const validationSummary = {
        totalDeliveries: paymentValidation.totalDeliveries,
        totalPaidDeliveries: paymentValidation.totalPaidDeliveries,
        totalUnpaidDeliveries: paymentValidation.totalDeliveries - paymentValidation.totalPaidDeliveries,
        paymentCompletionRate: paymentValidation.totalDeliveries > 0
          ? (paymentValidation.totalPaidDeliveries / paymentValidation.totalDeliveries * 100).toFixed(2) + '%'
          : '0%',
        totalUnpaidAmount: paymentValidation.totalUnpaidAmount,
        hasValidationErrors: paymentValidation.validationErrors.length > 0,
      };

      this.fastify.log.info(`Payment validation completed for RS: ${requisitionId}`, validationSummary);

      return {
        allPaid: paymentValidation.allPaid,
        details: {
          ...paymentValidation,
          summary: validationSummary,
        },
      };

    } catch (error) {
      this.fastify.log.error(`Enhanced payment prerequisite check failed for RS ${requisitionId}: ${error.message}`);
      return {
        allPaid: false,
        details: {
          error: `Payment validation failed: ${error.message}`,
          validationErrors: [{
            category: 'system_error',
            error: 'Payment validation system error',
            details: error.message,
          }],
        },
      };
    }
  }

  /**
   * Calculate delivery amount with enhanced validation
   * Helper method for payment prerequisite validation
   */
  _calculateDeliveryAmount(deliveryReceipt) {
    try {
      let totalAmount = 0;
      const drItems = deliveryReceipt.items || [];

      if (drItems.length === 0) {
        return {
          isValid: false,
          amount: 0,
          error: 'No delivery receipt items found',
        };
      }

      for (const drItem of drItems) {
        const quantity = parseFloat(drItem.quantity) || 0;
        const unitPrice = parseFloat(drItem.unitPrice) || 0;

        if (quantity < 0 || unitPrice < 0) {
          return {
            isValid: false,
            amount: 0,
            error: `Invalid quantity (${quantity}) or unit price (${unitPrice}) in delivery receipt item`,
          };
        }

        totalAmount += quantity * unitPrice;
      }

      return {
        isValid: true,
        amount: totalAmount,
        itemCount: drItems.length,
      };

    } catch (error) {
      return {
        isValid: false,
        amount: 0,
        error: `Calculation error: ${error.message}`,
      };
    }
  }

  /**
   * Validate payment request for a delivery receipt
   * Enhanced validation with detailed error categorization
   */
  _validatePaymentRequest(purchaseOrder, deliveryReceipt, deliveredAmount) {
    const { FORCE_CLOSE_PAYMENT_VALIDATION, FORCE_CLOSE_ERRORS } = this.constants.forceClose;

    try {
      // Find corresponding payment requests
      // Payment requests are linked to delivery receipt invoices, not delivery receipts directly
      const deliveryInvoices = deliveryReceipt.deliveryReceiptInvoices || [];
      const invoiceIds = deliveryInvoices.map(invoice => invoice.id);

      const paymentRequests = purchaseOrder.rsPaymentRequests?.filter(pr =>
        invoiceIds.includes(pr.deliveryInvoiceId)
      ) || [];

      // Check if payment request exists
      if (paymentRequests.length === 0) {
        return {
          isValid: false,
          validationCategory: FORCE_CLOSE_PAYMENT_VALIDATION.VALIDATION_CATEGORIES.REQUEST_EXISTS,
          error: {
            poId: purchaseOrder.id,
            drId: deliveryReceipt.id,
            deliveredAmount,
            reason: FORCE_CLOSE_ERRORS.PAYMENT_REQUEST_NOT_FOUND,
          },
        };
      }

      // Check for multiple payment requests (should be only one per DR)
      if (paymentRequests.length > 1) {
        return {
          isValid: false,
          validationCategory: FORCE_CLOSE_PAYMENT_VALIDATION.VALIDATION_CATEGORIES.REQUEST_EXISTS,
          error: {
            poId: purchaseOrder.id,
            drId: deliveryReceipt.id,
            prIds: paymentRequests.map(pr => pr.id),
            deliveredAmount,
            reason: FORCE_CLOSE_ERRORS.MULTIPLE_PAYMENT_REQUESTS,
          },
        };
      }

      const paymentRequest = paymentRequests[0];

      // Validate payment request status
      if (!FORCE_CLOSE_PAYMENT_VALIDATION.VALID_PR_STATUSES.includes(paymentRequest.status)) {
        return {
          isValid: false,
          validationCategory: FORCE_CLOSE_PAYMENT_VALIDATION.VALIDATION_CATEGORIES.STATUS_VALID,
          paymentRequest,
          error: {
            poId: purchaseOrder.id,
            drId: deliveryReceipt.id,
            prId: paymentRequest.id,
            deliveredAmount,
            paidAmount: paymentRequest.amount || 0,
            paymentStatus: paymentRequest.status,
            reason: paymentRequest.status ? FORCE_CLOSE_ERRORS.PAYMENT_REQUEST_NOT_APPROVED : FORCE_CLOSE_ERRORS.PAYMENT_REQUEST_INVALID_STATUS,
          },
        };
      }

      // Validate payment amount matches delivery amount
      const paymentAmount = parseFloat(paymentRequest.amount) || 0;
      const amountDifference = Math.abs(deliveredAmount - paymentAmount);

      if (amountDifference > FORCE_CLOSE_PAYMENT_VALIDATION.AMOUNT_TOLERANCE) {
        return {
          isValid: false,
          validationCategory: FORCE_CLOSE_PAYMENT_VALIDATION.VALIDATION_CATEGORIES.AMOUNT_MATCH,
          paymentRequest,
          amountDifference,
          error: {
            poId: purchaseOrder.id,
            drId: deliveryReceipt.id,
            prId: paymentRequest.id,
            deliveredAmount,
            paidAmount: paymentAmount,
            difference: amountDifference,
            tolerance: FORCE_CLOSE_PAYMENT_VALIDATION.AMOUNT_TOLERANCE,
            reason: FORCE_CLOSE_ERRORS.PAYMENT_AMOUNT_MISMATCH,
          },
        };
      }

      // Payment validation successful
      return {
        isValid: true,
        validationCategory: FORCE_CLOSE_PAYMENT_VALIDATION.VALIDATION_RESULTS.FULLY_PAID,
        paymentRequest,
        amountDifference,
      };

    } catch (error) {
      return {
        isValid: false,
        validationCategory: FORCE_CLOSE_PAYMENT_VALIDATION.VALIDATION_CATEGORIES.CALCULATION_VALID,
        error: {
          poId: purchaseOrder.id,
          drId: deliveryReceipt.id,
          deliveredAmount,
          reason: FORCE_CLOSE_ERRORS.PAYMENT_CALCULATION_ERROR,
          details: error.message,
        },
      };
    }
  }

  /**
   * Check for pending canvass sheets (different from pending approvals)
   * Used in Scenario 3: Closed Purchase Orders with Pending Canvass Sheet Approvals
   */
  async _checkPendingCanvassSheets(requisitionId) {
    try {
      // Get all canvass sheets for this requisition
      const canvassSheetsResult = await this.canvassRequisitionRepository.findAll({
        where: { requisitionId },
        include: ['canvassItems', 'canvassApprovers'],
      });

      // Handle paginated result from repository
      const canvassSheets = Array.isArray(canvassSheetsResult)
        ? canvassSheetsResult
        : (canvassSheetsResult?.data || []);

      if (!canvassSheets || canvassSheets.length === 0) {
        return {
          hasPending: false,
          details: { message: 'No canvass sheets found' },
        };
      }

      const pendingSheets = [];
      const approvedSheets = [];
      const rejectedSheets = [];

      for (const cs of canvassSheets) {
        const status = cs.status?.toUpperCase();

        if (status === 'CS_PENDING_APPROVAL' || status === 'CS_FOR_APPROVAL') {
          pendingSheets.push({
            id: cs.id,
            status: cs.status,
            createdAt: cs.createdAt,
            itemCount: cs.canvassItems?.length || 0,
            approvalCount: cs.canvassApprovers?.length || 0,
          });
        } else if (status === 'CS_APPROVED') {
          approvedSheets.push({
            id: cs.id,
            status: cs.status,
            approvedAt: cs.updatedAt,
          });
        } else if (status === 'CS_REJECTED' || status === 'CS_CANCELLED') {
          rejectedSheets.push({
            id: cs.id,
            status: cs.status,
            rejectedAt: cs.updatedAt,
          });
        }
      }

      const hasPending = pendingSheets.length > 0;

      return {
        hasPending,
        details: {
          totalSheets: canvassSheets.length,
          pendingCount: pendingSheets.length,
          approvedCount: approvedSheets.length,
          rejectedCount: rejectedSheets.length,
          pendingSheets,
          approvedSheets,
          rejectedSheets,
        },
      };

    } catch (error) {
      this.fastify.log.error(`Pending canvass sheets check failed for RS ${requisitionId}: ${error.message}`);
      return {
        hasPending: false,
        details: { error: `Canvass sheet validation failed: ${error.message}` },
      };
    }
  }

  /**
   * Execute Scenario 1: Active Purchase Orders with Partial Deliveries
   */
  async _executeActivePOPartialDeliveryScenario(requisitionId, userFromToken, transaction, result) {
    const { FORCE_CLOSE_ACTIONS, FORCE_CLOSE_STATUS } = this.constants.forceClose;

    try {
      // Get all active purchase orders for this requisition
      const purchaseOrders = await this.purchaseOrderRepository.findAll({
        where: {
          requisitionId,
          status: ['for_delivery', 'FOR_DELIVERY'] // Support both cases
        },
        include: [
          {
            association: 'deliveryReceipts',
            include: ['items'],
          },
          {
            association: 'purchaseOrderItems',
          },
          {
            association: 'rsPaymentRequests',
          },
        ],
        transaction,
      });

      for (const po of purchaseOrders) {
        // Calculate delivered quantities and amounts
        const deliveryData = await this._calculateDeliveredAmounts(po);

        // Store original values before modification
        await this.purchaseOrderRepository.update(
          { id: po.id },
          {
            originalAmount: po.totalAmount,
            originalQuantity: this._calculateTotalPOQuantity(po.purchaseOrderItems),
            systemGeneratedNotes: `Force Close: Original Amount: ${po.totalAmount}, Original Quantity: ${this._calculateTotalPOQuantity(po.purchaseOrderItems)}. Updated to reflect delivered quantities only.`,
          },
          { transaction }
        );

        // Update PO amounts to reflect only delivered quantities
        await this.purchaseOrderRepository.update(
          { id: po.id },
          {
            totalAmount: deliveryData.totalDeliveredAmount,
            status: FORCE_CLOSE_STATUS.RS_CLOSED,
          },
          { transaction }
        );

        // Update PO item quantities to match delivered quantities
        for (const poItem of po.purchaseOrderItems) {
          const deliveredQty = this._getDeliveredQuantityForItem(poItem.id, po.deliveryReceipts);
          await this.purchaseOrderItemRepository.update(
            { id: poItem.id },
            { quantity: deliveredQty },
            { transaction }
          );
        }

        // Return unfulfilled quantities to GFQ (OFM/OFM-TOM only)
        await this._returnUnfulfilledQuantitiesToGFQ(po, deliveryData.unfulfilledItems, transaction);

        result.actions.push(FORCE_CLOSE_ACTIONS.UPDATE_PO_AMOUNTS);
        result.actions.push(FORCE_CLOSE_ACTIONS.UPDATE_PO_QUANTITIES);
        result.actions.push(FORCE_CLOSE_ACTIONS.GENERATE_PO_NOTES);
        result.actions.push(FORCE_CLOSE_ACTIONS.RETURN_GFQ);
        result.actions.push(FORCE_CLOSE_ACTIONS.CLOSE_PO);
      }

      // Cancel all draft/pending related documents
      await this._cancelDraftDocuments(requisitionId, userFromToken, transaction);

    } catch (error) {
      this.fastify.log.error(`Scenario 1 execution failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Execute Scenario 2: All Purchase Orders Closed/Cancelled with Remaining Quantities
   */
  async _executeClosedPORemainingQtyScenario(requisitionId, userFromToken, transaction, result) {
    const { FORCE_CLOSE_ACTIONS } = this.constants.forceClose;

    try {
      // Get requisition with remaining quantities
      const requisition = await this.requisitionRepository.findOne({
        where: { id: requisitionId },
        include: [
          {
            association: 'requisitionItems',
          },
        ],
        transaction,
      });

      // Calculate remaining quantities that need canvassing
      const remainingQuantities = await this._calculateRemainingQuantities(requisitionId, transaction);

      // Zero out remaining quantities for canvassing
      for (const item of requisition.requisitionItems) {
        const remainingQty = remainingQuantities[item.id] || 0;
        if (remainingQty > 0) {
          // Update requisition item to zero out remaining quantity
          await this.requisitionItemRepository.update(
            { id: item.id },
            {
              quantityForCanvassing: 0,
              forceCloseRemainingQty: remainingQty, // Store original remaining qty for audit
            },
            { transaction }
          );

          // Return unfulfilled quantities to GFQ (OFM/OFM-TOM only)
          await this._returnQuantityToGFQ(item, remainingQty, transaction);
        }
      }

      result.actions.push(FORCE_CLOSE_ACTIONS.ZERO_REMAINING_QTY);
      result.actions.push(FORCE_CLOSE_ACTIONS.RETURN_REMAINING_GFQ);

    } catch (error) {
      this.fastify.log.error(`Scenario 2 execution failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Execute Scenario 3: Closed Purchase Orders with Pending Canvass Sheet Approvals
   */
  async _executeClosedPOPendingCSScenario(requisitionId, userFromToken, transaction, result) {
    const { FORCE_CLOSE_ACTIONS, FORCE_CLOSE_STATUS } = this.constants.forceClose;

    try {
      // Get all pending canvass sheets for this requisition
      const pendingCanvassSheets = await this.canvassRequisitionRepository.findAll({
        where: {
          requisitionId,
          status: ['FOR_APPROVAL', 'DRAFT']
        },
        include: [
          {
            association: 'canvassItems',
          },
        ],
        transaction,
      });

      // Cancel pending canvass sheets and return quantities to GFQ
      for (const cs of pendingCanvassSheets) {
        // Cancel the canvass sheet
        await this.canvassRequisitionRepository.update(
          { id: cs.id },
          {
            status: FORCE_CLOSE_STATUS.CS_CANCELLED,
            cancelledAt: new Date(),
            cancelledBy: userFromToken.id,
            cancellationReason: 'Force close operation - pending canvass sheet cancelled',
          },
          { transaction }
        );

        // Zero out quantities from cancelled canvass sheet and return to GFQ
        for (const csItem of cs.canvassItems) {
          // Return quantity to GFQ (OFM/OFM-TOM only)
          await this._returnQuantityToGFQ(csItem, csItem.quantity, transaction);

          // Update canvass item to reflect cancellation
          await this.canvassItemRepository.update(
            { id: csItem.id },
            {
              quantity: 0,
              cancelledQuantity: csItem.quantity,
            },
            { transaction }
          );
        }
      }

      result.actions.push(FORCE_CLOSE_ACTIONS.CANCEL_PENDING_CS);
      result.actions.push(FORCE_CLOSE_ACTIONS.ZERO_REMAINING_QTY_CS);
      result.actions.push(FORCE_CLOSE_ACTIONS.RETURN_CS_GFQ);

    } catch (error) {
      this.fastify.log.error(`Scenario 3 execution failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Execute common force close actions for all scenarios
   */
  async _executeCommonForceCloseActions(requisitionId, userFromToken, notes, transaction, result) {
    const { FORCE_CLOSE_STATUS, FORCE_CLOSE_ACTIONS, FORCE_CLOSE_HISTORY_ACTIONS } = this.constants.forceClose;

    // Update requisition status to CLOSED
    await this.requisitionRepository.update(
      { id: requisitionId },
      { status: FORCE_CLOSE_STATUS.RS_CLOSED },
      { transaction }
    );
    result.actions.push(FORCE_CLOSE_ACTIONS.UPDATE_RS_STATUS);

    // Add force close notes to comments
    await this.commentRepository.create({
      model: 'requisition',
      modelId: requisitionId,
      comment: `Force Close Notes: ${notes}`,
      commentedBy: userFromToken.id,
    }, { transaction });
    result.actions.push(FORCE_CLOSE_ACTIONS.ADD_NOTES);

    // Log force close activity to history table
    await this.historyRepository.create({
      model: 'requisition',
      modelId: requisitionId,
      action: FORCE_CLOSE_HISTORY_ACTIONS.FORCE_CLOSED,
      details: `Requisition force closed by ${userFromToken.username}`,
      userId: userFromToken.id,
    }, { transaction });

    // Log comprehensive force close activity to force_close_logs table
    await this._logForceCloseActivity(requisitionId, userFromToken, result, notes, transaction);

    result.actions.push(FORCE_CLOSE_ACTIONS.LOG_ACTIVITY);
  }

  /**
   * Calculate delivered amounts and quantities for a purchase order
   */
  async _calculateDeliveredAmounts(purchaseOrder) {
    const deliveredItems = {};
    let totalDeliveredAmount = 0;
    const unfulfilledItems = [];

    for (const poItem of purchaseOrder.purchaseOrderItems || []) {
      let deliveredQty = 0;

      // Calculate total delivered quantity for this item
      for (const dr of purchaseOrder.deliveryReceipts || []) {
        for (const drItem of dr.items || []) {
          if (drItem.purchaseOrderItemId === poItem.id) {
            deliveredQty += drItem.quantity || 0;
          }
        }
      }

      deliveredItems[poItem.id] = deliveredQty;
      totalDeliveredAmount += (deliveredQty * poItem.unitPrice);

      // Track unfulfilled quantities
      const unfulfilledQty = poItem.quantity - deliveredQty;
      if (unfulfilledQty > 0) {
        unfulfilledItems.push({
          itemId: poItem.id,
          unfulfilledQuantity: unfulfilledQty,
          originalQuantity: poItem.quantity,
          deliveredQuantity: deliveredQty,
        });
      }
    }

    return {
      deliveredItems,
      totalDeliveredAmount,
      unfulfilledItems,
    };
  }

  /**
   * Calculate total quantity for purchase order items
   */
  _calculateTotalPOQuantity(purchaseOrderItems) {
    return (purchaseOrderItems || []).reduce((total, item) => total + (item.quantity || 0), 0);
  }

  /**
   * Get delivered quantity for a specific purchase order item
   */
  _getDeliveredQuantityForItem(poItemId, deliveryReceipts) {
    let deliveredQty = 0;

    for (const dr of deliveryReceipts || []) {
      for (const drItem of dr.items || []) {
        if (drItem.purchaseOrderItemId === poItemId) {
          deliveredQty += drItem.quantity || 0;
        }
      }
    }

    return deliveredQty;
  }

  /**
   * Return unfulfilled quantities to GFQ (OFM/OFM-TOM only)
   */
  async _returnUnfulfilledQuantitiesToGFQ(purchaseOrder, unfulfilledItems, transaction) {
    // Only return to GFQ for OFM and OFM-TOM requisitions
    const requisition = await this.requisitionRepository.findOne({
      where: { id: purchaseOrder.requisitionId },
      transaction,
    });

    if (!requisition || !['OFM', 'OFM-TOM'].includes(requisition.type)) {
      return; // No GFQ return for non-OFM requisitions
    }

    for (const item of unfulfilledItems) {
      // Implementation would depend on GFQ system integration
      this.fastify.log.info(`Returning ${item.unfulfilledQuantity} units to GFQ for item ${item.itemId}`);
      // TODO: Implement actual GFQ return logic when GFQ system is available
    }
  }

  /**
   * Calculate remaining quantities that need canvassing
   */
  async _calculateRemainingQuantities(requisitionId, transaction) {
    const requisition = await this.requisitionRepository.findOne({
      where: { id: requisitionId },
      include: [
        {
          association: 'requisitionItems',
        },
        {
          association: 'purchaseOrders',
          include: ['purchaseOrderItems'],
        },
      ],
      transaction,
    });

    const remainingQuantities = {};

    for (const reqItem of requisition.requisitionItems || []) {
      let totalPOQuantity = 0;

      // Calculate total quantity already in purchase orders
      for (const po of requisition.purchaseOrders || []) {
        for (const poItem of po.purchaseOrderItems || []) {
          if (poItem.requisitionItemId === reqItem.id) {
            totalPOQuantity += poItem.quantity || 0;
          }
        }
      }

      // Remaining quantity = requested - already in POs
      const remainingQty = (reqItem.quantity || 0) - totalPOQuantity;
      remainingQuantities[reqItem.id] = Math.max(0, remainingQty);
    }

    return remainingQuantities;
  }

  /**
   * Return quantity to GFQ for a specific item
   */
  async _returnQuantityToGFQ(item, quantity, transaction) {
    // Implementation would depend on GFQ system integration
    this.fastify.log.info(`Returning ${quantity} units to GFQ for item ${item.id}`);
    // TODO: Implement actual GFQ return logic when GFQ system is available
  }

  /**
   * Cancel all draft/pending documents related to the requisition
   */
  async _cancelDraftDocuments(requisitionId, userFromToken, transaction) {
    const { FORCE_CLOSE_STATUS } = this.constants.forceClose;

    try {
      // Cancel draft delivery receipts
      const draftDeliveryReceipts = await this.deliveryReceiptRepository.findAll({
        where: {
          requisitionId,
          status: ['DRAFT', 'FOR_APPROVAL']
        },
        transaction
      });

      for (const dr of draftDeliveryReceipts) {
        await this.deliveryReceiptRepository.update(
          { id: dr.id },
          {
            status: FORCE_CLOSE_STATUS.DR_CANCELLED,
            cancelledAt: new Date(),
            cancelledBy: userFromToken.id,
            cancellationReason: 'Force close operation - draft document cancelled',
          },
          { transaction }
        );
      }

      // Cancel draft invoice reports
      const draftInvoiceReports = await this.invoiceReportRepository.findAll({
        where: {
          requisitionId,
          status: ['DRAFT', 'FOR_APPROVAL']
        },
        transaction
      });

      for (const ir of draftInvoiceReports) {
        await this.invoiceReportRepository.update(
          { id: ir.id },
          {
            status: FORCE_CLOSE_STATUS.IR_CANCELLED,
            cancelledAt: new Date(),
            cancelledBy: userFromToken.id,
            cancellationReason: 'Force close operation - draft document cancelled',
          },
          { transaction }
        );
      }

      // Cancel draft payment requests
      const draftPaymentRequests = await this.rsPaymentRequestRepository.findAll({
        where: {
          requisitionId,
          status: ['DRAFT', 'FOR_APPROVAL']
        },
        transaction
      });

      for (const pr of draftPaymentRequests) {
        await this.rsPaymentRequestRepository.update(
          { id: pr.id },
          {
            status: FORCE_CLOSE_STATUS.PR_CANCELLED,
            cancelledAt: new Date(),
            cancelledBy: userFromToken.id,
            cancellationReason: 'Force close operation - draft document cancelled',
          },
          { transaction }
        );
      }

    } catch (error) {
      this.fastify.log.error(`Failed to cancel draft documents: ${error.message}`);
      throw error;
    }
  }

  /**
   * Log comprehensive force close activity to force_close_logs table
   */
  async _logForceCloseActivity(requisitionId, userFromToken, result, notes, transaction) {
    try {
      // Create comprehensive log entry
      await this.db.forceCloseLogModel.create({
        requisitionId: requisitionId,
        userId: userFromToken.id,
        scenarioType: result.scenario || 'UNKNOWN',
        validationPath: result.validationPath || 'UNKNOWN',
        quantitiesAffected: result.quantitiesAffected || {},
        documentsCancelled: result.documentsCancelled || {},
        poAdjustments: result.poAdjustments || {},
        forceCloseNotes: notes,
      }, { transaction });

      this.fastify.log.info(`Force close activity logged for requisition ${requisitionId}`);
    } catch (error) {
      this.fastify.log.error(`Failed to log force close activity: ${error.message}`);
      // Don't throw error here as it's not critical to the force close operation
    }
  }

  /**
   * Get force close history for a requisition
   * Returns comprehensive audit trail and history information
   */
  async getForceCloseHistory({ requisitionId, userFromToken }) {
    this.fastify.log.info(`Starting getForceCloseHistory for requisition ${requisitionId}`);

    try {
      // Check user authorization first
      const authResult = await this._checkUserAuthorization(requisitionId, userFromToken);
      if (!authResult.isAuthorized) {
        throw this.clientErrors.FORBIDDEN({
          message: 'Access denied - insufficient permissions to view force close history',
        });
      }

      // Get force close log from database (simplified query without associations for now)
      const forceCloseLog = await this.db.forceCloseLogModel.findOne({
        where: { requisitionId },
      });

      // Get system changes from history table (simplified for now)
      const systemChanges = [];

      // Get audit trail from comments (simplified for now)
      const auditTrail = [];

      // Get impacted documents (simplified for now)
      const impactedDocuments = {
        cancelledCanvassSheets: [],
        cancelledDeliveryReceipts: [],
        cancelledInvoiceReports: [],
        cancelledPaymentRequests: [],
        modifiedPurchaseOrders: [],
      };

      return {
        forceCloseLog: forceCloseLog || null,
        systemChanges,
        auditTrail,
        impactedDocuments,
      };

    } catch (error) {
      // Re-throw client errors as-is
      if (error.status && error.status < 500) {
        throw error;
      }

      this.fastify.log.error(`Failed to get force close history: ${error.message}`);

      // Return empty structure instead of throwing error
      return {
        forceCloseLog: null,
        systemChanges: [],
        auditTrail: [],
        impactedDocuments: {
          cancelledCanvassSheets: [],
          cancelledDeliveryReceipts: [],
          cancelledInvoiceReports: [],
          cancelledPaymentRequests: [],
          modifiedPurchaseOrders: [],
        },
      };
    }
  }

  /**
   * Get documents that were impacted by force close
   */
  async _getImpactedDocuments(requisitionId) {
    const impactedDocuments = {
      cancelledCanvassSheets: [],
      cancelledDeliveryReceipts: [],
      cancelledInvoiceReports: [],
      cancelledPaymentRequests: [],
      modifiedPurchaseOrders: [],
    };

    try {
      // Get cancelled canvass sheets
      impactedDocuments.cancelledCanvassSheets = await this.canvassRequisitionRepository.findAll({
        where: {
          requisitionId,
          status: 'cs_cancelled',
          cancelledAt: { [this.db.Sequelize.Op.ne]: null }
        },
        attributes: ['id', 'csNumber', 'status', 'cancelledAt', 'cancellationReason'],
      });

      // Get cancelled delivery receipts
      impactedDocuments.cancelledDeliveryReceipts = await this.deliveryReceiptRepository.findAll({
        where: {
          requisitionId,
          status: 'rr_cancelled',
          cancelledAt: { [this.db.Sequelize.Op.ne]: null }
        },
        attributes: ['id', 'drNumber', 'status', 'cancelledAt', 'cancellationReason'],
      });

      // Get cancelled invoice reports
      impactedDocuments.cancelledInvoiceReports = await this.invoiceReportRepository.findAll({
        where: {
          requisitionId,
          status: 'ir_cancelled',
          cancelledAt: { [this.db.Sequelize.Op.ne]: null }
        },
        attributes: ['id', 'irNumber', 'status', 'cancelledAt', 'cancellationReason'],
      });

      // Get cancelled payment requests
      impactedDocuments.cancelledPaymentRequests = await this.rsPaymentRequestRepository.findAll({
        where: {
          requisitionId,
          status: 'pr_cancelled',
          cancelledAt: { [this.db.Sequelize.Op.ne]: null }
        },
        attributes: ['id', 'prNumber', 'status', 'cancelledAt', 'cancellationReason'],
      });

      // Get modified purchase orders
      impactedDocuments.modifiedPurchaseOrders = await this.purchaseOrderRepository.findAll({
        where: {
          requisitionId,
          systemGeneratedNotes: { [this.db.Sequelize.Op.ne]: null }
        },
        attributes: ['id', 'poNumber', 'status', 'originalAmount', 'originalQuantity', 'totalAmount', 'systemGeneratedNotes'],
      });

    } catch (error) {
      this.fastify.log.error(`Failed to get impacted documents: ${error.message}`);
      // Return empty structure if there's an error
    }

    return impactedDocuments;
  }

  /**
   * Enhanced error handling with comprehensive error analysis
   * Provides detailed error categorization, severity assessment, and recovery strategies
   */
  _createEnhancedErrorResponse(error, context = {}) {
    const { FORCE_CLOSE_ERROR_HANDLING, FORCE_CLOSE_ERRORS } = this.constants.forceClose;

    try {
      // Analyze error type and determine category
      const errorAnalysis = this._analyzeError(error, context);

      // Determine severity level
      const severity = this._determineSeverityLevel(error, errorAnalysis);

      // Get recovery strategy
      const recoveryStrategy = this._getRecoveryStrategy(errorAnalysis, severity);

      // Generate user-friendly message
      const userMessage = this._generateUserFriendlyMessage(error, errorAnalysis);

      // Create comprehensive error details
      const enhancedDetails = {
        errorId: this._generateErrorId(),
        timestamp: new Date().toISOString(),
        context: context,
        analysis: errorAnalysis,
        severity: severity,
        recovery: recoveryStrategy,
        originalError: {
          message: error.message || error.reason || 'Unknown error',
          type: error.constructor?.name || 'Unknown',
          code: error.code || error.statusCode,
        },
        systemInfo: {
          nodeEnv: process.env.NODE_ENV,
          service: 'ForceCloseService',
          version: '1.0.0', // Could be from package.json
        },
      };

      return {
        isEligible: false,
        buttonVisible: false, // Fixed: If not eligible, button should never be visible
        errorType: 'ENHANCED_ERROR',
        reason: userMessage,
        details: enhancedDetails,
      };

    } catch (enhancementError) {
      // Fallback to basic error response if enhancement fails
      this.fastify.log.error(`Error enhancement failed: ${enhancementError.message}`);
      return this._createUnexpectedErrorResponse(error, context.requisitionId);
    }
  }

  /**
   * Analyze error to determine category and characteristics
   */
  _analyzeError(error, context) {
    const { FORCE_CLOSE_ERROR_HANDLING, FORCE_CLOSE_ERRORS } = this.constants.forceClose;

    let category = FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.SYSTEM;
    let subcategory = 'unknown';
    let isRetryable = false;
    let isUserActionRequired = false;

    // Analyze based on error message/reason
    const errorMessage = error.message || error.reason || '';

    if (errorMessage.includes('authorization') || errorMessage.includes('Access Denied')) {
      category = FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.AUTHORIZATION;
      subcategory = 'access_denied';
      isUserActionRequired = true;
    } else if (errorMessage.includes('validation') || errorMessage.includes('invalid')) {
      category = FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.VALIDATION;
      subcategory = 'validation_failed';
      isUserActionRequired = true;
    } else if (errorMessage.includes('payment') || errorMessage.includes('delivery')) {
      category = FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.BUSINESS_LOGIC;
      subcategory = 'business_rule_violation';
      isUserActionRequired = true;
    } else if (errorMessage.includes('database') || errorMessage.includes('transaction')) {
      category = FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.DATABASE;
      subcategory = 'database_operation';
      isRetryable = true;
    } else if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
      category = FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.NETWORK;
      subcategory = 'network_issue';
      isRetryable = true;
    }

    // Analyze based on error type
    if (error.statusCode) {
      if (error.statusCode >= 400 && error.statusCode < 500) {
        category = FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.VALIDATION;
        isUserActionRequired = true;
      } else if (error.statusCode >= 500) {
        category = FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.SYSTEM;
        isRetryable = true;
      }
    }

    return {
      category,
      subcategory,
      isRetryable,
      isUserActionRequired,
      contextType: context.step || FORCE_CLOSE_ERROR_HANDLING.CONTEXT_TYPES.SYSTEM_OPERATION,
      affectedComponents: this._identifyAffectedComponents(error, context),
    };
  }

  /**
   * Determine severity level based on error analysis
   */
  _determineSeverityLevel(error, analysis) {
    const { FORCE_CLOSE_ERROR_HANDLING } = this.constants.forceClose;

    // Critical: System errors that prevent any operation
    if (analysis.category === FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.SYSTEM ||
        analysis.category === FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.DATABASE) {
      return FORCE_CLOSE_ERROR_HANDLING.SEVERITY_LEVELS.CRITICAL;
    }

    // High: Business logic violations that require immediate attention
    if (analysis.category === FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.BUSINESS_LOGIC) {
      return FORCE_CLOSE_ERROR_HANDLING.SEVERITY_LEVELS.HIGH;
    }

    // Medium: Validation errors that can be corrected by user
    if (analysis.category === FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.VALIDATION) {
      return FORCE_CLOSE_ERROR_HANDLING.SEVERITY_LEVELS.MEDIUM;
    }

    // Low: Authorization issues (user needs different permissions)
    if (analysis.category === FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.AUTHORIZATION) {
      return FORCE_CLOSE_ERROR_HANDLING.SEVERITY_LEVELS.LOW;
    }

    return FORCE_CLOSE_ERROR_HANDLING.SEVERITY_LEVELS.MEDIUM;
  }

  /**
   * Get appropriate recovery strategy based on error analysis
   */
  _getRecoveryStrategy(analysis, severity) {
    const { FORCE_CLOSE_ERROR_HANDLING } = this.constants.forceClose;

    const strategy = {
      type: FORCE_CLOSE_ERROR_HANDLING.RECOVERY_STRATEGIES.NO_RECOVERY,
      steps: [],
      estimatedTime: 'Unknown',
      requiresSupport: false,
    };

    if (analysis.isRetryable) {
      strategy.type = FORCE_CLOSE_ERROR_HANDLING.RECOVERY_STRATEGIES.RETRY;
      strategy.steps = [
        'Wait a few moments and try again',
        'Check network connectivity',
        'Verify system status',
      ];
      strategy.estimatedTime = '1-2 minutes';
    } else if (analysis.isUserActionRequired) {
      strategy.type = FORCE_CLOSE_ERROR_HANDLING.RECOVERY_STRATEGIES.USER_ACTION;
      strategy.steps = this._getUserActionSteps(analysis);
      strategy.estimatedTime = '5-15 minutes';
    } else if (severity === FORCE_CLOSE_ERROR_HANDLING.SEVERITY_LEVELS.CRITICAL) {
      strategy.type = FORCE_CLOSE_ERROR_HANDLING.RECOVERY_STRATEGIES.SYSTEM_ADMIN;
      strategy.steps = [
        'Contact system administrator',
        'Report error details',
        'Wait for system resolution',
      ];
      strategy.requiresSupport = true;
      strategy.estimatedTime = '30+ minutes';
    }

    return strategy;
  }

  /**
   * Generate user-friendly error message
   */
  _generateUserFriendlyMessage(error, analysis) {
    const { FORCE_CLOSE_ERROR_HANDLING } = this.constants.forceClose;

    const baseMessage = error.message || error.reason || 'An error occurred';

    switch (analysis.category) {
      case FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.AUTHORIZATION:
        return 'You do not have permission to perform this action. Please contact the requisition owner or purchasing staff.';

      case FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.VALIDATION:
        return `Validation failed: ${baseMessage}. Please review the requirements and try again.`;

      case FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.BUSINESS_LOGIC:
        return `Business rule violation: ${baseMessage}. Please complete the required prerequisites.`;

      case FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.DATABASE:
        return 'A database error occurred. Please try again in a few moments.';

      case FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.NETWORK:
        return 'A network error occurred. Please check your connection and try again.';

      case FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.SYSTEM:
        return 'A system error occurred. Please try again or contact support if the problem persists.';

      default:
        return baseMessage;
    }
  }

  /**
   * Generate unique error ID for tracking
   */
  _generateErrorId() {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `FC_${timestamp}_${random}`.toUpperCase();
  }

  /**
   * Identify affected components based on error context
   */
  _identifyAffectedComponents(error, context) {
    const components = [];

    if (context.step) {
      components.push(`validation_step_${context.step}`);
    }

    if (context.scenario) {
      components.push(`scenario_${context.scenario}`);
    }

    if (context.requisitionId) {
      components.push(`requisition_${context.requisitionId}`);
    }

    const errorMessage = error.message || error.reason || '';

    if (errorMessage.includes('payment')) {
      components.push('payment_validation');
    }

    if (errorMessage.includes('delivery')) {
      components.push('delivery_validation');
    }

    if (errorMessage.includes('auto-close')) {
      components.push('auto_close_detection');
    }

    return components;
  }

  /**
   * Get specific user action steps based on error analysis
   */
  _getUserActionSteps(analysis) {
    const { FORCE_CLOSE_ERROR_HANDLING } = this.constants.forceClose;

    switch (analysis.category) {
      case FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.AUTHORIZATION:
        return [
          'Verify you are the requisition requester or assigned purchasing staff',
          'Contact the requisition owner for permission',
          'Check with purchasing head if needed',
        ];

      case FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.VALIDATION:
        return [
          'Review the validation error details',
          'Correct the identified issues',
          'Ensure all required fields are completed',
          'Try the operation again',
        ];

      case FORCE_CLOSE_ERROR_HANDLING.ERROR_CATEGORIES.BUSINESS_LOGIC:
        return [
          'Complete any pending payment requirements',
          'Ensure all deliveries are properly recorded',
          'Verify purchase order statuses',
          'Check for pending approvals',
        ];

      default:
        return [
          'Review the error details',
          'Correct any identified issues',
          'Try the operation again',
        ];
    }
  }

  /**
   * Create Error1 response - Authorization failures
   * Handles all user authorization related errors from CheckUser step
   */
  _createError1Response(authResult) {
    const { FORCE_CLOSE_ERRORS } = this.constants.forceClose;

    return {
      isEligible: false,
      buttonVisible: false,
      scenario: 'authorization_error',
      errorType: 'ERROR1_AUTHORIZATION',
      reason: typeof authResult.details === 'string' ? authResult.details : FORCE_CLOSE_ERRORS.ACCESS_DENIED,
      details: {
        step: 'CheckUser',
        errorCategory: 'Authorization',
        authorizationDetails: authResult.details,
        possibleCauses: [
          'User is not the requisition requester',
          'User is not assigned as purchasing staff',
          'Requisition not found',
          'Invalid user permissions'
        ],
        resolution: 'Contact the requisition requester or assigned purchasing staff to perform this action'
      },
    };
  }

  /**
   * Create Error2 response - Requisition status failures
   * Handles all RS status related errors from CheckRS step
   */
  _createError2Response(rsResult) {
    const { FORCE_CLOSE_ERRORS } = this.constants.forceClose;

    return {
      isEligible: false,
      buttonVisible: rsResult.showButton,
      scenario: 'rs_status_error',
      errorType: 'ERROR2_RS_STATUS',
      reason: rsResult.reason,
      details: {
        step: 'CheckRS',
        errorCategory: 'Requisition Status',
        statusDetails: rsResult.details,
        possibleCauses: [
          'Requisition is not fully approved yet',
          'Requisition is not in progress',
          'Requisition is still eligible for regular cancellation',
          'Invalid requisition status'
        ],
        resolution: rsResult.showButton
          ? 'Wait for requisition to be fully approved and in progress'
          : 'Use regular RS cancellation instead of force close'
      },
    };
  }

  /**
   * Create Error3 response - Business logic validation failures
   * Enhanced with comprehensive error handling for all validation scenarios
   */
  _createError3Response(validationResult) {
    const { FORCE_CLOSE_ERRORS, FORCE_CLOSE_ERROR_HANDLING } = this.constants.forceClose;

    let errorCategory = 'Business Logic Validation';
    let possibleCauses = [];
    let resolution = '';
    let severity = FORCE_CLOSE_ERROR_HANDLING.SEVERITY_LEVELS.MEDIUM;
    let recoveryStrategy = FORCE_CLOSE_ERROR_HANDLING.RECOVERY_STRATEGIES.USER_ACTION;

    // Enhanced error categorization for all validation scenarios
    if (validationResult.reason === FORCE_CLOSE_ERRORS.NO_DELIVERIES_YET) {
      errorCategory = 'Purchase Order Status';
      severity = FORCE_CLOSE_ERROR_HANDLING.SEVERITY_LEVELS.HIGH;
      possibleCauses = [
        'No deliveries have been made for any purchase orders',
        'All purchase orders are still pending delivery',
        'Purchase orders may need manual cancellation first'
      ];
      resolution = 'Manually cancel the Purchase Order first, then retry force close';
    } else if (validationResult.reason === FORCE_CLOSE_ERRORS.INVALID_PO_STATUS) {
      errorCategory = 'Purchase Order Status';
      severity = FORCE_CLOSE_ERROR_HANDLING.SEVERITY_LEVELS.HIGH;
      possibleCauses = [
        'Purchase order status is not FOR_DELIVERY, CLOSED, or CANCELLED',
        'Purchase order is in an invalid state for force close'
      ];
      resolution = 'Wait for purchase order to reach a valid status or contact administrator';
    } else if (validationResult.reason === FORCE_CLOSE_ERRORS.UNPAID_DELIVERIES) {
      errorCategory = 'Payment Validation';
      severity = FORCE_CLOSE_ERROR_HANDLING.SEVERITY_LEVELS.HIGH;
      possibleCauses = [
        'Some delivered items have not been paid',
        'Payment amounts do not match delivery amounts',
        'Payment requests are not approved'
      ];
      resolution = 'Complete payment for all delivered items before force closing';
    } else if (validationResult.reason === FORCE_CLOSE_ERRORS.DELIVERY_VALIDATION_ERROR) {
      errorCategory = 'Delivery Validation';
      severity = FORCE_CLOSE_ERROR_HANDLING.SEVERITY_LEVELS.HIGH;
      possibleCauses = [
        'Delivery receipt validation failed',
        'Delivery quantities are invalid',
        'Delivery status is not approved'
      ];
      resolution = 'Verify delivery receipts and ensure all deliveries are properly approved';
    } else if (validationResult.reason === FORCE_CLOSE_ERRORS.AUTO_CLOSE_DETECTED) {
      errorCategory = 'Auto-Close Detection';
      severity = FORCE_CLOSE_ERROR_HANDLING.SEVERITY_LEVELS.LOW;
      recoveryStrategy = FORCE_CLOSE_ERROR_HANDLING.RECOVERY_STRATEGIES.NO_RECOVERY;
      possibleCauses = [
        'All purchase orders are closed or cancelled',
        'No remaining quantities for canvassing',
        'No pending canvass sheet approvals',
        'Requisition meets criteria for automatic closure'
      ];
      resolution = 'Requisition should close automatically - no force close needed';
    } else if (validationResult.reason === FORCE_CLOSE_ERRORS.NO_VALID_SCENARIO) {
      errorCategory = 'Scenario Detection';
      severity = FORCE_CLOSE_ERROR_HANDLING.SEVERITY_LEVELS.MEDIUM;
      possibleCauses = [
        'No remaining quantities to canvass',
        'No pending canvass approvals',
        'No partial deliveries detected',
        'No draft documents to cancel'
      ];
      resolution = 'Requisition may already be complete or in an unexpected state';
    } else if (validationResult.reason?.includes('notes')) {
      errorCategory = 'Notes Validation';
      severity = FORCE_CLOSE_ERROR_HANDLING.SEVERITY_LEVELS.LOW;
      possibleCauses = [
        'Force close notes are required but not provided',
        'Notes exceed 500 character limit',
        'Notes contain invalid characters or emojis'
      ];
      resolution = 'Provide valid force close notes and retry';
    } else if (validationResult.reason?.includes('payment')) {
      errorCategory = 'Payment Validation';
      severity = FORCE_CLOSE_ERROR_HANDLING.SEVERITY_LEVELS.HIGH;
      possibleCauses = [
        'Payment prerequisite validation failed',
        'Payment calculation errors detected',
        'Payment request status issues'
      ];
      resolution = 'Review and complete payment requirements before force closing';
    } else if (validationResult.reason?.includes('delivery')) {
      errorCategory = 'Delivery Validation';
      severity = FORCE_CLOSE_ERROR_HANDLING.SEVERITY_LEVELS.HIGH;
      possibleCauses = [
        'Delivery validation failed',
        'Delivery completion issues detected',
        'Delivery receipt status problems'
      ];
      resolution = 'Review and complete delivery requirements before force closing';
    }

    // Enhanced debug information
    const enhancedDebugInfo = {
      hasRemainingQty: validationResult.details?.hasRemainingQty,
      hasPendingApproval: validationResult.details?.hasPendingApproval,
      isPartiallyDelivered: validationResult.details?.isPartiallyDelivered,
      hasDraftDocs: validationResult.details?.hasDraftDocs,
      autoCloseAnalysis: validationResult.details?.autoCloseAnalysis,
      paymentValidation: validationResult.details?.payments,
      deliveryValidation: validationResult.details?.deliveries,
      validationPath: validationResult.details?.validationPath,
      confidence: validationResult.details?.confidence,
    };

    return {
      isEligible: false,
      buttonVisible: false, // Fixed: If not eligible, button should not be visible
      scenario: 'not_eligible', // Add scenario field for consistency
      errorType: 'ERROR3_ENHANCED_VALIDATION',
      reason: validationResult.reason,
      details: {
        step: 'CheckPO/CheckRemQty/CheckCS',
        errorCategory,
        severity,
        recoveryStrategy,
        validationDetails: validationResult.details,
        possibleCauses,
        resolution,
        enhancedDebugInfo,
        errorId: this._generateErrorId(),
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * Create unexpected error response
   * Handles any unexpected errors during validation
   */
  _createUnexpectedErrorResponse(error, requisitionId) {
    return {
      isEligible: false,
      buttonVisible: false,
      scenario: 'unexpected_error',
      errorType: 'UNEXPECTED_ERROR',
      reason: 'An unexpected error occurred during force close validation',
      details: {
        step: 'Unknown',
        errorCategory: 'System Error',
        errorMessage: error.message,
        errorStack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
        requisitionId,
        timestamp: new Date().toISOString(),
        resolution: 'Please try again or contact system administrator if the problem persists'
      },
    };
  }


}

module.exports = ForceCloseService;
