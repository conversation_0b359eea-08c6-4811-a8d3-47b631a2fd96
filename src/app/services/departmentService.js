class DepartmentService {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      clientErrors,
      departmentRepository,
      userRepository,
      companyRepository,
      syncRepository,
    } = container;

    this.db = db;
    this.utils = utils;
    this.clientErrors = clientErrors;
    this.departmentEntity = entities.department;
    this.departmentRepository = departmentRepository;
    this.userRepository = userRepository;
    this.companyRepository = companyRepository;
    this.syncRepository = syncRepository;
    this.httpClient = new utils.HTTPClient({
      endpoint: `${process.env.CITYLAND_API_URL}/departments`,
    });
  }

  async syncDepartments(userDetails) {
    const result = await this.httpClient.get();
    const departments = result.data ?? [];
    const mappedDepartments = departments.map((department) => ({
      code: department.DEPTCD,
      name: department.DEPNME,
    }));

    await this.departmentRepository.syncDepartments(
      mappedDepartments,
      userDetails,
    );

    return await this.syncRepository.updateLastSynced('department');
  }

  async getDepartmentById(id) {
    const department = await this.departmentRepository.getDepartmentDetails(id);

    if (!department) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Department not found',
      });
    }

    return {
      ...department,
      isAssociation: department.code == process.env.ASSOCIATION_DEPARTMENT_CODE,
    };
  }
}

module.exports = DepartmentService;
