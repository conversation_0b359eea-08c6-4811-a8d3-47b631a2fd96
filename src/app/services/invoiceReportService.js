const {
  generateNumber,
  convertDateToDDMMMYYYY,
  convertToUSDateFormat,
} = require('../utils');
const {
  attachment: ATTACHMENT,
  purchaseOrder: PURCHASE_ORDER,
  deliveryReceipt: DELIVERY_RECEIPT,
  deliveryReceiptItem: DELIVERY_RECEIPT_ITEM,
} = require('../../domain/constants');
class InvoiceReportService {
  constructor({
    attachmentService,
    clientErrors,
    db,
    deliveryReceiptItemRepository,
    deliveryReceiptRepository,
    invoiceReportRepository,
    purchaseOrderRepository,
    requisitionRepository,
  }) {
    this.attachmentService = attachmentService;
    this.clientErrors = clientErrors;
    this.db = db;
    this.deliveryReceiptItemRepository = deliveryReceiptItemRepository;
    this.deliveryReceiptRepository = deliveryReceiptRepository;
    this.invoiceReportRepository = invoiceReportRepository;
    this.purchaseOrderRepository = purchaseOrderRepository;
    this.requisitionRepository = requisitionRepository;
    this.Sequelize = db.Sequelize;
  }

  async createInvoiceReport(data, userFromToken, transaction) {
    await this.#validateInvoiceReport(data, userFromToken);

    const irNumberResult = await this.#generateNumber(
      data.requisitionId,
      data.isDraft,
    );

    if (data.isDraft === 'true') {
      data.irDraftNumber = irNumberResult.number;
    } else {
      data.irNumber = irNumberResult.number;
    }

    data.createdBy = userFromToken.id;
    data.companyCode = irNumberResult.companyCode;

    try {
      const invoiceReport =
        await this.invoiceReportRepository.createInvoiceReport(data, {
          transaction,
        });

      if (invoiceReport) {
        await this.deliveryReceiptRepository.syncWithInvoiceReport(
          data.deliveryReceiptIds,
          invoiceReport.id,
          { transaction },
        );

        await this.attachmentService.syncAttachmentsByIds(
          [],
          data.attachmentIds,
          ATTACHMENT.MODELS.INVOICE,
          invoiceReport.id,
          userFromToken.id,
          transaction,
        );
      }

      return invoiceReport;
    } catch (error) {
      throw this.clientErrors.UNPROCESSABLE_ENTITY({
        message: error.message,
        error,
      });
    }
  }

  async getInvoiceReportById(id) {
    const invoiceReport = await this.invoiceReportRepository.getById(id, {

      include: [
        {
          model: this.db.userModel,
          as: 'createdByUser',
          attributes: ['id', 'firstName', 'lastName'],
        },
        {
          model: this.db.purchaseOrderModel,
          attributes: [
            'id',
            [
              this.Sequelize.fn(
                'CONCAT',
                'PO-',
                this.Sequelize.col('purchaseOrder->requisition.company_code'),
                this.Sequelize.col('purchaseOrder.po_letter'),
                this.Sequelize.col('purchaseOrder.po_number')
              ),
              'poNumber'
            ]
          ],
          as: 'purchaseOrder',
          include: [
            {
              model: this.db.requisitionModel,
              as: 'requisition',
              attributes: []
            }
          ]
        },
      ],
    });

    if (!invoiceReport) {
      throw this.clientErrors.NOT_FOUND({
        message: `Invoice Report with id of ${id} not found.`,
      });
    }

    return invoiceReport;
  }

  async getDeliveryReportsByInvoiceReportId(id, query) {
    const {
      search,
      limit,
      page,
      paginate,
      sortBy = JSON.stringify({ id: 'ASC' }),
    } = query;

    let whereQuery = {};
    if (search) {
      whereQuery = {
        [this.db.Sequelize.Op.or]: [
          this.db.Sequelize.where(
            this.db.Sequelize.fn(
              'CONCAT',
              'RR-',
              this.db.Sequelize.col('dr_number'),
            ),
            {
              [this.db.Sequelize.Op.iLike]: `%${search}%`,
            },
          ),
        ],
      };
    }

    const formattedOrder = [];
    for (const [field, direction] of Object.entries(JSON.parse(sortBy))) {
      switch (field) {
        case 'drNumber':
          formattedOrder.push(['drNumber', direction]);
          continue;
        case 'dateDelivered':
          formattedOrder.push(['latestDeliveryDate', direction]);
          continue;
        case 'status':
          formattedOrder.push(['latestDeliveryStatus', direction]);
          continue;
      }
    }

    const deliveryReports = await this.deliveryReceiptRepository.findAll({
      attributes: [
        'id',
        [
          this.db.Sequelize.fn(
            'CONCAT',
            'RR-',
            this.db.Sequelize.col('dr_number'),
          ),
          'drNumber',
        ],
        'invoiceNumber',
        'latestDeliveryDate',
        'latestDeliveryStatus',
        'issuedDate',
        'status',
      ],
      where: {
        invoiceId: id,
        ...whereQuery,
      },
      paginate,
      limit,
      page,
      order: formattedOrder,
    });

    return deliveryReports;
  }

  async getDeliveryReportItemsByInvoiceReportId(id, query) {
    const { limit, page, sortBy } = query;

    let whereQuery = {};

    const formattedOrder = [];
    for (const [field, direction] of Object.entries(
      JSON.parse(sortBy) ?? { drNumber: 'ASC', itemDes: 'ASC' },
    )) {
      formattedOrder.push([field, direction]);
    }

    const deliveryReportItems =
      await this.deliveryReceiptItemRepository.findAll({
        attributes: [
          'id',
          [
            this.db.Sequelize.fn(
              'CONCAT',
              'RR-',
              this.db.Sequelize.col('deliveryReceipt.dr_number'),
            ),
            'drNumber',
          ],
          'itemDes',
          'qtyOrdered',
          'qtyDelivered',
          'qtyReturned',
          'dateDelivered',
        ],
        include: [
          {
            attributes: [],
            model: this.db.deliveryReceiptModel,
            as: 'deliveryReceipt',
            where: {
              invoiceId: id,
            },
          },
        ],
        where: whereQuery,
        paginate: true,
        limit,
        page,
        order: formattedOrder,
      });

    return deliveryReportItems;
  }

  async updateInvoiceReport(id, data, userFromToken, transaction) {
    await this.#validateInvoiceReport(data, userFromToken, id);

    const invoiceReport = await this.invoiceReportRepository.getById(id);
    if (!invoiceReport) {
      throw this.clientErrors.NOT_FOUND({
        message: `Invoice Report with id of ${id} not found.`,
      });
    }

    if (!invoiceReport.isDraft) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Cannot update submitted invoice report.',
      });
    }

    if (data.isDraft !== invoiceReport.isDraft.toString()) {
      const irNumberResult = await this.#generateNumber(
        data.requisitionId,
        data.isDraft,
      );

      if (data.isDraft === 'true') {
        data.irDraftNumber = irNumberResult.number;
      } else {
        data.irNumber = irNumberResult.number;
        data.irDraftNumber = null;
      }

      data.companyCode = irNumberResult.companyCode;
    }

    try {
      const invoiceReport =
        await this.invoiceReportRepository.updateInvoiceReport(id, data, {
          transaction,
        });

      if (invoiceReport[0]) {
        await this.deliveryReceiptRepository.syncWithInvoiceReport(
          data.deliveryReceiptIds,
          invoiceReport[1][0].id,
          { transaction },
        );

        const currentAttachments =
          await this.attachmentService.getAttachments({
            model: ATTACHMENT.MODELS.INVOICE,
            modelId: invoiceReport[1][0].id,
          });

        await this.attachmentService.syncAttachmentsByIds(
          currentAttachments?.data.map((attachment) => attachment.id),
          data.attachmentIds,
          ATTACHMENT.MODELS.INVOICE,
          invoiceReport[1][0].id,
          userFromToken.id,
          transaction,
        );

        return invoiceReport[1][0].dataValues;
      }
    } catch (error) {
      throw this.clientErrors.UNPROCESSABLE_ENTITY({
        message: error.message,
        error,
      });
    }
  }

  async canCreateInvoice(requisitionId) {
    const purchaseOrders = await this.purchaseOrderRepository.findAll({
      attributes: ['id'],
      where: {
        requisitionId,
        status: PURCHASE_ORDER.PO_STATUS.FOR_DELIVERY,
      },
      include: [
        {
          attributes: ['id'],
          model: this.db.deliveryReceiptModel,
          as: 'deliveryReceipts',
          where: {
            invoiceId: null,
          },
        },
      ],
      subQuery: false,
    });

    return purchaseOrders.data.length > 0;
  }

  async #generateNumber(requisitionId, isDraft) {
    const numberType = isDraft === 'true' ? 'irDraftNumber' : 'irNumber';
    const requisition = await this.requisitionRepository.getById(requisitionId);

    if (!requisition) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition with id of ${requisitionId} not found.`,
      });
    }

    const companyCode = requisition.companyCode;

    const lastInvoiceReport = await this.invoiceReportRepository.findOne({
      attributes: ['id', numberType],
      where: {
        companyCode,
        [numberType]: {
          [this.Sequelize.Op.ne]: null,
        },
      },
      order: [[numberType, 'DESC']],
    });

    const result = {
      companyCode,
      number: null,
    };

    if (lastInvoiceReport) {
      const extractedNumber = lastInvoiceReport[numberType].replace(
        /IR-(TMP-)?/,
        '',
      );
      result.number = generateNumber(companyCode, extractedNumber);
    } else {
      result.number = generateNumber(companyCode);
    }

    return result;
  }

  async #validateInvoiceReport(data, userFromToken, invoiceId = null) {
    const requisition = await this.requisitionRepository.getById(
      data.requisitionId,
    );

    if (!requisition) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition with id of ${requisitionId} not found.`,
      });
    }

    const purchaseOrder =
      await this.purchaseOrderRepository.getPOWithItsDeliveryReceipts(
        data.purchaseOrderId,
        invoiceId,
      );

    if (
      !purchaseOrder ||
      purchaseOrder.status !== PURCHASE_ORDER.PO_STATUS.FOR_DELIVERY
    ) {
      throw this.clientErrors.UNPROCESSABLE_ENTITY({
        message: `Purchase order is not ready for delivery, has no delivery receipts, or has all delivery reports already in an invoice.`,
      });
    }

    if (purchaseOrder.requisitionId !== Number(data.requisitionId)) {
      throw this.clientErrors.UNPROCESSABLE_ENTITY({
        message: `Purchase order does not belong to this requisition.`,
      });
    }

    let deliveryReceiptIdsFromPO = [];
    if (purchaseOrder.deliveryReceipts.length) {
      deliveryReceiptIdsFromPO = purchaseOrder.deliveryReceipts.map(
        (dr) => dr.id,
      );
    }

    const missingDRIds = data.deliveryReceiptIds.filter(
      (drId) => !deliveryReceiptIdsFromPO.includes(drId),
    );
    if (missingDRIds.length > 0) {
      throw this.clientErrors.UNPROCESSABLE_ENTITY({
        message: `Delivery reports with ids ${missingDRIds.join(', ')} are not part of the given purchase order or already in a separate invoice.`,
      });
    }
  }

  async getInvoiceReportsFromRequisitionId(
    requisitionId,
    { search, page, limit, sortBy },
  ) {
    const formattedOrder = [];
    const sortByObject = sortBy ? JSON.parse(sortBy) : { updatedAt: 'DESC' };
    for (const [field, direction] of Object.entries(sortByObject)) {
      formattedOrder.push([field, direction]);
    }

    const invoiceReportResults = await this.invoiceReportRepository.findAll({
      attributes: [
        'id',
        [
          this.Sequelize.fn(
            'CONCAT',
            this.Sequelize.literal(
              "CASE WHEN \"invoice_reports\".\"is_draft\" = 'true' THEN 'TMP-' ELSE '' END",
            ),
            this.Sequelize.fn(
              'COALESCE',
              this.Sequelize.col('ir_number'),
              this.Sequelize.col('ir_draft_number'),
            ),
          ),
          'irNumber',
        ],
        'supplierInvoiceNo',
        'issuedInvoiceDate',
        'invoiceAmount',
        'updatedAt',
        'status',
      ],
      where: {
        [this.db.Sequelize.Op.and]: [
          {
            requisitionId,
          },
          search
            ? {
                [this.db.Sequelize.Op.or]: [
                  this.db.Sequelize.where(
                    this.db.Sequelize.literal(
                      `CONCAT('IR-', CASE WHEN "invoice_reports"."is_draft" = 'true' THEN 'TMP-' ELSE '' END, COALESCE("ir_number", "ir_draft_number"))`,
                    ),
                    { [this.db.Sequelize.Op.iLike]: `%${search}%` },
                  ),
                  {
                    supplierInvoiceNo: {
                      [this.db.Sequelize.Op.iLike]: `%${search}%`,
                    },
                  },
                ],
              }
            : null,
        ],
      },
      paginate: true,
      page,
      limit,
      order: formattedOrder,
    });

    return {
      data: invoiceReportResults.data?.map((invoiceReport) => ({
        ...invoiceReport,
        issuedInvoiceDate: convertDateToDDMMMYYYY(
          invoiceReport.issuedInvoiceDate,
        ),
        updatedAt: convertDateToDDMMMYYYY(invoiceReport.updatedAt),
      })),
      total: invoiceReportResults.total,
    };
  }

  async validateInvoiceIds(invoiceIds, purchaseOrderId) {
    const invoices = await this.invoiceReportRepository.findAll({
      where: {
        id: {
          [this.db.Sequelize.Op.in]: invoiceIds
        },
        purchaseOrderId
      },
      paginate: false,
    });

    if (invoices.data.length !== invoiceIds.length) {
      throw this.clientErrors.UNPROCESSABLE_ENTITY({
        message: 'All invoice reports must belong to the same purchase order.'
      });
    }

    return true;
  }

  async hasUnassignedInvoiceReportToPaymentRequest(requisitionId) {
    const invoiceReports = await this.invoiceReportRepository.findAll({
      where: {
        isDraft: false,
        requisitionId,
        paymentRequestId: null
      },
      attributes: ['id'],
      raw: true,
    });

    return invoiceReports.data.length > 0;
  }

  async getInvoiceReportsFromPurchaseOrderId(purchaseOrderId, paymentRequestId) {
    const invoiceReports = await this.invoiceReportRepository.findAll({
      attributes: [
        'id',
        'irNumber',
        'invoiceAmount',
        'paymentRequestId',
      ],
      where: {
        isDraft: false,
        purchaseOrderId,
        paymentRequestId: {
          [this.db.Sequelize.Op.or]: [null, paymentRequestId? paymentRequestId: null]
        }
      },
      paginate: false,
    });

    return invoiceReports.data;
  }

  async getInvoiceReportsByPaymentRequestId(paymentRequestId, query) {
    const {
      limit,
      page,
      paginate = true,
      sortBy = JSON.stringify({ irNumber: 'ASC' }),
    } = query;

    const formattedOrder = [];
    for (const [field, direction] of Object.entries(JSON.parse(sortBy))) {
      formattedOrder.push([field, direction]);
    }

    const invoiceReports = await this.invoiceReportRepository.findAll({
      attributes: [
        'id',
        'irNumber',
        'supplierInvoiceNo',
        'invoiceAmount',
      ],
      where: {
        isDraft: false,
        paymentRequestId,
      },
      paginate,
      limit,
      page,
      order: formattedOrder,
    });

    return invoiceReports;
  }
}

module.exports = InvoiceReportService;
