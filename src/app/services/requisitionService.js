const fs = require('fs');
class RequisitionService {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      userRepository,
      requisitionRepository,
      requisitionItemListRepository,
      tomItemRepository,
      commentRepository,
      attachmentRepository,
      supplierRepository,
      projectRepository,
      companyRepository,
      constants,
      projectApprovalRepository,
      clientErrors,
      requisitionApproverRepository,
      departmentApprovalRepository,
      fastify,
      itemRepository,
      nonOfmItemRepository,
      notificationRepository,
      historyRepository,
      canvassRequisitionRepository,
      purchaseOrderRepository,
      deliveryReceiptRepository,
      rsPaymentRequestRepository,
      notificationService,
      departmentRepository,
      departmentAssociationApprovalRepository,
      noteService,
      noteRepository,
    } = container;

    this.db = db;
    this.utils = utils;
    this.userRepository = userRepository;
    this.requisitionRepository = requisitionRepository;
    this.requisitionItemListRepository = requisitionItemListRepository;
    this.commentRepository = commentRepository;
    this.tomItemRepository = tomItemRepository;
    this.Sequelize = db.Sequelize;
    this.attachmentRepository = attachmentRepository;
    this.supplierRepository = supplierRepository;
    this.projectRepository = projectRepository;
    this.companyRepository = companyRepository;
    this.constants = constants;
    this.projectApprovalRepository = projectApprovalRepository;
    this.clientErrors = clientErrors;
    this.requisitionApproverRepository = requisitionApproverRepository;
    this.departmentApprovalRepository = departmentApprovalRepository;
    this.attachmentModel = db.attachmentModel;
    this.commentModel = db.commentModel;
    this.requisitionModel = db.requisitionModel;
    this.itemRepository = itemRepository;
    this.nonOfmItemRepository = nonOfmItemRepository;
    this.fastify = fastify;
    this.notificationRepository = notificationRepository;
    this.historyRepository = historyRepository;
    this.requisitionItemListEntity = entities.requisitionItemList;
    this.canvassRequisitionRepository = canvassRequisitionRepository;
    this.purchaseOrderRepository = purchaseOrderRepository;
    this.deliveryReceiptRepository = deliveryReceiptRepository;
    this.rsPaymentRequestRepository = rsPaymentRequestRepository;
    this.notificationService = notificationService;
    this.departmentRepository = departmentRepository;
    this.departmentAssociationApprovalRepository =
      departmentAssociationApprovalRepository;
    this.noteService = noteService;
    this.noteRepository = noteRepository;
  }

  formatCompanyCode(companyCode) {
    return companyCode.padStart(2, '0');
  }

  async getCompanyCode(companyId) {
    const company = await this.companyRepository.getByCompanyId(companyId);

    if (!company) {
      throw this.clientErrors.NOT_FOUND({ message: 'Company not found' });
    }

    return this.formatCompanyCode(company.code.toString());
  }

  async getExistingRequisition(requisitionId, options = {}) {
    const requisition = await this.requisitionRepository.getById(
      requisitionId,
      {
        include: [
          {
            association: 'department',
            as: 'department',
            required: false,
            attributes: ['id', 'name'],
          },
          {
            association: 'company',
            as: 'company',
            required: false,
            attributes: ['id', 'name'],
          },
          {
            association: 'project',
            as: 'project',
            required: false,
            attributes: ['id', 'name'],
          },
          {
            association: 'createdByUser',
            as: 'createdByUser',
            required: false,
            attributes: ['id', 'firstName', 'lastName', 'username', 'roleId'],
          },
          {
            association: 'chargeToCompany',
            as: 'chargeToCompany',
            required: false,
            attributes: ['id', 'name'],
          },
          {
            association: 'chargeToSupplier',
            as: 'chargeToSupplier',
            required: false,
            attributes: ['id', 'name'],
          },
          {
            association: 'chargeToProject',
            as: 'chargeToProject',
            required: false,
            attributes: ['id', 'name'],
          },
        ],
        attributes: [
          'id',
          'chargeToId',
          'purpose',
          'projectId',
          'companyId',
          'departmentId',
          'createdBy',
          'chargeTo',
          'dateRequired',
        ],
        ...options,
      },
    );

    if (!requisition) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Requisition slip not found',
      });
    }

    return requisition;
  }

  async generateRSNumberCode(companyCode, isDraft = 'false') {
    let whereClause;
    const isRSDraft = isDraft == 'true'; /* String passed by FE */
    const numberField = isRSDraft ? 'draftRsNumber' : 'rsNumber';
    if (isRSDraft) {
      whereClause = {
        status: 'rs_draft',
        draftRsNumber: {
          [this.Sequelize.Op.ne]: null,
        },
      };
    } else {
      whereClause = {
        status: {
          [this.Sequelize.Op.ne]: 'rs_draft',
        },
        rsNumber: {
          [this.Sequelize.Op.ne]: null,
        },
      };
    }

    const lastRequisition = await this.requisitionRepository.findOne({
      where: whereClause,
      order: [[numberField, 'DESC']],
    });

    let lastRSNumber = lastRequisition?.rsNumber ?? '0';

    if (isRSDraft) {
      lastRSNumber = lastRequisition?.draftRsNumber ?? '0';
    }

    const { nextLetter, nextNumber } = this.utils.getNextNumberAndLetter(
      lastRSNumber,
      lastRequisition?.rsLetter,
    );

    return {
      rsNumber: nextNumber,
      rsLetter: nextLetter,
    };
  }

  async getOfmRequisitionItemLists(requisitionId, paginate = true) {
    return this.requisitionItemListRepository.findAll({
      where: {
        requisitionId,
      },
      paginate,
      include: [
        {
          model: this.db.itemModel,
          as: 'item',
        },
      ],
    });
  }

  async getNonOfmRequisitionItemLists(requisitionId, paginate = true) {
    try {
      const lists = await this.requisitionItemListRepository.findAll({
        where: {
          requisitionId,
        },
        paginate,
        include: [
          {
            model: this.db.nonOfmItemModel,
            as: 'nonOfmItem',
          },
        ],
      });

      const transformedData = lists.data.map((list) => {
        const { nonOfmItem, ...rest } = list;
        return {
          ...rest,
          item: nonOfmItem,
        };
      });

      return {
        data: transformedData,
        total: lists.total,
      };
    } catch (error) {
      console.error('Error in getNonOfmRequisitionItemLists:', error);
      throw error;
    }
  }

  async getTransferRequisitionItemLists(requisitionId, paginate = true) {
    return this.tomItemRepository.findAll({
      where: { requisitionId },
      paginate,
    });
  }

  async getChargeToList(payload) {
    this.fastify.log.info(`Getting Charge to List of ${payload}`);
    if (!payload) {
      return this.fastify.log.error(`There is no category`);
    }

    let result = [];

    switch (payload.toLowerCase()) {
      case 'company':
        result = await this.companyRepository.findAll({
          where: { category: 'company' },
          attributes: [
            'id',
            [
              this.db.Sequelize.fn(
                'CONCAT',
                this.db.Sequelize.col('initial'),
                ' - ',
                this.db.Sequelize.col('name'),
              ),
              'name',
            ],
          ],
          paginate: false,
          order: [[this.db.Sequelize.col('name'), 'ASC']],
        });

        break;
      case 'project':
        result = await this.projectRepository.findAll({
          attributes: ['id', 'name'],
          include: [
            {
              model: this.db.companyModel,
              as: 'taggedCompanies',
              attributes: ['id'],
            },
          ],
          paginate: false,
          order: [[this.db.Sequelize.col('name'), 'ASC']],
        });

        break;
      case 'association':
        result = await this.companyRepository.findAll({
          where: { category: 'association' },
          attributes: ['id', 'name'],
          paginate: false,
          order: [[this.db.Sequelize.col('name'), 'ASC']],
        });
        break;
      case 'supplier':
        result = this.supplierRepository.findAll({
          attributes: ['id', 'name'],
          paginate: false,
          order: [[this.db.Sequelize.col('name'), 'ASC']],
        });
        break;
    }

    this.fastify.log.info(`Successfully loaded Charge to List of ${payload}`);

    return result;
  }

  async getDeliveryAddresses() {
    const [companies, projects] = await Promise.all([
      this.db.companyModel.findAll({
        attributes: ['id', 'address'],
        order: [[this.db.Sequelize.col('address'), 'ASC']],
      }),
    ]);

    return { companies, projects };
  }

  async formatRSNumber(requisition) {
    const { status, draftRsNumber, rsNumber, companyCode, rsLetter } =
      requisition;
    const isDraft = status === 'rs_draft';
    const prefix = isDraft ? 'RS-TMP-' : 'RS-';
    const number = isDraft ? draftRsNumber : rsNumber;

    return `${prefix}${companyCode}${rsLetter}${number}`;
  }

  async commentBadge(userId, requisitionId) {
    const commentsCount = await this.commentRepository.count({
      where: {
        commentedBy: userId,
        model: 'requisition',
        modelId: requisitionId,
      },
      include: [
        {
          model: this.db.commentBadgeModel,
          as: 'commentBadge',
        },
      ],
    });

    const badgeCount = await this.commentRepository.count({
      where: {
        commentedBy: userId,
        model: 'requisition',
        modelId: requisitionId,
      },
      include: [
        {
          model: this.db.commentBadgeModel,
          as: 'commentBadge',
          required: true,
        },
      ],
    });

    return commentsCount === badgeCount ? false : true;
  }

  async attachmentBadge(userId, requisitionId) {
    const attachmentsCount = await this.attachmentRepository.count({
      where: {
        model: 'requisition',
        modelId: requisitionId,
        userId,
      },
      include: [
        {
          model: this.db.attachmentBadgeModel,
          as: 'attachmentBadge',
        },
      ],
    });

    const badgeCount = await this.attachmentRepository.count({
      where: {
        model: 'requisition',
        modelId: requisitionId,
        userId,
      },
      include: [
        {
          model: this.db.attachmentBadgeModel,
          as: 'attachmentBadge',
          required: true,
        },
      ],
    });

    return attachmentsCount === badgeCount ? false : true;
  }

  async isValidChargeTo({ chargeTo, chargeToId }) {
    if (chargeTo === '') {
      return true;
    }

    if (!this.constants.requisition.CHARGE_TO_LIST.includes(chargeTo)) {
      return false;
    }

    if (
      chargeTo === 'supplier' &&
      !(await this.supplierRepository.getById(chargeToId))
    ) {
      return false;
    }

    if (
      chargeTo === 'project' &&
      !(await this.projectRepository.getById(chargeToId))
    ) {
      return false;
    }

    if (
      chargeTo === 'association' &&
      !(await this.companyRepository.getById(chargeToId, {
        category: 'association',
      }))
    ) {
      return false;
    }

    if (
      chargeTo === 'company' &&
      !(await this.companyRepository.getById(chargeToId))
    ) {
      return false;
    }

    return true;
  }

  async rsApprovers({
    chargeTo,
    chargeToId,
    departmentId,
    userFromToken,
    transaction,
  }) {
    const { supervisor } = userFromToken;
    // const userDepartmentId = department?.id || null;
    const userSupervisorId = supervisor?.id || null;
    const userId = userFromToken.id;

    if (chargeTo === '') {
      return [];
    }

    // TODO: add handling of invalid chargeTo
    let response = [];
    if (chargeTo === 'project') {
      const { data, total } = await this.projectApprovalRepository.findAll(
        {
          where: {
            projectId: chargeToId,
            approvalTypeCode: 'RS',
          },
          include: [
            {
              model: this.db.userModel,
              as: 'approver',
              attributes: [],
              where: {
                status: 'active',
              },
              required: true,
            },
          ],
          attributes: {
            include: [
              [this.Sequelize.literal("'project'"), 'modelType'],
              [this.Sequelize.col('project_id'), 'modelId'],
              'level',
              'approverId',
            ],
          },
        },
        { transaction },
      );

      if (total > 0) {
        response.push(...data);
      }
    }

    if (departmentId && ['project', 'company'].includes(chargeTo)) {
      const { data, total } = await this.departmentApprovalRepository.findAll({
        where: {
          approvalTypeCode: 'RS',
          departmentId,
        },
        include: [
          {
            model: this.db.userModel,
            as: 'approver',
            attributes: [],
            where: { status: 'active' },
            required: true,
          },
        ],
        attributes: {
          include: [
            [this.Sequelize.literal("'department'"), 'modelType'],
            [
              this.Sequelize.col('department_approvals.department_id'),
              'modelId',
            ],
            'level',
            'approverId',
          ],
        },
        transaction,
      });

      if (total > 0) {
        response.push(...data);
      }
    }

    // TODO: implement supervisor approval
    // if (true) {
    if (userSupervisorId) {
      const userSupervisorData = await this.userRepository.findOne({
        where: {
          id: userSupervisorId,
          status: 'active',
        },
      });

      if (userSupervisorData) {
        response.push({
          modelId: userSupervisorId,
          // modelId: userId,
          approverId: userSupervisorId,
          // approverId: userId, // hardcode approver id for now since the feature for assigning supervisor is not yet implemented
          level: 0,
          status: 'pending',
          modelType: 'user',
        });
      }
    }

    return response;
  }

  async assignRSApprovers({
    rsApprovers,
    category,
    requisitionId,
    transaction,
  }) {
    if (rsApprovers.length === 0) {
      this.fastify.log.info(`NO_APPROVERS_TO_ASSIGN`);
      return;
    }

    const sortedApprovers = [...rsApprovers].sort((a, b) => {
      const getModelTypePriority = (modelType) => {
        if (modelType === 'user') return 1;
        if (modelType === 'project') return 2;
        if (modelType === 'department') return 3;
        return 4;
      };
      const modelTypeComparison =
        getModelTypePriority(a.modelType) - getModelTypePriority(b.modelType);
      if (modelTypeComparison !== 0) return modelTypeComparison;

      if (a.isOptionalApprover !== b.isOptionalApprover) {
        return a.isOptionalApprover ? 1 : -1;
      }

      return a.level - b.level;
    });

    const approvers = sortedApprovers.map((approver, index) => {
      return {
        modelId: approver.modelId,
        approverId: approver.approverId,
        level: category === 'company' ? approver.level + 1 : approver.level,
        status: 'pending',
        modelType: approver.modelType,
        requisitionId,
        isOptionalApprover: approver.isOptionalApprover,
        optionalApproverItemIds: approver.optionalApproverItemIds,
        approverLevel: index + 1,
      };
    });

    try {
      return await this.requisitionApproverRepository.bulkCreate(approvers, {
        transaction,
      });
    } catch (error) {
      this.fastify.log.error(`ERROR_IN_ASSIGN_RS_APPROVERS: ${error}`);
      throw error;
    }
  }

  async updateRequisitionDetails(id, payload) {
    this.fastify.log.info(`Updating Requisition Details`);

    payload.companyCode = await this.getCompanyCode(payload.companyId);
    await this.requisitionModel.update(payload, {
      where: {
        id,
      },
    });
    return this.fastify.log.info(`Successfully updated Requisition Details `);
  }

  //Comment edit

  // async updateRequisitionComment(payload) {
  //   const { id, comment } = payload;
  //   this.fastify.log.info(`Updating Requisition Comment`);
  //   await this.commentModel.update(
  //     { comment },
  //     {
  //       where: {
  //         id,
  //       },
  //     },
  //   );

  //   return this.fastify.log.info(`Successfully updated Requisition Comment`);
  // }

  async updateRequisitionAttachments(payload) {
    const {
      userFromToken,
      attachments = [],
      updateAttachments = [],
      requisitionId,
    } = payload;
    if (!attachments) {
      return true;
    }
    this.fastify.log.info(`Updating Requisition Attachments`);

    const toBeDeleted = [];

    if (attachments.length > 0) {
      this.fastify.log.info(`Attachment is new Creating Attachment...`);
      await this.createAttachments({
        userFromToken,
        body: {
          attachments,
          requisitionId,
        },
      });
      this.fastify.log.info(`Successfully Created an Attachment`);
    }

    if (updateAttachments.length > 0) {
      try {
        updateAttachments.forEach(async (attachment) => {
          this.fastify.log.info(`Unlinking Attachment`);
          fs.unlinkSync(`./upload/${attachment.path}`);
          this.fastify.log.info(`Successfully unlinked Attachment`);
          toBeDeleted.push(attachment.id);
        });

        this.fastify.log.info(`Deleting Attachments`);

        await this.attachmentModel.destroy({
          where: {
            id: {
              [this.Sequelize.Op.in]: toBeDeleted,
            },
          },
        });

        this.fastify.log.info(`Successfully Deleted Attachment`);
      } catch (error) {
        throw error;
      }
    }

    return this.fastify.log.info(
      `Successfully updated Requisition Attachments`,
    );
  }

  async hasCompanyRequisitions(companyId) {
    const requisitions = await this.requisitionRepository.count({
      where: {
        [this.Sequelize.Op.or]: [
          { companyId },
          {
            chargeTo: 'association',
            chargeToId: companyId,
          },
        ],
      },
    });

    return requisitions > 0;
  }

  async createAttachments(request, transaction = null) {
    const { userFromToken } = request;
    const { attachments, requisitionId } = request.body;

    this.fastify.log.info(`Adding attachment to requisition slip`);
    const paths = attachments.map((file) => ({
      path: `/requisitions/${file.filePath}`,
      fileName: file.originalname,
    }));

    this.fastify.log.info(`Checking if requisition slip is valid`);
    const requisition = await this.requisitionRepository.getById(
      requisitionId,
      { transaction },
    );

    const attachmentTransaction = await this.db.sequelize.transaction();

    let results = [];

    try {
      await Promise.all(
        paths.map(async (file) => {
          await this.attachmentRepository
            .create(
              {
                model: 'requisition',
                modelId: requisition.id,
                userId: userFromToken.id,
                path: file.path,
                fileName: file.fileName,
              },
              { attachmentTransaction },
            )
            .then((values) => results.push(values));
        }),
      );

      await attachmentTransaction.commit();
    } catch (error) {
      this.fastify.log.info(`Error in attachment transaction: ${error}`);
      await attachmentTransaction.rollback();
      throw error;
    }
    this.fastify.log.info(`Successfully added attachment to requisition slip`);
    return results;
  }

  async createComment(request) {
    const { userFromToken } = request;
    const { comment, requisitionId } = request.body;

    this.fastify.log.info(`Adding comment to requisition slip`);
    this.fastify.log.info(`Checking if requisition slip is valid`);
    const requisition = await this.requisitionRepository.getById(requisitionId);

    let result;

    try {
      result = await this.commentRepository.create({
        model: 'requisition',
        modelId: requisition.id,
        commentedBy: userFromToken.id,
        comment,
      });
    } catch (error) {
      return error;
    }

    this.fastify.log.info(`Successfully added comment to requisition slip`);
    return result;
  }

  async getComments(request) {
    const { id } = request;

    this.fastify.log.info(`Getting comments for requisition ID: ${id}`);
    let commentDateFrom = '';
    let commentDateTo = '';
    if (request.query) {
      commentDateFrom = request.query.commentDateFrom;
      commentDateTo = request.query.commentDateTo;
    }

    const whereClause = {
      modelId: parseInt(id),
      model: 'requisition',
    };

    if (commentDateFrom || commentDateTo) {
      whereClause.createdAt = {};

      if (commentDateFrom) {
        whereClause.createdAt[this.db.Sequelize.Op.gte] = commentDateFrom;
      }

      if (commentDateTo) {
        whereClause.createdAt[this.db.Sequelize.Op.lte] = commentDateTo;
      }
    }

    const comments = await this.commentRepository.findAll({
      where: whereClause,
      include: [
        {
          model: this.db.userModel,
          as: 'userComment',
          attributes: ['id', 'firstName', 'lastName'],
        },
        {
          model: this.db.commentBadgeModel,
          as: 'commentBadge',
        },
      ],
    });

    const groupedData = {};

    // comments.data.forEach((comment) => {
    //   const createdDate = comment.createdAt.toISOString().split('T')[0]; // Extract date from createdAt
    //   const user = comment.userComment;

    //   if (!groupedData[createdDate]) {
    //     groupedData[createdDate] = {};
    //   }

    //   if (!groupedData[createdDate][user.id]) {
    //     groupedData[createdDate][user.id] = {
    //       id: user.id,
    //       firstName: user.firstName,
    //       lastName: user.lastName,
    //       comments: [],
    //     };
    //   }

    //   groupedData[createdDate][user.id].noteBadge =
    //     comment.commentBadge === null ? true : false;

    //   groupedData[createdDate][user.id].comments.push({
    //     id: comment.id,
    //     comment: comment.comment,
    //   });
    // });

    // const result = Object.entries(groupedData).map(([createdAt, users]) => ({
    //   data: Object.values(users),
    //   createdAt,
    // }));
    return comments;
  }

  async getAttachments(request) {
    const { id } = request;

    this.fastify.log.info(`Getting attachments for requisition ID: ${id}`);
    let attachmentDateFrom = '';
    let attachmentDateTo = '';

    if (request.query) {
      attachmentDateFrom = request.query.attachmentDateFrom;
      attachmentDateTo = request.query.attachmentDateTo;
    }

    const whereClause = {
      modelId: id,
      model: 'requisition', // TODO: make this dynamic
    };

    //Check for attachmentDateFrom and attachmentDateTo in the request query
    if (attachmentDateFrom || attachmentDateTo) {
      whereClause.createdAt = {};

      if (attachmentDateFrom) {
        whereClause.createdAt[this.db.Sequelize.Op.gte] = attachmentDateFrom;
      }

      if (attachmentDateTo) {
        whereClause.createdAt[this.db.Sequelize.Op.lte] = attachmentDateTo;
      }
    }

    const attachments = await this.attachmentRepository.findAll({
      where: whereClause,
      paginate: false,
      include: [
        {
          model: this.db.userModel,
          as: 'userAttachment',
          attributes: ['id', 'firstName', 'lastName'],
        },
        {
          model: this.db.attachmentBadgeModel,
          as: 'attachmentBadge',
        },
      ],
    });

    const hasNewNotifications = attachments.data.some((attachment) =>
      attachment.attachmentBadge === null ? true : false,
    );

    const attachmentsData = attachments.data.map((attachment) => ({
      id: attachment?.id,
      fileName: attachment?.fileName,
      model: attachment?.model,
      modelId: attachment?.modelId,
      path: attachment?.path,
      userAttachment: attachment?.userAttachment,
      createdAt: attachment?.createdAt,
      updatedAt: attachment?.updatedAt,
    }));

    const result = {
      data: attachmentsData,
      hasNewNotifications,
      total: attachments.total,
    };

    return result;
  }

  async createItemList(request, transaction) {
    const { body, params } = request;
    const { requisitionId } = params;

    const parsed = JSON.parse(JSON.stringify(body.itemList));

    this.fastify.log.info(
      `Creating Item list for Requisition ${requisitionId}`,
    );

    const requisition = await this.requisitionRepository.getById(
      requisitionId,
      { transaction },
    );

    if (!requisition) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition with id of ${requisitionId} not found`,
      });
    }

    const requisitionItemList = await Promise.all(
      parsed.map(async (items) => {
        let item;
        if (requisition.type === 'ofm') {
          item = await this.itemRepository.getById(items.id, { transaction });
        }

        if (requisition.type === 'ofm-tom') {
          item = await this.itemRepository.getById(items.id, { transaction });
        }

        if (requisition.type === 'non-ofm') {
          item = await this.nonOfmItemRepository.getById(items.id, {
            transaction,
          });
        }

        if (requisition.type === 'non-ofm-tom') {
          item = await this.nonOfmItemRepository.getById(items.id, {
            transaction,
          });
        }

        if (requisition.type === 'transfer') {
          item = await this.tomItemRepository.getById(items.id, {
            transaction,
          });
        }

        if (!item) {
          throw this.clientErrors.NOT_FOUND({
            message: `Item with id of ${items.id} not found`,
          });
        }

        if (requisition.type === 'ofm' || requisition.type === 'ofm-tom') {
          const currentBalance = item.remainingGfq ?? item.gfq;

          if (currentBalance < items.quantity) {
            throw this.clientErrors.BAD_REQUEST({
              message: `Item with name of ${item.itmDes} has insufficient quantity`,
            });
          }

          if (body.isDraft === 'false') {
            item.gfq_balance = currentBalance - items.quantity;
          }

          await this.itemRepository.updateItem(item, { transaction });
        }

        return await this.requisitionItemListRepository.create(
          {
            itemId: items.id,
            quantity: items.quantity,
            notes: items.notes,
            requisitionId,
            itemType: requisition.type,
            accountCode: items.accountCode,
            ...(items?.ofmListId && { ofmListId: items.ofmListId }),
          },
          { transaction },
        );
      }),
    );

    this.fastify.log.info(`Created Item list for Requisition ${requisitionId}`);

    return requisitionItemList;
  }

  async updateItemList(requisitionId, itemList, isDraft) {
    const { REQUISITION_STATUS } = this.constants.requisition;

    try {
      this.fastify.log.info('Updating item list');

      const requisition =
        await this.requisitionRepository.getById(requisitionId);

      if (!requisition) {
        throw this.clientErrors.NOT_FOUND({
          message: `Requisition with id of ${requisitionId} not found`,
        });
      }

      const result = await this.requisitionModel.sequelize.transaction(
        async (t) => {
          if (
            !(
              (requisition.type === 'non-ofm' ||
                requisition.type === 'non-ofm-tom') &&
              requisition.status === 'assigned'
            )
          ) {
            this.fastify.log.info(
              `Deleting existing items for requisitionId: ${requisitionId}`,
            );
            await this.requisitionItemListRepository.destroyByRequisitionId(
              requisitionId,
              t,
            );
          }

          this.fastify.log.info('Creating new items');
          let previousItemList;
          if (requisition.type === 'ofm' || requisition.type === 'ofm-tom') {
            previousItemList =
              await this.requisitionItemListRepository.getAllRequisitionItems({
                requisitionId,
                type: requisition.type,
              });
          }
          const requisitionItemList = await Promise.all(
            itemList.map(async (items) => {
              let item;
              if (
                requisition.type === 'ofm' ||
                requisition.type === 'ofm-tom'
              ) {
                item = await this.itemRepository.getById(items.id);
              }
              if (
                (requisition.type === 'non-ofm' ||
                  requisition.type === 'non-ofm-tom') &&
                requisition.status !== 'assigned'
              ) {
                item = await this.nonOfmItemRepository.getById(items.id);
              }
              if (requisition.type === 'transfer') {
                item = await this.tomItemRepository.getById(items.id);
              }

              if (!item && requisition.status !== 'assigned') {
                throw this.clientErrors.NOT_FOUND({
                  message: `Item with name of ${item.itmDes} not found`,
                });
              }

              if (
                requisition.type === 'ofm' ||
                requisition.type === 'ofm-tom'
              ) {
                const previousItem = previousItemList?.data.find(
                  (item) => item.itemId === items.id,
                );

                const currentBalance = item.remainingGfq ?? item.gfq;
                const previousQty = previousItem?.quantity;

                if (currentBalance < items.quantity) {
                  throw this.clientErrors.BAD_REQUEST({
                    message: `Item with name of ${item.itmDes} has insufficient quantity`,
                  });
                }

                if (isDraft === 'false') {
                  item.gfq_balance = currentBalance - items.quantity;
                }

                if (requisition.status === REQUISITION_STATUS.SUBMITTED) {
                  item.gfq_balance =
                    items.quantity > previousQty
                      ? currentBalance - (items.quantity - previousQty)
                      : currentBalance + (previousQty - items.quantity);
                }

                await this.itemRepository.updateItem(item, { transaction: t });

                if (previousQty > items.quantity) {
                  this.#removeAdditionalApprovers({
                    transaction: t,
                    requisitionId,
                    itemIds: [items.id],
                  });
                } else {
                  const addOptionalApprovers =
                    await this.#shouldAddOptionalApprovers([items]);

                  if (addOptionalApprovers.length !== 0) {
                    await this.#addOptionalApprovers({
                      transaction: t,
                      requisition,
                      itemIds: addOptionalApprovers,
                    });
                  }
                }
              }

              return await this.requisitionItemListRepository.create(
                {
                  requisitionId: Number(requisitionId),
                  itemId: items.id,
                  quantity: items.quantity,
                  notes: items.notes,
                  itemType: requisition.type,
                  accountCode: items.acctCd,
                  ...(items?.ofmListId && { ofmListId: items.ofmListId }),
                },
                { transaction: t },
              );
            }),
          );

          if (requisition.type === 'ofm' || requisition.type === 'ofm-tom') {
            await this.#requisitionItemListDeleteItems({
              requisitionId,
              requisitionItemList: previousItemList.data || [],
              itemList,
              transaction: t,
            });
          }

          return requisitionItemList;
        },
      );

      this.fastify.log.info(
        `Successfully updated Item list for Requisition ${requisitionId}`,
      );
      return result;
    } catch (error) {
      this.fastify.log.error(
        `Error in updateItemList: ${error.message || error}`,
      );
      throw error;
    }
  }

  async deleteRequisitionItems(requisitionId) {
    try {
      this.fastify.log.info('Deleting requisition items');

      const requisition =
        await this.requisitionRepository.getById(requisitionId);

      if (!requisition) {
        throw this.clientErrors.NOT_FOUND({
          message: `Requisition with id of ${requisitionId} not found`,
        });
      }

      const result = await this.requisitionModel.sequelize.transaction(
        async (t) => {
          this.fastify.log.info(
            `Deleting items for requisitionId: ${requisitionId}`,
          );

          if (requisition.type === 'ofm') {
            const requisitionItems =
              await this.requisitionItemListRepository.getAllRequisitionItems({
                requisitionId,
                type: 'ofm',
              });

            if (requisitionItems.rows?.length) {
              await Promise.all(
                requisitionItems.rows.map(async (reqItem) => {
                  const item = reqItem.item;
                  if (item) {
                    const currentBalance = item.remainingGfq ?? item.gfq;
                    item.gfq_balance = currentBalance + reqItem.quantity;
                    await this.itemRepository.updateItem(item, {
                      transaction: t,
                    });
                  }
                }),
              );
            }
          }

          const deleteResult =
            await this.requisitionItemListRepository.destroyByRequisitionId(
              requisitionId,
              t,
            );
          return { success: true, deletedCount: deleteResult };
        },
      );

      this.fastify.log.info(
        `Successfully deleted ${result.deletedCount} items for Requisition ${requisitionId}`,
      );
      return result;
    } catch (error) {
      this.fastify.log.error(
        `Error in deleteRequisitionItems: ${error.message || error}`,
      );
      throw error;
    }
  }

  async assignRequisition(requisitionId, assignedTo, type, userFromToken) {
    this.fastify.log.info(`Assigning Requisition`);

    const { NOTIFICATION_TYPES, NOTIFICATION_DETAILS } =
      this.constants.notification;

    await this.requisitionRepository.assignRequisition(
      requisitionId,
      assignedTo,
      type,
      userFromToken,
    );

    await this.notificationService.sendNotification({
      title: NOTIFICATION_DETAILS.ASSIGNING_RS.title,
      message: NOTIFICATION_DETAILS.ASSIGNING_RS.message,
      type: NOTIFICATION_TYPES.REQUISITION_SLIP,
      recipientUserIds: [assignedTo],
      metaData: {
        requisitionId: requisitionId,
      },
      senderId: userFromToken,
    });

    return this.fastify.log.info(`Successfully assigned Requisition`);
  }

  async notifyApproverWhoRejectedRS({ requisitionId, userFromToken }) {
    const { NOTIFICATION_TYPES } = this.constants.notification;
    const approverWhoRejectsRs =
      await this.requisitionApproverRepository.findOne({
        where: {
          requisitionId,
          status: 'rejected',
        },
        order: [['updatedAt', 'DESC']],
      });

    if (!approverWhoRejectsRs) {
      this.fastify.log.info(
        `No approver who rejected the requisition ${requisitionId}`,
      );
      return true;
    }

    this.fastify.log.info(
      `Sending notification to approver who rejects the requisition ${requisitionId}`,
    );
    await this.notificationRepository.create({
      title: 'Requisition Slip has been resubmitted and for review',
      metaData: {
        requisitionId: requisitionId,
      },
      message: `Requester has resubmitted the Requisition Slip that you have Rejected.
      Click here or access the Dashboard to proceed in reviewing the Requisition Slip.`,
      type: NOTIFICATION_TYPES.REQUISITION_SLIP,
      recipientUserIds: [approverWhoRejectsRs.approverId],
      senderId: userFromToken.id,
    });

    await this.requisitionApproverRepository.update(
      {
        requisitionId: approverWhoRejectsRs.requisitionId,
      },
      {
        status: 'pending',
      },
    );

    this.fastify.log.info(
      `Successfully sent notification to approver who rejects the requisition ${requisitionId} and reset state.`,
    );

    return true;
  }

  async saveHistory(request) {
    this.fastify.log.info(`Initiating Saving Item history`);
    const { sortBy, ...queries } = request.query;
    const { requisitionItemListSortSchema } = this.requisitionItemListEntity;

    const requisitionId = request.params.requisitionId || request.body.id;

    const {
      type,
      rsNumber,
      rsLetter,
      createdAt,
      companyId,
      projectId,
      departmentId,
    } = await this.requisitionRepository.getById(requisitionId);

    const { data } =
      await this.requisitionItemListRepository.getAllRequisitionItems({
        ...queries,
        requisitionId,
        type,
        order: requisitionItemListSortSchema.parse(sortBy),
      });

    const transaction = await this.db.sequelize.transaction();

    this.fastify.log.info(`Saving Item history`);

    try {
      const result = await Promise.all(
        data.map((item) =>
          this.historyRepository.create(
            {
              rsNumber,
              rsLetter,
              dateRequested: createdAt,
              quantityRequested: item.quantity,
              itemId: item.itemId,
              type,
              companyId: companyId ?? null,
              projectId: projectId ?? null,
              departmentId: departmentId ?? null,
              price: null,
              quantityDelivered: null,
              dateDelivered: null,
            },
            { transaction },
          ),
        ),
      );

      await transaction.commit();
    } catch (error) {
      this.fastify.log.info(`Initializing Rollback Item History`);
      await transaction.rollback();
      this.fastify.log.info(`Finished Rollback Item History`);
      throw error;
    }

    return this.fastify.log.info(`Saved Item history`);
  }

  async createTomItem(request, transaction) {
    const { requisitionId, body } = request;
    const { itemList } = body;

    this.fastify.log.info(`Creating TOM Item/s`);

    let result;
    try {
      result = await Promise.all(
        itemList.map((item) =>
          this.tomItemRepository.create(
            {
              name: item.itemName,
              quantity: item.quantity,
              unit: item.unit,
              requisitionId,
              notes: item.notes,
            },
            { transaction },
          ),
        ),
      );
      this.fastify.log.info(`Created TOM Item/s`);
    } catch (error) {
      this.fastify.log.info(`${error}`);
      throw this.clientErrors.BAD_REQUEST({
        message: 'Failed to create TOM Item/s',
      });
    }
    return result;
  }

  async closeRequisition(id) {
    const { REQUISITION_STATUS } = this.constants.requisition;
    const { CANVASS_STATUS } = this.constants.canvass;
    const { PO_STATUS } = this.constants.purchaseOrder;
    const { DELIVERY_ITEM_STATUSES } = this.constants.deliveryReceiptItem;
    const { RS_PAYMENT_REQUEST_STATUS } = this.constants.rsPaymentRequest;
    const requisition = await this.requisitionRepository.getById(id);

    if (!requisition) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition not found with ID ${id}`,
      });
    }

    //checking if requisition is on submitted state

    if (requisition.status === REQUISITION_STATUS.CLOSED) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition with ID ${id} is already closed`,
      });
    }

    if (requisition.status === REQUISITION_STATUS.DRAFT) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition with ID ${id} is a draft`,
      });
    }

    if (requisition.status === REQUISITION_STATUS.APPROVED) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition with ID ${id} is approved`,
      });
    }

    if (requisition.status === REQUISITION_STATUS.SUBMITTED) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition with ID ${id} is submitted`,
      });
    }

    // get all canvasses with open status
    // close is cancelled or approved
    const canvasses = await this.canvassRequisitionRepository.findAll({
      paginate: false,
      where: {
        [this.Sequelize.Op.and]: [
          { requisitionId: id },
          {
            status: {
              [this.db.Sequelize.Op.in]: [
                CANVASS_STATUS.DRAFT,
                CANVASS_STATUS.PARTIAL,
                CANVASS_STATUS.FOR_APPROVAL,
                CANVASS_STATUS.REJECTED,
              ],
            },
          },
        ],
      },
    });
    // get all purchase order with open status
    // no close for now
    const purchaseOrders = await this.purchaseOrderRepository.findAll({
      paginate: false,
      where: {
        [this.Sequelize.Op.and]: [
          { requisitionId: id },
          {
            status: {
              [this.db.Sequelize.Op.in]: [
                PO_STATUS.FOR_PO_REVIEW,
                PO_STATUS.FOR_PO_APPROVAL,
                PO_STATUS.FOR_APPROVAL,
                PO_STATUS.REJECT_PO,
              ],
            },
          },
        ],
      },
    });

    // get all delivery receipts with open status
    // close status FULLY_DELIVERED FULLY_DELIVERED_WITH_RETURNS CANCELLED_RETURNS
    const deliveryReceipts = await this.deliveryReceiptRepository.findAll({
      paginate: false,
      where: {
        [this.Sequelize.Op.and]: [
          { requisitionId: id },
          {
            latestDeliveryStatus: {
              [this.db.Sequelize.Op.in]: [
                DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED,
                DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED_WITH_RETURNS,
              ],
            },
          },
        ],
      },
    });
    // get all rs payment requests with open status
    // no close for now
    const rsPaymentRequests = await this.rsPaymentRequestRepository.findAll({
      paginate: false,
      where: {
        [this.Sequelize.Op.and]: [
          { requisitionId: id },
          {
            status: {
              [this.db.Sequelize.Op.in]: [
                RS_PAYMENT_REQUEST_STATUS.DRAFT,
                RS_PAYMENT_REQUEST_STATUS.REJECTED,
                RS_PAYMENT_REQUEST_STATUS.FOR_PR_APPROVAL,
                RS_PAYMENT_REQUEST_STATUS.SUBMITTED,
              ],
            },
          },
        ],
      },
    });

    if (
      !canvasses.total &&
      !purchaseOrders.total &&
      !deliveryReceipts.total &&
      !rsPaymentRequests.total
    ) {
      await this.requisitionRepository.update(
        {
          id,
        },
        { status: REQUISITION_STATUS.CLOSED },
      );

      return this.fastify.log.info(
        `Requisition with ID ${id} Closed Successfully`,
      );
    }

    return;
  }

  async getOptions() {
    const {
      REQUISITION_CATEGORIES,
      REQUISITION_REQUEST_TYPES,
      REQUISITION_CHARGE_TO,
    } = this.constants.requisition;

    const [companies, projects, departments, deliveryAddresses, unifiedDocs] =
      await Promise.all([
        this.companyRepository.getAllCompanies({
          paginate: false,
          filterBy: [],
          include: [
            {
              association: 'projects',
              as: 'projects',
              attributes: ['id', 'name', 'initial'],
            },
          ],
        }),
        this.projectRepository.getAllProjects({
          paginate: false,
          filterBy: [],
        }),
        this.departmentRepository.getAllDepartments({
          paginate: false,
          filterBy: [],
          order: [['name', 'ASC']],
        }),
        this.getDeliveryAddresses(),
      ]);

    const options = {
      categories: REQUISITION_CATEGORIES,
      requestType: REQUISITION_REQUEST_TYPES,
      companies: companies.data,
      projects: projects.data,
      departments: departments.data,
      deliverTo: deliveryAddresses.companies,
      chargeTo: REQUISITION_CHARGE_TO,
    };

    return options;
  }

  async rsApproversV2({
    category,
    projectId,
    companyId,
    departmentId,
    userFromToken,
    transaction,
    itemList,
  }) {
    try {
      this.fastify.log.info(
        `CALL_RS_APPROVERS_V2: ${category}, projectId: ${projectId}, departmentId: ${departmentId}`,
      );
      const { supervisor } = userFromToken;
      const userSupervisorId = supervisor?.id || null;

      if (category === '') {
        return [];
      }

      let response = [];
      const addOptionalApprovers =
        await this.#shouldAddOptionalApprovers(itemList);

      if (category === 'project' && projectId) {
        response.push(
          ...(await this.#getProjectApprovers({
            projectId,
            transaction,
            addOptionalApprovers,
          })),
        );
      }

      if (departmentId && ['project', 'company'].includes(category)) {
        response.push(
          ...(await this.#getProjectDeptApprovers({
            departmentId,
            transaction,
            addOptionalApprovers,
          })),
        );
      }

      if (departmentId && category === 'association') {
        response.push(
          ...(await this.#getAssociationApprovers({ companyId, transaction })),
        );
      }

      if (userSupervisorId) {
        response.push(
          ...(await this.#getUserSupervisorApprovers({
            userSupervisorId,
            transaction,
          })),
        );
      }

      this.fastify.log.info(
        `CALL_RS_APPROVERS_V2: ${JSON.stringify(response)}`,
      );

      return response;
    } catch (error) {
      this.fastify.log.error(`CALL_RS_APPROVERS_V2_ERROR: ${error.message}`);
      throw error;
    }
  }

  async isUserCreatorOrAssignee(requisitionId, userId) {
    const requisition = await this.requisitionRepository.findOne({
      where: { id: requisitionId },
      attributes: ['id', 'createdBy', 'assignedTo'],
    });

    if (!requisition) {
      return false;
    }

    return (
      requisition.createdBy === userId || requisition.assignedTo === userId
    );
  }

  async #getProjectApprovers({ projectId, transaction, addOptionalApprovers }) {
    try {
      const where = {
        projectId,
        approvalTypeCode: 'RS',
        isOptional: false,
      };

      if (addOptionalApprovers.length !== 0) {
        delete where.isOptional;
      }

      const { data, total } =
        await this.projectApprovalRepository.getAdditionalApprovers({
          where,
          projectId,
          transaction,
          optionalApproverItemIds: addOptionalApprovers,
        });

      if (
        addOptionalApprovers.length !== 0 &&
        !this.#hasAdditionalApprovers(data)
      ) {
        data.push(
          ...this.#prepareadditionalApprover({
            modelId: projectId,
            modelType: 'project',
            total,
            optionalApproverItemIds: addOptionalApprovers,
          }),
        );
      }

      if (total === 0) {
        return [];
      }

      this.fastify.log.info(
        `GET_PROJECT_APPROVERS_DATA: ${JSON.stringify(data)}`,
      );

      return data;
    } catch (error) {
      this.fastify.log.error(`GET_PROJECT_APPROVERS_ERROR: ${error.message}`);
      throw error;
    }
  }

  async #getProjectDeptApprovers({
    departmentId,
    transaction,
    addOptionalApprovers,
  }) {
    try {
      const where = {
        approvalTypeCode: 'RS',
        departmentId,
        isOptional: false,
      };

      if (addOptionalApprovers.length !== 0) {
        delete where.isOptional;
      }

      const { data, total } =
        await this.departmentApprovalRepository.getAdditionalApprovers({
          where,
          departmentId,
          transaction,
          optionalApproverItemIds: addOptionalApprovers,
        });

      if (
        addOptionalApprovers.length !== 0 &&
        !this.#hasAdditionalApprovers(data)
      ) {
        data.push(
          ...this.#prepareadditionalApprover({
            modelId: departmentId,
            modelType: 'department',
            total,
          }),
        );
      }

      if (total === 0) {
        return [];
      }

      this.fastify.log.info(
        `GET_PROJECT_DEPT_APPROVERS_DATA: ${JSON.stringify(data)}`,
      );

      return data;
    } catch (error) {
      this.fastify.log.error(
        `GET_PROJECT_DEPT_APPROVERS_ERROR: ${error.message}`,
      );
      throw error;
    }
  }

  async #getAssociationApprovers({ companyId, transaction }) {
    try {
      const { areaCode, category } = await this.companyRepository.findOne({
        attributes: ['areaCode', 'category'],
        where: {
          id: companyId,
        },
      });

      if (category !== 'association') {
        throw this.clientErrors.NOT_FOUND({
          message: `Invalid company id ${companyId} for association`,
        });
      }

      const { data, total } =
        await this.departmentAssociationApprovalRepository.findAll({
          attributes: [
            'approverId',
            'level',
            [this.Sequelize.literal("'company'"), 'modelType'],
            [this.Sequelize.literal(companyId.toString()), 'modelId'],
          ],
          where: { areaCode, approvalTypeCode: 'RS' },
          paginate: false,
          transaction,
        });

      if (total === 0) {
        return [];
      }

      this.fastify.log.info(
        `GET_ASSOCIATION_APPROVERS_DATA: ${JSON.stringify(data)}`,
      );

      return data;
    } catch (error) {
      this.fastify.log.error(
        `GET_ASSOCIATION_APPROVERS_ERROR: ${error.message}`,
      );
      throw error;
    }
  }

  async #getUserSupervisorApprovers({ userSupervisorId, transaction }) {
    try {
      const userSupervisorData = await this.userRepository.findOne({
        attributes: ['id'],
        where: {
          id: userSupervisorId,
          status: 'active',
        },
        transaction,
      });

      this.fastify.log.info(
        `GET_USER_SUPERVISOR_APPROVERS_DATA: ${JSON.stringify(userSupervisorData)}`,
      );

      if (userSupervisorData) {
        return [
          {
            modelId: userSupervisorId,
            approverId: userSupervisorId,
            level: 0,
            status: 'pending',
            modelType: 'user',
            optionalApproverItemIds: null,
          },
        ];
      }

      return [];
    } catch (error) {
      this.fastify.log.error(
        `GET_USER_SUPERVISOR_APPROVERS_ERROR: ${error.message}`,
      );
      throw error;
    }
  }

  async #shouldAddOptionalApprovers(itemList) {
    if (!itemList?.length) {
      return [];
    }

    const shouldCreate = itemList
      .filter((item) => {
        const quantity = parseInt(item.quantity, 10);
        const remainingGfq = parseFloat(item.remainingGfq);

        if (isNaN(quantity) || isNaN(remainingGfq)) {
          return false;
        }

        return (
          quantity >=
          remainingGfq *
            this.constants.requisition.GFQ_PERCENTAGE_FOR_OPTIONAL_APPROVERS
        );
      })
      .map((item) => item.id);

    this.fastify.log.info(
      `SHOULD_ADD_OPTIONAL_APPROVERS: ${JSON.stringify(shouldCreate)}`,
    );
    return shouldCreate;
  }

  async updateItemQuantityWithApprover({ requisition, itemList, transaction }) {
    try {
      const items = [];
      await Promise.all(
        itemList.map(async (item) => {
          const itemDetails = await this.requisitionItemListRepository.findOne({
            where: { requisitionId: requisition.id, id: item.id },
            attributes: [
              'id',
              'quantity',
              [this.Sequelize.literal('item.remaining_gfq'), 'remainingGfq'],
              [this.Sequelize.literal('item.id'), 'itemId'],
            ],
            include: [
              {
                model: this.db.itemModel,
                as: 'item',
                attributes: [],
              },
            ],
            transaction,
          });

          const quantityDifference = item.quantity - itemDetails.quantity;

          if (quantityDifference === 0) {
            this.fastify.log.info(`QUANTITY_IS_SAME_FOR_ITEM_SKIP: ${item.id}`);
            return;
          }

          this.fastify.log.info(
            `QUANTITY_DIFFERENCE: ${item.quantity} - ${itemDetails.quantity} = ${quantityDifference}`,
          );
          await this.requisitionItemListRepository.update(
            { id: item.id },
            { quantity: item.quantity },
            { transaction },
          );

          const remainingGfq =
            parseFloat(itemDetails.remainingGfq) - quantityDifference;
          this.fastify.log.info(
            `ITEM_UPDATE_DETAILS: ${JSON.stringify({
              id: itemDetails.itemId,
              remainingGfq,
              quantity: item.quantity,
              quantityDifference,
              currentRemainingGfq: itemDetails.remainingGfq,
            })}`,
          );

          await this.itemRepository.update(
            { id: itemDetails.itemId },
            { remainingGfq },
            { transaction },
          );

          items.push({
            id: item.id,
            quantity: quantityDifference,
            remainingGfq: itemDetails.remainingGfq,
          });
        }),
      );

      let rsApprovers = [];
      const addOptionalApprovers =
        await this.#shouldAddOptionalApprovers(items);

      if (
        addOptionalApprovers.length !== 0 &&
        requisition.category === 'project' &&
        requisition.projectId
      ) {
        rsApprovers.push(
          ...(await this.#getAdditionalProjectApprovers({
            projectId: requisition.projectId,
            transaction,
          })),
        );
      }

      if (
        addOptionalApprovers.length !== 0 &&
        requisition.departmentId &&
        ['project', 'company'].includes(requisition.category)
      ) {
        rsApprovers.push(
          ...(await this.#getAdditionalProjectDeptApprovers({
            departmentId: requisition.departmentId,
            transaction,
          })),
        );
      }

      const { data: currentApprovers } =
        await this.requisitionApproverRepository.findAll({
          where: {
            requisitionId: requisition.id,
          },
        });

      if (addOptionalApprovers.length !== 0) {
        // check if approver already exists
        rsApprovers.forEach((approver) => {
          // us the modelId, approverId and modelType to check if the approver already exists
          const existingApprover = currentApprovers.find((currentApprover) => {
            return (
              currentApprover.modelId === approver.modelId &&
              currentApprover.approverId === approver.approverId &&
              currentApprover.modelType === approver.modelType
            );
          });
          if (existingApprover) {
            // if existing approver, remove it from rsApprovers
            this.fastify.log.info(
              `EXISTING_APPROVER: ${JSON.stringify({
                modelId: existingApprover.modelId,
                approverId: existingApprover.approverId,
                modelType: existingApprover.modelType,
              })}`,
            );
            rsApprovers = rsApprovers.filter(
              (approver) => approver.modelId !== existingApprover.modelId,
            );
          }
        });

        await this.assignRSApprovers({
          rsApprovers,
          transaction,
          category: requisition.category,
          requisitionId: requisition.id,
        });
      } else {
        await this.#removeAdditionalApprovers({
          requisitionId: requisition.id,
          transaction,
          itemId: itemList.map((item) => item.id),
        });
      }
    } catch (error) {
      this.fastify.log.error(
        `UPDATE_ITEM_QUANTITY_WITH_APPROVER_ERROR: ${error.message}`,
      );
      throw error;
    }
  }

  async #getAdditionalProjectApprovers({
    projectId,
    transaction,
    optionalApproverItemIds,
  }) {
    try {
      const { data, total } =
        await this.projectApprovalRepository.getAdditionalApprovers({
          where: {
            projectId,
            approvalTypeCode: 'RS',
            isOptional: true,
          },
          projectId,
          transaction,
          optionalApproverItemIds,
        });

      if (total === 0) {
        return [];
      }

      return data;
    } catch (error) {
      this.fastify.log.error(`GET_PROJECT_APPROVERS_ERROR: ${error.message}`);
      throw error;
    }
  }

  async #getAdditionalProjectDeptApprovers({
    departmentId,
    transaction,
    optionalApproverItemIds,
  }) {
    try {
      const { data, total } =
        await this.departmentApprovalRepository.getAdditionalApprovers({
          where: {
            approvalTypeCode: 'RS',
            departmentId,
            isOptional: true,
          },
          departmentId,
          transaction,
          optionalApproverItemIds,
        });

      if (total === 0) {
        return [];
      }

      this.fastify.log.info(
        `GET_PROJECT_DEPT_APPROVERS_DATA: ${JSON.stringify(data)}`,
      );

      return data;
    } catch (error) {
      this.fastify.log.error(
        `GET_PROJECT_DEPT_APPROVERS_ERROR: ${error.message}`,
      );
      throw error;
    }
  }

  async #removeAdditionalApprovers({ transaction, requisitionId, itemIds }) {
    try {
      this.fastify.log.info(
        `REMOVE_ADDITIONAL_APPROVERS_FOR_RS_ID: ${requisitionId}`,
      );

      await this.requisitionApproverRepository.destroy(
        {
          isOptionalApprover: true,
          requisitionId,
          status: 'pending',
          optionalApproverItemIds: {
            [this.Sequelize.Op.contains]: itemIds,
          },
        },
        { transaction },
      );

      return true;
    } catch (error) {
      this.fastify.log.error(
        `REMOVE_ADDITIONAL_APPROVERS_ERROR: ${error.message}`,
      );
      throw error;
    }
  }

  parseChargeToId({ chargeTo, companyId, projectId }) {
    let chargeToId = null;
    if (chargeTo === 'company' || chargeTo === 'association') {
      chargeToId = companyId;
    }

    if (chargeTo === 'project') {
      chargeToId = projectId;
    }

    return chargeToId;
  }

  async #requisitionItemListDeleteItems({
    requisitionId,
    requisitionItemList,
    itemList,
    transaction,
  }) {
    try {
      if (requisitionItemList.length <= itemList.length) {
        this.fastify.log.info(`NOTHING_TO_DELETE: requisitionItemList.length: 
          ${requisitionItemList.length} <= itemList.length: ${itemList.length}`);
        return true;
      }

      const itemsToDelete = requisitionItemList.filter(
        (requisitionItem) =>
          !itemList.some((item) => requisitionItem.itemId === item.id),
      );

      await Promise.all(
        itemsToDelete.map(async (item) => {
          await this.requisitionItemListRepository.destroy(
            { id: item.id },
            { transaction },
          );

          const { remainingGfq } = await this.itemRepository.findOne({
            where: { id: item.itemId },
            attributes: ['remainingGfq'],
            transaction,
          });

          await this.itemRepository.update(
            { id: item.itemId },
            {
              remainingGfq:
                parseFloat(remainingGfq) + parseFloat(item.quantity),
            },
            { transaction },
          );

          await this.#removeAdditionalApprovers({
            requisitionId,
            transaction,
            itemIds: [item.item.id],
          });
        }),
      );

      this.fastify.log.info(
        `REQUISITION_ITEM_LIST_DELETED_COUNT: ${itemsToDelete.length}`,
      );
      return true;
    } catch (error) {
      this.fastify.log.error(
        `REMOVE_ADDITIONAL_APPROVERS_ERROR: ${error.message}`,
      );
      throw error;
    }
  }

  #prepareadditionalApprover({
    modelId,
    modelType,
    total,
    optionalApproverItemIds,
  }) {
    return [
      {
        modelId,
        modelType,
        approverId: null,
        level: parseInt(total, 10) + 1,
        status: 'pending',
        isOptionalApprover: true,
        optionalApproverItemIds: optionalApproverItemIds,
      },
    ];
  }

  #hasAdditionalApprovers(data) {
    return data.some((item) => item.isOptionalApprover);
  }

  async #addOptionalApprovers({ transaction, requisition, itemIds }) {
    try {
      let modelType = [];

      if (requisition.category === 'company') {
        modelType = ['department'];
      }

      if (requisition.category === 'project') {
        modelType = ['project', 'department'];
      }

      const { data: currentApprovers } =
        await this.requisitionApproverRepository.findAll({
          where: {
            requisitionId: requisition.id,
            isOptionalApprover: true,
            modelType: {
              [this.Sequelize.Op.in]: modelType,
            },
          },
        });

      const missingModelType = modelType.filter(
        (model) =>
          !currentApprovers.some((approver) => approver.modelType === model),
      );
      if (missingModelType.length === 0) {
        this.fastify.log.info(
          `ALREADY_HAS_OPTIONAL_APPROVERS: ${JSON.stringify(currentApprovers)}`,
        );
        return true;
      }

      let rsApprovers = [];

      if (
        requisition.category === 'project' &&
        requisition.projectId &&
        missingModelType.includes('project')
      ) {
        rsApprovers.push(
          ...(await this.#getAdditionalProjectApprovers({
            projectId: requisition.projectId,
            transaction,
            optionalApproverItemIds: itemIds,
          })),
        );
      }

      if (
        requisition.departmentId &&
        ['project', 'company'].includes(requisition.category) &&
        missingModelType.includes('department')
      ) {
        rsApprovers.push(
          ...(await this.#getAdditionalProjectDeptApprovers({
            departmentId: requisition.departmentId,
            transaction,
            optionalApproverItemIds: itemIds,
          })),
        );
      }

      await this.assignRSApprovers({
        rsApprovers,
        transaction,
        category: requisition.category,
        requisitionId: requisition.id,
      });
    } catch (error) {
      this.fastify.log.error(
        `ADD_ADDITIONAL_APPROVERS_ERROR: ${error.message}`,
      );
      throw error;
    }
  }

  async upsertRequisitionNote(payload) {
    const { modelId, model } = payload;

    const existingNote = await this.noteRepository.findOne({
      where: {
        model,
        modelId,
      },
      order: [['updated_at', 'DESC']],
    });

    if (!existingNote) {
      return await this.noteService.createNote({ ...payload });
    }

    return await this.noteRepository.update(
      {
        model,
        modelId,
      },
      {
        note: payload.note,
      },
    );
  }
}

module.exports = RequisitionService;
