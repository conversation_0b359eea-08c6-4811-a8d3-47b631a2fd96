class RoleService {
  constructor(container) {
    const { constants, roleRepository, db, userRepository, clientErrors } =
      container;

    this.db = db;
    this.userConstants = constants.user;
    this.roleRepository = roleRepository;
    this.userRepository = userRepository;
    this.clientErrors = clientErrors;
  }

  async getRolesByUserType(requesterRole) {
    let whereClause = {};
    const { USER_TYPES } = this.userConstants;

    if (requesterRole === USER_TYPES.ROOT_USER) {
      whereClause = {
        name: USER_TYPES.ADMIN,
      };
    } else {
      whereClause = {
        name: {
          [this.db.Sequelize.Op.notIn]: [
            USER_TYPES.ROOT_USER,
            USER_TYPES.ADMIN,
          ],
        },
      };
    }

    return await this.roleRepository.findAll({
      paginate: false,
      where: whereClause,
    });
  }

  async getPurchasingHeadUserId() {
    const { id } = await this.roleRepository.findOne({
      where: { name: 'Purchasing Head' },
    });

    return await this.userRepository.findOne({
      where: { roleId: id },
    });
  }
}

module.exports = RoleService;
