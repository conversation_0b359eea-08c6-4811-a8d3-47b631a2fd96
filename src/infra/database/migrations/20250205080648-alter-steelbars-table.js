'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeColumn('steelbars', 'price_per_kg');
    
    await queryInterface.changeColumn('steelbars', 'diameter', {
      type: Sequelize.DECIMAL,
      allowNull: false,
    });
    
    await queryInterface.changeColumn('steelbars', 'length', {
      type: Sequelize.DECIMAL,
      allowNull: false,
    });
    
    await queryInterface.changeColumn('steelbars', 'weight', {
      type: Sequelize.DECIMAL,
      allowNull: false,
    });
    
    await queryInterface.changeColumn('steelbars', 'kg_per_meter', {
      type: Sequelize.DECIMAL,
      allowNull: false,
    });

    await queryInterface.sequelize.query(
      'ALTER TABLE steelbars DROP CONSTRAINT IF EXISTS unique_grade_diameter_length;'
    );
    
    await queryInterface.addConstraint('steelbars', {
      fields: ['grade', 'diameter', 'length', 'ofm_acctcd'],
      type: 'unique',
      name: 'unique_grade_diameter_length_ofm_acctcd'
    });
  },

  async down(queryInterface, Sequelize) {n
    await queryInterface.changeColumn('steelbars', 'diameter', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
    });
    
    await queryInterface.changeColumn('steelbars', 'length', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
    });
    
    await queryInterface.changeColumn('steelbars', 'weight', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
    });
    
    await queryInterface.changeColumn('steelbars', 'kg_per_meter', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
    });
    
    await queryInterface.addColumn('steelbars', 'price_per_kg', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
    });

    await queryInterface.sequelize.query(
      'ALTER TABLE steelbars DROP CONSTRAINT IF EXISTS unique_grade_diameter_length_ofm_acctcd;'
    );
    
    await queryInterface.addConstraint('steelbars', {
      fields: ['grade', 'diameter', 'length'],
      type: 'unique',
      name: 'unique_grade_diameter_length'
    });
  }
};