'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('approvers', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      model: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        field: 'user_id',
      },
      altApproverId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        field: 'alt_approver',
      },
      approvalOrder: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'approval_order',
        comment: 'The sequence/order in which approval should occur'
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now')
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now')
      }
    });

    // Indexes
    await queryInterface.addIndex('approvers', ['user_id']);
    await queryInterface.addIndex('approvers', ['model']);
    await queryInterface.addIndex('approvers', ['approval_order']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('approvers');
  },
};
