'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    const { QueryTypes } = require('sequelize');

    const constraintExists = await queryInterface.sequelize.query(
      `SELECT 1 
      FROM information_schema.table_constraints 
      WHERE constraint_name = 'requisition_canvass_histories_supplier_id_fkey' 
      AND table_name = 'requisition_canvass_histories';`,
      { type: QueryTypes.SELECT }
    );

    if (constraintExists.length > 0) {
      await queryInterface.removeConstraint('requisition_canvass_histories', 'requisition_canvass_histories_supplier_id_fkey');
    }
  },

  async down(queryInterface) {
    const constraintExists = await queryInterface.sequelize.query(
      `SELECT 1 
      FROM information_schema.table_constraints 
      WHERE constraint_name = 'requisition_canvass_histories_supplier_id_fkey' 
      AND table_name = 'requisition_canvass_histories';`,
      { type: QueryTypes.SELECT }
    );

    if (constraintExists.length > 0) {
      await queryInterface.addConstraint('requisition_canvass_histories', {
        fields: ['supplier_id'],
        type: 'foreign key',
        name: 'requisition_canvass_histories_supplier_id_fkey',
        references: {
          table: 'suppliers',
          field: 'id'
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE'
      });
    }
  }
};

