'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('delivery_receipt_items', 'qty_returned', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });
    await queryInterface.addColumn('delivery_receipt_items', 'has_returns', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
    });
    await queryInterface.addColumn('delivery_receipt_items_history', 'qty_returned', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('delivery_receipt_items', 'qty_returned');
    await queryInterface.removeColumn('delivery_receipt_items', 'has_returns');
    await queryInterface.removeColumn('delivery_receipt_items_history', 'qty_returned');
  }
};