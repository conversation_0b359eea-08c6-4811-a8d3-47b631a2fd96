'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('requisition_item_lists', 'item_type', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
    await queryInterface.renameColumn('requisition_item_lists', 'ofm_item_id', 'item_id');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('requisition_item_lists', 'item_type');
    await queryInterface.renameColumn('requisition_item_lists', 'item_id', 'ofm_item_id');
  }
};
