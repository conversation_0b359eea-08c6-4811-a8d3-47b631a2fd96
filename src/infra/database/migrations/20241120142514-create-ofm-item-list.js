'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ofm_item_lists', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      listName: {
        type: Sequelize.STRING(100),
        unique: true,
        field: 'list_name',
      },
      companyCode: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'company_code',
      },
      departmentCode: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'department_code',
      },
      projectCode: {
        type: Sequelize.STRING(20),
        allowNull: true,
        field: 'project_code',
      },
      tradeCode: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'trade_code',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'updated_at',
        onUpdate: Sequelize.NOW,
      },
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ofm_item_lists');
  },
};