'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('purchase_order_approvers', 'added_by', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      field: 'added_by',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('purchase_order_approvers', 'added_by');
  },
};