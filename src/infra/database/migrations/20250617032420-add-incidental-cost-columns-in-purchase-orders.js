'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('purchase_orders', 'withholding_tax_deduction', {
      type: Sequelize.DECIMAL,
      allowNull: true,
      field: 'withholding_tax_deduction',
    });

    await queryInterface.addColumn('purchase_orders', 'delivery_fee', {
      type: Sequelize.DECIMAL,
      allowNull: true,
      field: 'delivery_fee',
    });

    await queryInterface.addColumn('purchase_orders', 'tip', {
      type: Sequelize.DECIMAL,
      allowNull: true,
      field: 'tip',
    });

    await queryInterface.addColumn('purchase_orders', 'extra_charges', {
      type: Sequelize.DECIMAL,
      allowNull: true,
      field: 'extra_charges',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('purchase_orders', 'withholding_tax_deduction');
    await queryInterface.removeColumn('purchase_orders', 'delivery_fee');
    await queryInterface.removeColumn('purchase_orders', 'tip');
    await queryInterface.removeColumn('purchase_orders', 'extra_charges');
  },
};
