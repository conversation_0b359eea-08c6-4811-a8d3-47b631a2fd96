'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('requisitions', 'draft_rs_number', {
      type: Sequelize.STRING,
      allowNull: true,
    });
    await queryInterface.renameColumn('requisitions', 'rs_letters', 'rs_letter');
    await queryInterface.changeColumn('requisitions', 'rs_number', {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('requisitions', 'draft_rs_number');
    await queryInterface.renameColumn('requisitions', 'rs_letter', 'rs_letters');
    await queryInterface.changeColumn('requisitions', 'rs_number', {
      type: Sequelize.STRING,
      allowNull: false,
    });
  }
};
