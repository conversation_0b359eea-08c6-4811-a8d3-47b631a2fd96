'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('steelbars', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      grade: {
        type: Sequelize.STRING(20),
        allowNull: false,
      },
      diameter: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
      },
      length: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
      },
      weight: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
      },
      pricePerKg: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        field: 'price_per_kg',
      },
      kgPerMeter: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        field: 'kg_per_meter',
      },
      ofmAcctcd: {
        type: Sequelize.STRING(50),
        allowNull: true,
        field: 'ofm_acctcd',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'updated_at',
        onUpdate: Sequelize.NOW,
      },
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('steelbars');
  },
};