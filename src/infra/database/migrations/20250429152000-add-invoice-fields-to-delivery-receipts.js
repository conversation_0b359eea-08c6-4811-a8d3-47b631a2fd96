'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('delivery_receipts', 'invoice_number', {
      type: Sequelize.STRING,
      allowNull: true,
    });
    
    await queryInterface.addColumn('delivery_receipts', 'supplier_delivery_issued_date', {
      type: Sequelize.DATE,
      allowNull: true,
    });
    
    await queryInterface.addColumn('delivery_receipts', 'issued_date', {
      type: Sequelize.DATE,
      allowNull: true,
    });
  },

  async down(queryInterface) {
    await queryInterface.removeColumn('delivery_receipts', 'invoice_number');
    await queryInterface.removeColumn('delivery_receipts', 'supplier_delivery_issued_date');
    await queryInterface.removeColumn('delivery_receipts', 'issued_date');
  }
};