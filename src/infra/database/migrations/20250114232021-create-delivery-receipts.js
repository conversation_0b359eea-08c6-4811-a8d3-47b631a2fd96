'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('delivery_receipts', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      requisition_id: {
        type: Sequelize.INTEGER,
        references: {
          model: 'requisitions',
          key: 'id'
        }
      },
      company_code: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'company_code'
      },
      dr_number: {
        type: Sequelize.STRING,
        unique: true,
        field: 'dr_number'
      },
      po_number: {
        type: Sequelize.STRING(8),
        field: 'po_number'
      },
      supplier: {
        type: Sequelize.STRING,
        field: 'supplier'
      },
      is_draft: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      draft_dr_number: {
        type: Sequelize.STRING,
        unique: true,
        field: 'draft_dr_number'
      },
      status: {
        type: Sequelize.STRING(20),
        allowNull: false,
        field: 'status',
      },
      note: {
        type: Sequelize.STRING(60),
        allowNull: true,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        onUpdate: Sequelize.fn('now'),
      }
    });
    await queryInterface.addConstraint('delivery_receipts', {
      fields: ['is_draft', 'draft_dr_number'],
      type: 'unique',
      name: 'unique_draft_dr_number'
    });
    await queryInterface.addConstraint('delivery_receipts', {
      fields: ['is_draft', 'dr_number'],
      type: 'unique',
      name: 'unique_dr_number'
    });
  },
  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('delivery_receipts');
  }
};