'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('requisition_canvass_histories', 'requisition_item_list_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'requisition_item_lists',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });

    await queryInterface.addColumn('requisition_canvass_histories', 'supplier_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'suppliers',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });

    await queryInterface.addColumn('requisition_canvass_histories', 'canvass_requisition_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });

    await queryInterface.addIndex('requisition_canvass_histories', ['requisition_item_list_id', 'supplier_id', 'canvass_requisition_id'], {
      name: 'requisition_canvass_histories_requisition_item_list_id_supplier_id_canvass_requisition_id_idx',
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('requisition_canvass_histories', 'requisition_item_list_id');
    await queryInterface.removeColumn('requisition_canvass_histories', 'supplier_id');
    await queryInterface.removeColumn('requisition_canvass_histories', 'canvass_requisition_id');
  }
};
