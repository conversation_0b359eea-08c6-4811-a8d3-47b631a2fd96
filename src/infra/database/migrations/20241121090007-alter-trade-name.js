'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('trades', 'trade_name', {
      type: Sequelize.STRING(100),
      allowNull: false,
      unique: true
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      UPDATE trades
      SET trade_name = SUBSTRING(trade_name, 1, 20);
    `);
    
    await queryInterface.changeColumn('trades', 'trade_name', {
      type: Sequelize.STRING(20),
      allowNull: false,
      unique: true
    });
  },
};