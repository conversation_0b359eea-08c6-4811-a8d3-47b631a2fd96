'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('delivery_receipt_items', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      dr_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'delivery_receipts',
          key: 'id'
        }
      },
      po_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      item_id: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      item_des: {
        type: Sequelize.STRING,
        allowNull: false
      },
      qty_ordered: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      qty_delivered: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      unit: {
        type: Sequelize.STRING(5),
        allowNull: false
      },
      date_delivered: {
        type: Sequelize.DATE,
        allowNull: true
      },
      delivery_status: {
        type: Sequelize.STRING(50),
        allowNull: true
      },
      notes: {
        type: Sequelize.STRING(60),
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        onUpdate: Sequelize.fn('now'),
      }
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('delivery_receipt_items');
  }
};

