'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('rs_payment_requests', 'created_at', {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.fn('now'),
    });

    await queryInterface.addColumn('rs_payment_requests', 'updated_at', {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.fn('now'),
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('rs_payment_requests', 'created_at');
    await queryInterface.removeColumn('rs_payment_requests', 'updated_at');
  }
};

