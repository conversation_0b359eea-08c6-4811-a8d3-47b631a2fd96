'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // This migration ensures that the new cancelled status values are available
    // for force close operations. The status constants are already defined in:
    // - deliveryReceiptConstants.js: RR_CANCELLED = 'rr_cancelled'
    // - invoiceReportConstants.js: IR_CANCELLED = 'ir_cancelled'
    
    // Note: Since the application uses string-based status values rather than
    // enum constraints in the database, no schema changes are needed.
    // The new status values will be used by the force close functionality
    // when updating delivery receipts and invoice reports.

    // Add any necessary data updates here if needed
    // For example, if there are existing records that need status updates
    
    console.log('Force close cancelled status values are now available:');
    console.log('- Delivery Receipt: RR_CANCELLED = "rr_cancelled"');
    console.log('- Invoice Report: IR_CANCELLED = "ir_cancelled"');
  },

  async down(queryInterface, Sequelize) {
    // Rollback any data changes if necessary
    // Note: This migration primarily documents the availability of new status values
    // rather than making schema changes, so rollback is minimal
    
    console.log('Force close cancelled status values rollback completed');
  },
};
