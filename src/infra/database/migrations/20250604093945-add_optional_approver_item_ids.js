'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('requisition_approvers', 'optional_approver_item_ids', {
      type: Sequelize.ARRAY(Sequelize.INTEGER),
      allowNull: true,
      field: 'optional_approver_item_ids',
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('requisition_approvers', 'optional_approver_item_ids');
  },
};
