require('dotenv').config();
const { requestContext } = require('@fastify/request-context');

const customLogger = (msg, queryOptions) => {
  const logger = requestContext.get('log');
  const { bind } = queryOptions;
  if (logger) {
    logger.info(`${msg}`);
    if (bind) {
      const bindValueLog = bind.reduce((acc, b, index) => {
        acc[`$${index + 1}`] = b;
        return acc;
      }, {});
      logger.info(JSON.stringify(bindValueLog));
    }
  } else {
    console.log(`${msg}`);
  }
};

module.exports = {
  development: {
    host: process.env.POSTGRES_HOST,
    dialect: process.env.DIALECT,
    port: process.env.POSTGRES_PORT,
    username: process.env.POSTGRES_USER,
    password: process.env.POSTGRES_PASSWORD,
    database: process.env.POSTGRES_DB,
    dialectOptions: {
      useUTC: true,
    },
    define: {
      timestamps: false,
    },
    pool: {
      max: Number(process.env.POOL_MAX),
      min: Number(process.env.POOL_MIN),
      acquire: Number(process.env.POOL_ACQUIRE),
      idle: Number(process.env.POOL_IDLE),
      evict: Number(process.env.POOL_EVICTION),
    },
    // logging: customLogger,
  },
  production: {
    host: process.env.POSTGRES_HOST,
    dialect: process.env.DIALECT,
    port: process.env.POSTGRES_PORT,
    username: process.env.POSTGRES_USER,
    password: process.env.POSTGRES_PASSWORD,
    database: process.env.POSTGRES_DB,
    dialectOptions: {
      useUTC: true,
      ssl: {
        require: false,
        rejectUnauthorized: false,
      },
    },
    define: {
      timestamps: false,
    },
    pool: {
      max: Number(process.env.POOL_MAX),
      min: Number(process.env.POOL_MIN),
      acquire: Number(process.env.POOL_ACQUIRE),
      idle: Number(process.env.POOL_IDLE),
    },
    logging: console.log,
  },
};
