const {
  PR_APPROVER_STATUS,
} = require('../../../domain/constants/rsPaymentRequestConstants');

module.exports = (sequelize, Sequelize) => {
  const RSPaymentRequestApproverModel = sequelize.define(
    'rs_payment_request_approvers',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      paymentRequestId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'rs_payment_requests',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'payment_request_id',
      },
      level: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'user_id',
      },
      altApproverId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'alt_approver_id',
      },
      status: {
        allowNull: false,
        type: Sequelize.STRING(255),
        defaultValue: PR_APPROVER_STATUS.PENDING,
      },
      roleId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'roles',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        field: 'role_id',
      },
      isAdhoc: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'is_adhoc',
      },
      overrideBy: {
        type: Sequelize.JSONB,
        field: 'override_by',
        defaultValue: null,
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  RSPaymentRequestApproverModel.associate = (models) => {
    RSPaymentRequestApproverModel.belongsTo(models.userModel, {
      foreignKey: 'userId',
      as: 'approver',
    });

    RSPaymentRequestApproverModel.belongsTo(models.userModel, {
      foreignKey: 'altApproverId',
      as: 'altApprover',
    });

    RSPaymentRequestApproverModel.belongsTo(models.rsPaymentRequestModel, {
      foreignKey: 'paymentRequestId',
      as: 'paymentRequest',
    });

    RSPaymentRequestApproverModel.belongsTo(models.roleModel, {
      foreignKey: 'roleId',
      as: 'role',
    });
  };

  RSPaymentRequestApproverModel.afterUpdate(
    async (instance, { transaction }) => {
      try {
        const paymentRequest = await sequelize
          .model('rs_payment_requests')
          .findOne({
            where: {
              id: instance.paymentRequestId,
            },
          });

        const lastApproverId =
          instance.isAdhoc && instance.altApproverId
            ? instance.altApproverId
            : instance.userId;
        await paymentRequest.update(
          {
            lastApproverId,
          },
          {
            transaction,
          },
        );
      } catch (error) {
        console.error(
          'HOOK_ERROR - rsPaymentRequestApproverModel - afterUpdate: ',
          error.stack,
        );
      }
    },
  );

  return RSPaymentRequestApproverModel;
};
