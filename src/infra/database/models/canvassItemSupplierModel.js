const {
  DISCOUNT_TYPE,
  SUPPLIER_TYPE,
  CANVASS_STATUS,
  CANVASS_ITEM_STATUS,
} = require('../../../domain/constants/canvassConstants');

module.exports = (sequelize, Sequelize) => {
  const CanvassItemSupplierModel = sequelize.define(
    'canvass_item_suppliers',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      canvassItemId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'canvass_items',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'canvass_item_id',
      },
      supplierId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        validate: {
          isInt: true,
        },
        field: 'supplier_id',
      },
      supplierType: {
        allowNull: false,
        defaultValue: SUPPLIER_TYPE.SUPPLIER,
        type: Sequelize.ENUM(Object.values(SUPPLIER_TYPE)),
        field: 'supplier_type',
      },
      term: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      quantity: {
        type: Sequelize.DECIMAL(13, 3),
        allowNull: false,
        set(value) {
          const fixedValue = parseFloat(value || 0).toFixed(3);
          this.setDataValue('quantity', fixedValue);
        },
        get() {
          const rawValue = this.getDataValue('quantity');
          return parseFloat(rawValue || 0);
        },
      },
      order: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      unitPrice: {
        type: Sequelize.DOUBLE,
        allowNull: false,
        field: 'unit_price',
      },
      discountValue: {
        type: Sequelize.DOUBLE,
        allowNull: false,
        field: 'discount_value',
      },
      discountType: {
        type: Sequelize.ENUM,
        allowNull: false,
        defaultValue: DISCOUNT_TYPE.FIXED,
        values: Object.values(DISCOUNT_TYPE),
        field: 'discount_type',
      },
      isSelected: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'is_selected',
      },
      supplierName: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      supplierNameLocked: {
        type: Sequelize.BOOLEAN,
        default: false,
      },
      supplierDetails: {
        type: Sequelize.VIRTUAL,
        get() {
          switch (this.supplierType) {
            case 'supplier':
              return this.supplier;
            case 'project':
              return this.project;
            case 'company':
              return this.company;
            default:
              return null;
          }
        },
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  CanvassItemSupplierModel.associate = (models) => {
    CanvassItemSupplierModel.belongsTo(models.canvassItemModel, {
      foreignKey: 'canvassItemId',
      as: 'canvassItem',
    });

    CanvassItemSupplierModel.belongsTo(models.supplierModel, {
      foreignKey: 'supplierId',
      as: 'supplier',
    });

    CanvassItemSupplierModel.belongsTo(models.projectModel, {
      foreignKey: 'supplierId',
      as: 'project',
    });

    CanvassItemSupplierModel.belongsTo(models.companyModel, {
      foreignKey: 'supplierId',
      as: 'company',
    });
  };

  CanvassItemSupplierModel.beforeCreate(async (item, { transaction }) => {
    try {
      let supplier;
      if (item.supplierType === 'supplier') {
        supplier = await sequelize
          .model('suppliers')
          .findOne({ where: { id: item.supplierId } });
      } else if (item.supplierType === 'project') {
        supplier = await sequelize
          .model('projects')
          .findOne({ where: { id: item.supplierId } });
      } else {
        supplier = await sequelize
          .model('companies')
          .findOne({ where: { id: item.supplierId } });
      }

      item.supplierName = supplier.name;

      const canvassItem = await item.getCanvassItem({
        attributes: [
          'canvassRequisitionId',
          'requisitionItemListId',
          'status',
          'requisitionId',
        ],
        include: [
          {
            model: sequelize.model('requisitions'),
            as: 'requisition',
            attributes: ['id', 'companyCode'],
          },
          {
            model: sequelize.model('canvass_requisitions'),
            as: 'canvass',
            attributes: [
              'id',
              'status',
              'csLetter',
              'csNumber',
              'draftCsNumber',
            ],
          },
          {
            model: sequelize.model('requisition_item_lists'),
            as: 'requisitionItem',
            attributes: ['id', 'itemId', 'itemType'],
          },
        ],
        transaction,
      });

      const isOfm =
        canvassItem.requisitionItem.itemType === 'ofm' ||
        canvassItem.requisitionItem.itemType === 'ofm-tom';

      const itemDetails = await sequelize
        .model(isOfm ? 'items' : 'non_ofm_items')
        .findOne({
          attributes: [isOfm ? 'itmDes' : 'itemName'],
          where: { id: canvassItem.requisitionItem.itemId },
        });

      const companyCode = canvassItem.requisition.companyCode;
      const csLetter = canvassItem.canvass.csLetter;
      const csNumber = canvassItem.canvass.csNumber;
      const draftCsNumber = canvassItem.canvass.draftCsNumber;

      const canvassNumber =
        canvassItem.canvass.status === CANVASS_STATUS.DRAFT
          ? `CS-TMP-${companyCode}${csLetter}${draftCsNumber}`
          : `CS-${companyCode}${csLetter}${csNumber}`;

      const itemName = itemDetails?.itmDes || itemDetails?.itemName || '';

      await sequelize.model('requisition_canvass_histories').create(
        {
          canvassRequisitionId: canvassItem.canvassRequisitionId,
          requisitionItemListId: canvassItem.requisitionItemListId,
          canvassNumber,
          requisitionId: canvassItem.requisitionId,
          status: canvassItem.canvass.status,
          supplierId: item.supplierId,
          supplier: item.supplierName,
          price: item.unitPrice,
          discount: item.discountValue,
          item: itemName,
        },
        { transaction },
      );
    } catch (error) {
      await transaction.rollback();
      console.log(
        'HOOK_ERROR - canvassItemSupplierModel.beforeCreate: ',
        error.stack,
      );
    }
  });

  CanvassItemSupplierModel.beforeUpdate(async (item, { transaction }) => {
    try {
      let supplier;
      if (item.supplierType === 'supplier') {
        supplier = await sequelize
          .model('suppliers')
          .findOne({ where: { id: item.supplierId } });
      } else if (item.supplierType === 'project') {
        supplier = await sequelize
          .model('projects')
          .findOne({ where: { id: item.supplierId } });
      } else {
        supplier = await sequelize
          .model('companies')
          .findOne({ where: { id: item.supplierId } });
      }

      if (item.changed('supplierId')) {
        if (supplier) {
          item.setDataValue('supplierName', supplier.name);
        }
      }

      const canvassItem = await item.getCanvassItem({
        attributes: [
          'canvassRequisitionId',
          'requisitionItemListId',
          'status',
          'requisitionId',
        ],
        include: [
          {
            model: sequelize.model('requisitions'),
            as: 'requisition',
            attributes: ['id', 'companyCode'],
          },
          {
            model: sequelize.model('canvass_requisitions'),
            as: 'canvass',
            attributes: [
              'id',
              'status',
              'csLetter',
              'csNumber',
              'draftCsNumber',
            ],
          },
          {
            model: sequelize.model('requisition_item_lists'),
            as: 'requisitionItem',
            attributes: ['id', 'itemId', 'itemType'],
          },
        ],
        transaction,
      });

      const isOfm =
        canvassItem.requisitionItem.itemType === 'ofm' ||
        canvassItem.requisitionItem.itemType === 'ofm-tom';

      const itemDetails = await sequelize
        .model(isOfm ? 'items' : 'non_ofm_items')
        .findOne({
          attributes: [isOfm ? 'itmDes' : 'itemName'],
          where: { id: canvassItem.requisitionItem.itemId },
        });

      if (
        canvassItem.changed('status') ||
        item.changed('supplierId') ||
        item.changed('supplierName') ||
        item.changed('unitPrice') ||
        item.changed('discountValue') ||
        canvassItem.canvass?.changed('status') ||
        canvassItem.requisitionItem?.changed('itemId')
      ) {
        const companyCode = canvassItem.requisition.companyCode;
        const csLetter = canvassItem.canvass.csLetter;
        const csNumber = canvassItem.canvass.csNumber;
        const draftCsNumber = canvassItem.canvass.draftCsNumber;

        const canvassNumber =
          canvassItem.canvass.status === CANVASS_STATUS.DRAFT
            ? `CS-TMP-${companyCode}${csLetter}${draftCsNumber}`
            : `CS-${companyCode}${csLetter}${csNumber}`;

        const itemName = itemDetails?.itmDes || itemDetails?.itemName || '';

        await sequelize.model('requisition_canvass_histories').create(
          {
            canvassRequisitionId: canvassItem.canvassRequisitionId,
            requisitionItemListId: canvassItem.requisitionItemListId,
            canvassNumber,
            requisitionId: canvassItem.requisitionId,
            status: canvassItem.canvass.status,
            supplierId: item.supplierId,
            supplier: item.supplierName,
            price: item.unitPrice,
            discount: item.discountValue,
            item: itemName,
          },
          { transaction },
        );
      }
    } catch (error) {
      await transaction.rollback();
      console.error(
        'HOOK_ERROR - canvassItemSupplierModel - beforeUpdate: ',
        error.stack,
      );
    }
  });

  return CanvassItemSupplierModel;
};
