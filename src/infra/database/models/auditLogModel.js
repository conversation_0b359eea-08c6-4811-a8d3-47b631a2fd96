module.exports = (sequelize, Sequelize) => {
  const AuditLogModel = sequelize.define(
    'audit_logs',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      actionType: {
        type: Sequelize.STRING(50),
        allowNull: false,
        field: 'action_type'
      },
      module: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'created_at'
      }
    },
    {
      timestamps: true,
      updatedAt: false,
      underscored: true,
      tableName: 'audit_logs'
    }
  );

  return AuditLogModel;
};