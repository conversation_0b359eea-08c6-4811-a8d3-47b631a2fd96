module.exports = (sequelize, Sequelize) => {
  const ProjectCompanyModel = sequelize.define(
    'project_companies',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      projectId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'projects',
          key: 'id',
        },
        field: 'project_id',
      },
      companyId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'companies',
          key: 'id',
        },
        field: 'company_id',
      },
    },
    {
      timestamps: true,
      underscored: true,
      indexes: [
        {
          unique: true,
          fields: ['project_id', 'company_id'],
          name: 'project_companies_unique',
        },
      ],
    },
  );

  return ProjectCompanyModel;
};