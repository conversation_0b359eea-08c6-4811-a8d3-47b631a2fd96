module.exports = (sequelize, DataTypes) => {
  const DeliveryReceiptItemHistoryModel = sequelize.define(
    'delivery_receipt_items_history',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      deliveryReceiptItemId: {
        type: DataTypes.INTEGER,
        references: {
          model: 'deliveryReceiptItem',
          key: 'id',
        },
        allowNull: false,
        onDelete: 'CASCADE',
      },
      qtyOrdered: {
        type: DataTypes.DECIMAL(10, 3),
        allowNull: false,
        set(value) {
          const fixedValue = parseFloat(value || 0).toFixed(3);
          this.setDataValue('qtyOrdered', fixedValue);
        },
        get() {
          const rawValue = this.getDataValue('qtyOrdered');
          return parseFloat(rawValue || 0);
        },
      },
      qtyDelivered: {
        type: DataTypes.DECIMAL(10, 3),
        allowNull: true,
        set(value) {
          const fixedValue = parseFloat(value || 0).toFixed(3);
          this.setDataValue('qtyDelivered', fixedValue);
        },
        get() {
          const rawValue = this.getDataValue('qtyDelivered');
          return parseFloat(rawValue || 0);
        },
      },
      qtyReturned: {
        type: DataTypes.DECIMAL(10, 3),
        allowNull: true,
        set(value) {
          const fixedValue = parseFloat(value || 0).toFixed(3);
          this.setDataValue('qtyReturned', fixedValue);
        },
        get() {
          const rawValue = this.getDataValue('qtyReturned');
          return parseFloat(rawValue || 0);
        },
      },
      dateDelivered: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      status: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.fn('now'),
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.fn('now'),
        onUpdate: DataTypes.fn('now'),
      },
    },
    {
      underscored: true,
      tableName: 'delivery_receipt_items_history',
    }
  );

  DeliveryReceiptItemHistoryModel.associate = (models) => {
    DeliveryReceiptItemHistoryModel.belongsTo(models.deliveryReceiptItemModel, {
      foreignKey: 'deliveryReceiptItemId',
      onDelete: 'CASCADE',
    });
  };

  return DeliveryReceiptItemHistoryModel;
};