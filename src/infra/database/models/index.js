require('dotenv').config();

const fs = require('fs');
const path = require('path');
const Sequelize = require('sequelize');
const dbConfig = require('../config/dbConfig');

const sequelize = new Sequelize(
  dbConfig[
  process.env.NODE_ENV === 'production' ? 'production' : 'development'
  ],
);

const db = {
  Sequelize,
  sequelize,
};

/* -------------------------------------------------------------------------- */
/*                          Model Creation Guidelines                         */
/* -------------------------------------------------------------------------- */

/**
 * How to create a new model:
 * 1. Create a new file in this directory named {EntityName}Model.js
 * 2. Export a function that takes (sequelize, Sequelize) as parameters
 * 3. Define your model using sequelize.define() with:
 *    - Table name (plural, snake_case)
 *    - Attributes schema
 *    - Options object with timestamps and underscored
 * 4. Define associations in an associate() method on the model
 * 5. Return the model
 *
 * Example:
 * module.exports = (sequelize, Sequelize) => {
 *   const NewModel = sequelize.define('table_name', {
 *     id: {
 *       type: Sequelize.INTEGER,
 *       primaryKey: true,
 *       autoIncrement: true
 *     },
 *   }, {
 *     timestamps: true,
 *     underscored: true
 *   });
 *
 *   NewModel.associate = (models) => {
 *     NewModel.belongsTo(models.otherModel);
 *     NewModel.hasMany(models.anotherModel);
 *   };
 *
 *   return NewModel;
 * };
 */

/* -------------------------------------------------------------------------- */
/*                                Models                                      */
/* -------------------------------------------------------------------------- */

const modelFiles = fs
  .readdirSync(__dirname)
  .filter(
    (file) =>
      file.indexOf('.') !== 0 &&
      file !== 'index.js' &&
      file.endsWith('Model.js'),
  );

modelFiles.forEach((file) => {
  const model = require(path.join(__dirname, file))(sequelize, Sequelize);
  const modelName = file.replace('.js', '');
  db[modelName] = model;
});

/* -------------------------------------------------------------------------- */
/*                                Associations                                */
/* -------------------------------------------------------------------------- */

/**
 * Initialize model associations
 * Calls associate() method on each model if it exists
 * This allows models to define their own associations
 * @function
 * @param {Object} db - Database object containing all models
 */
Object.keys(db).forEach((modelName) => {
  if (db[modelName].associate) {
    db[modelName].associate(db);
  }
});

module.exports = db;
