module.exports = (sequelize, Sequelize) => {
  const CompanyModel = sequelize.define(
    'companies',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      code: {
        type: Sequelize.BIGINT,
        allowNull: false,
        unique: true,
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      initial: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      tin: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      address: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      contactNumber: {
        type: Sequelize.STRING(255),
        allowNull: true,
        field: 'contact_number',
      },
      category: {
        type: Sequelize.ENUM('company', 'association'),
        allowNull: false,
        defaultValue: 'company',
      },
      areaCode: {
        type: Sequelize.STRING(255),
        allowNull: true,
        field: 'area_code',
      },
      displayName: {
        type: Sequelize.VIRTUAL,
        get() {
          return `${this.initial} - ${this.name}`;
        },
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  CompanyModel.associate = (models) => {
    CompanyModel.belongsToMany(models.projectModel, {
      through: 'project_companies',
      foreignKey: 'companyId',
      otherKey: 'projectId',
      as: 'taggedProjects',
    });

    CompanyModel.hasMany(models.projectModel, {
      foreignKey: 'company_code',
      sourceKey: 'code',
      as: 'projects',
    });

    CompanyModel.hasMany(models.ofmItemListModel, {
      foreignKey: 'company_code',
      sourceKey: 'code',
      as: 'ofmItemLists',
    });

    CompanyModel.hasMany(models.nonRequisitionModel, {
      foreignKey: 'companyId',
      as: 'nonRS',
    });

    CompanyModel.hasMany(models.requisitionModel, {
      foreignKey: 'companyId',
      as: 'requisitions',
    });

    CompanyModel.hasMany(models.historyModel, {
      foreignKey: 'companyId',
      as: 'companies',
    });
  };

  return CompanyModel;
};
