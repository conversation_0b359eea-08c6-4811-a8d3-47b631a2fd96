const BaseRepository = require('./baseRepository');
const { PO_STATUS } = require('../../domain/constants/purchaseOrderConstants');

class PurchaseOrderRepository extends BaseRepository {
  constructor ({ db }) {
    super(db.purchaseOrderModel);
    this.db = db;
  }

  async getPOWithItsDeliveryReceipts(purchaseOrderId, invoiceId = null) {
    return this.getById(purchaseOrderId, {
      attributes: ['id', 'requisitionId', 'status'],
      include: [
        {
          model: this.db.deliveryReceiptModel,
          as: 'deliveryReceipts',
          where: {
            isDraft: false,
            invoiceId: {
              [this.db.Sequelize.Op.or]: [null, invoiceId]
            }
          },
          required: false
        }
      ]
    })
  }

  async getPurchaseOrderTotals(purchaseOrderId) {
    const sqlQuery = `
      SELECT
        -- Base amount calculation
        SUM(cis.unit_price * poi.quantity_purchased) AS total_base_amount,
        
        -- Discount calculation based on discount type
        SUM(
          CASE
            WHEN cis.discount_type = 'fixed' THEN cis.discount_value * poi.quantity_purchased
            WHEN cis.discount_type = 'percent' THEN (cis.unit_price * (cis.discount_value / 100)) * poi.quantity_purchased
            ELSE 0
          END
        ) AS total_discount_amount,
        
        -- Discounted amount (base amount minus discount)
        SUM(cis.unit_price * poi.quantity_purchased) - 
        SUM(
          CASE
            WHEN cis.discount_type = 'fixed' THEN cis.discount_value * poi.quantity_purchased
            WHEN cis.discount_type = 'percent' THEN (cis.unit_price * (cis.discount_value / 100)) * poi.quantity_purchased
            ELSE 0
          END
        ) AS total_discounted_amount,
        
        -- Additional fees from purchase order (using MAX since these are the same for all rows of a PO)
        MAX(COALESCE(po.withholding_tax_deduction, 0)) + 
        MAX(COALESCE(po.delivery_fee, 0)) + 
        MAX(COALESCE(po.tip, 0)) + 
        MAX(COALESCE(po.extra_charges, 0)) AS total_additional_fees,
        
        -- Grand total calculation (discounted amount + additional fees)
        (SUM(cis.unit_price * poi.quantity_purchased) - 
         SUM(
           CASE
             WHEN cis.discount_type = 'fixed' THEN cis.discount_value * poi.quantity_purchased
             WHEN cis.discount_type = 'percent' THEN (cis.unit_price * (cis.discount_value / 100)) * poi.quantity_purchased
             ELSE 0
           END
         )) +
        MAX(COALESCE(po.withholding_tax_deduction, 0)) + 
        MAX(COALESCE(po.delivery_fee, 0)) + 
        MAX(COALESCE(po.tip, 0)) + 
        MAX(COALESCE(po.extra_charges, 0)) AS grand_total
      FROM
        purchase_order_items poi
      JOIN
        purchase_orders po ON poi.purchase_order_id = po.id
      JOIN
        canvass_item_suppliers cis ON poi.canvass_item_supplier_id = cis.id
      WHERE
        po.id = :purchaseOrderId
      GROUP BY
        po.id
    `;

    const [result] = await this.db.sequelize.query(sqlQuery, {
      replacements: { purchaseOrderId },
      type: this.db.Sequelize.QueryTypes.SELECT
    });

    return {
      totalBaseAmount: Number(parseFloat(result.total_base_amount || 0)),
      totalDiscountAmount: Number(parseFloat(result.total_discount_amount || 0)),
      totalDiscountedAmount: Number(parseFloat(result.total_discounted_amount || 0)),
      totalAdditionalFees: Number(parseFloat(result.total_additional_fees || 0)),
      grandTotal: Number(parseFloat(result.grand_total || 0))
    };
  }
}

module.exports = PurchaseOrderRepository;
