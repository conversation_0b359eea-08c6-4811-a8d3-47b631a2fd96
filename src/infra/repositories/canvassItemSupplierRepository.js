const BaseRepository = require('./baseRepository');

class CanvassItemSupplierRepository extends BaseRepository {
  constructor({ db }) {
    super(db.canvassItemSupplierModel);
    this.db = db;
  }

  async getSelectedSupplierByCanvassId(canvassItemIds = []) {
    const selectedSuppliers = await this.findAll({
      where: {
        isSelected: true,
        canvassItemId: {
          [this.db.Sequelize.Op.in]: canvassItemIds,
        },
      },
      include: [
        {
          association: 'canvassItem',
          as: 'canvassItem',
        },
      ],
      paginate: false,
    });

    return selectedSuppliers;
  }
}

module.exports = CanvassItemSupplierRepository;
