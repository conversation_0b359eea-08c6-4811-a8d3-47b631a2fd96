const BaseRepository = require('./baseRepository');

class RsPaymentRequestApproverRepository extends BaseRepository {
  constructor({ db, constants }) {
    super(db.rsPaymentRequestApproverModel);

    this.db = db;
    this.Sequelize = db.Sequelize;
    this.constants = constants;
  }

  async getPRApprovers(paymentRequestId) {
    const today = new Date(new Date().setHours(0, 0, 0, 0));
    const local = new Date(
      today.getTime() - today.getTimezoneOffset() * 60 * 1000,
    );

    const prApprovers = await this.findAll({
      paginate: false,
      where: { paymentRequestId },
      include: [
        {
          association: 'approver',
          as: 'approver',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'username',
            [
              this.db.Sequelize.fn(
                'CONCAT',
                this.db.Sequelize.col('approver.first_name'),
                ' ',
                this.db.Sequelize.col('approver.last_name'),
              ),
              'fullName',
            ],
          ],
          include: [
            { association: 'role', as: 'role', attributes: ['id', 'name'] },
            {
              model: this.db.leaveModel,
              attributes: ['id', 'startDate', 'endDate', 'totalDays'],
              as: 'userLeaves',
              required: false,
              where: {
                [this.db.Sequelize.Op.and]: [
                  {
                    startDate: {
                      [this.db.Sequelize.Op.lte]: local,
                    },
                  },
                  {
                    endDate: {
                      [this.db.Sequelize.Op.gte]: local,
                    },
                  },
                ],
              },
            },
          ],
        },
        {
          model: this.db.userModel,
          as: 'altApprover',
          attributes: [
            'id',
            [
              this.db.Sequelize.fn(
                'CONCAT',
                this.db.Sequelize.col('altApprover.first_name'),
                ' ',
                this.db.Sequelize.col('altApprover.last_name'),
              ),
              'fullName',
            ],
          ],
          include: [
            { association: 'role', as: 'role', attributes: ['id', 'name'] },
          ],
        },
      ],
      order: [
        ['level', 'ASC'],
        ['isAdhoc', 'ASC'],
      ],
    });

    return prApprovers;
  }

  async findPendingApproversByUserId(userId) {
    const { PR_APPROVER_STATUS } = this.constants.rsPaymentRequest;

    const approvers = await this.findAll({
      paginate: false,
      attributes: ['id'],
      where: {
        userId: null,
        isAdhoc: false,
        status: PR_APPROVER_STATUS.PENDING,
        [this.db.Sequelize.Op.or]: [
          {
            '$paymentRequest.requisition.assigned_to$': userId,
            level: 1,
          },
          {
            '$paymentRequest.requisition.created_by$': userId,
            level: 2,
          },
        ],
      },
      include: [
        {
          association: 'paymentRequest',
          attributes: [],
          include: [
            {
              association: 'requisition',
            },
          ],
        },
      ],
    });

    return approvers;
  }

  async getAllRsPaymentRequestByUserId(request) {
    const {
      where = {},
      include = [],
      attributes = null,
      group = [],
      order = [['createdAt', 'DESC']],
      distinct = false,
      whereRequisition = {},
      whereCanvass = {},
      wherePurchaseOrder = {},
      wherePaymentRequest = {},
    } = request;

    const queryOptions = {
      where,
      include,
      attributes,
      order,
      distinct,
      paginate: false,
      group,
      attributes: ['id', 'level', 'altApproverId', 'userId', 'paymentRequestId'],
      include: [
        {
          model: this.db.rsPaymentRequestModel,
          attributes: [],
          where: wherePaymentRequest,
          as: 'paymentRequest',
          include: [
            {
              model: this.db.requisitionModel,
              where: whereRequisition,
              attributes: [],
              as: 'requisition',
            },
          ],
        },
        {
          model: this.db.userModel,
          attributes: ['id', 'firstName', 'lastName', 'username'],
          as: 'altApprover',
          include: [
            { model: this.db.roleModel, attributes: ['name'], as: 'role' },
          ],
        },
        {
          model: this.db.userModel,
          attributes: ['id', 'firstName', 'lastName', 'username'],
          as: 'approver',
          include: [
            { model: this.db.roleModel, attributes: ['name'], as: 'role' },
          ],
        },
      ],
    };

    const rsPaymentRequests = await this.tableName.findAll(queryOptions);

    return rsPaymentRequests;
  }
}
module.exports = RsPaymentRequestApproverRepository;
