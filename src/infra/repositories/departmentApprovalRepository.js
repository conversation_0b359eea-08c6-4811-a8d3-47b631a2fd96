const BaseRepository = require('./baseRepository');

class DepartmentApprovalRepository extends BaseRepository {
  constructor ({
    db,
    fastify,
  }) {
    super(db.departmentApprovalModel);
    this.fastify = fastify;
    this.Sequelize = db.Sequelize;
    this.db = db;
  }

  async getAllApprovals(payload) {
    const { where, limit, page, paginate } = payload;
    const allApprovals = await this.findAll({
      where,
      limit,
      page,
      paginate,
      order: [
        ['approvalTypeCode', 'ASC'],
        ['level', 'ASC'],
      ],
      include: [
        {
          association: 'approver',
          attributes: ['id', 'firstName', 'lastName'],
          include: [
            {
              association: 'role',
              attributes: ['id', 'name'],
            },
            // TODO: Include alternative approver here once ready
          ],
        },
      ],
      attributes: {
        exclude: ['approverId'],
      },
    });

    return allApprovals;
  }

  async getAdditionalApprovers({ where, departmentId, transaction, optionalApproverItemIds }) {
    try {
      const additionalApprovers = await this.findAll({
        where,
        include: [
          {
            model: this.db.userModel,
            as: 'approver',
            attributes: [],
            where: { status: 'active' },
            required: true,
          },
        ],
        attributes: {
          include: [
            [this.Sequelize.literal("'department'"), 'modelType'],
            [this.Sequelize.col('department_approvals.department_id'), 'modelId'],
            [
              this.Sequelize.literal(`CAST(CASE 
                WHEN is_optional = true THEN 
                  (SELECT COUNT(*) FROM department_approvals 
                  WHERE department_id = ${departmentId} AND is_optional = false) + 1
                ELSE level
              END AS INTEGER)`),
              'level'
            ],
            'approverId',
            [
              this.Sequelize.literal(`CAST(CASE 
                WHEN is_optional = true THEN true
                ELSE false
              END AS BOOLEAN)`),
              'isOptionalApprover'
            ],
            [
              this.Sequelize.literal(`CAST(CASE 
                WHEN is_optional = true THEN ARRAY[${optionalApproverItemIds}]::INTEGER[]
                ELSE null
              END AS INTEGER[])`),
              'optionalApproverItemIds'
            ],
          ],
        },
        transaction,
      });

      this.fastify.log.info(`GET_DEPARTMENT_APPROVERS_DATA: ${JSON.stringify(additionalApprovers)}`);

      return additionalApprovers;
    } catch (error) {
      this.fastify.log.error(`GET_DEPARTMENT_APPROVERS_ERROR: ${error.message}`);
      throw error;
    }
  }
}

module.exports = DepartmentApprovalRepository;
