const BaseRepository = require('./baseRepository');

class CanvassRequisitionRepository extends BaseRepository {
  constructor({ db, entities, utils }) {
    super(db.canvassRequisitionModel);

    this.db = db;
    this.Sequelize = db.Sequelize;
    this.utils = utils;
    this.entities = entities;
  }

  async getExistingCanvass(payload = {}, options = {}) {
    const { canvassId, requisitionId } = payload;

    if (!canvassId) return;

    return this.findOne({
      where: {
        id: canvassId,
        requisitionId,
      },
      ...options,
    });
  }

  async getAllCanvass(payload = {}) {
    const { requisitionId, limit, page, sortBy, paginate, filterBy } = payload;

    const whereClause = { requisitionId };
    const defaultOrder = [['createdAt', 'DESC']];

    const { canvassSortSchema, canvassFilterSchema } = this.entities.canvass;

    const parsedSortBy = canvassSortSchema.parse(sortBy);
    const orderClauses = parsedSortBy?.map(([field, direction]) => {
      if (field === 'canvassNumber') {
        return ['cs_number', direction];
      }

      if (field === 'lastApprover') {
        return [
          this.db.Sequelize.literal(
            `(SELECT "users"."first_name" 
              FROM "canvass_approvers" 
              LEFT JOIN "users" ON "users"."id" = "canvass_approvers"."user_id"
              WHERE "canvass_approvers"."canvass_requisition_id" = "canvass_requisitions"."id"
              AND "canvass_approvers"."status" = 'pending'
              ORDER BY "canvass_approvers"."level" ASC, "canvass_approvers"."is_adhoc" DESC
              LIMIT 1)`,
          ),
          direction,
        ];
      }

      return [field, direction];
    });

    const parsedFilterBy = canvassFilterSchema.parse(filterBy);
    const { canvassNumber } = this.utils.buildFilterWhereClause(parsedFilterBy);

    if (canvassNumber) {
      whereClause[this.Sequelize.Op.or] = [
        this.Sequelize.literal(`
          CASE 
            WHEN "canvass_requisitions"."status" = 'draft' 
            THEN CONCAT('CS-TMP-', "requisition"."company_code",cs_letter, draft_cs_number)
            ELSE CONCAT('CS-', "requisition"."company_code", cs_letter, cs_number)
          END ILIKE '%${canvassNumber}%'
        `),
      ];
    }

    return await this.findAll({
      limit,
      page,
      paginate,
      order: orderClauses || defaultOrder,
      where: whereClause,
      include: [
        {
          association: 'requisition',
          as: 'requisition',
          attributes: [
            'id',
            'assignedTo',
            'status',
            'type',
            'dateRequired',
            'companyCode',
          ],
        },
        {
          required: false,
          association: 'canvassApprovers',
          as: 'latestApprover',
          attributes: ['id', 'userId', 'level', 'status', 'isAdhoc'],
          where: {
            status: 'pending',
          },
          order: [
            ['level', 'ASC'],
            ['isAdhoc', 'DESC'],
          ],
          limit: 1,
          include: [
            {
              required: false,
              association: 'approver',
              as: 'approver',
              attributes: ['id', 'firstName', 'lastName'],
            },
          ],
        },
      ],
    });
  }

  async getCanvass(canvassId, options = {}) {
    return await this.getById(canvassId, {
      ...options,
      include: [
        {
          association: 'requisition',
          as: 'requisition',
          attributes: ['id', 'assignedTo', 'createdBy', 'type', 'companyCode'],
        },
      ],
    });
  }

  async getDistinctRsIds(supplierId) {
    const rsIds = await this.tableName.findAll({
      attributes: ['requisitionId'],
      distinct: true,
      include: [
        {
          model: this.db.canvassItemModel,
          as: 'canvassItems',
          include: [
            {
              model: this.db.canvassItemSupplierModel,
              as: 'suppliers',
              where: {
                supplierId,
              },
            },
          ],
        },
      ],
    });

    return rsIds.map((item) => item.requisitionId);
  }
}

module.exports = CanvassRequisitionRepository;
