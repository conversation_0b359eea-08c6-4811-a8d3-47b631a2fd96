const BaseRepository = require('./baseRepository');

class ForceCloseRepository extends BaseRepository {
  constructor({
    db,
    clientErrors,
    fastify,
    requisitionRepository,
    purchaseOrderRepository,
    canvassRequisitionRepository,
    deliveryReceiptRepository,
    invoiceReportRepository,
    rsPaymentRequestRepository,
    itemRepository,
    historyRepository,
  }) {
    // Force close repository doesn't have its own model, it orchestrates other repositories
    super(null);

    this.db = db;
    this.Sequelize = db.Sequelize;
    this.clientErrors = clientErrors;
    this.fastify = fastify;

    // Inject all required repositories for force close operations
    this.requisitionRepository = requisitionRepository;
    this.purchaseOrderRepository = purchaseOrderRepository;
    this.canvassRequisitionRepository = canvassRequisitionRepository;
    this.deliveryReceiptRepository = deliveryReceiptRepository;
    this.invoiceReportRepository = invoiceReportRepository;
    this.rsPaymentRequestRepository = rsPaymentRequestRepository;
    this.itemRepository = itemRepository;
    this.historyRepository = historyRepository;
  }

  /**
   * Execute force close workflow with transaction safety
   * Centralizes all database operations for force close
   */
  async executeForceCloseWorkflow({
    requisitionId,
    userId,
    scenario,
    notes,
    transaction,
  }) {
    this.fastify.log.info(`Executing force close workflow for RS: ${requisitionId}, Scenario: ${scenario}`);

    const result = {
      requisitionId,
      scenario,
      documentsUpdated: [],
      quantitiesReturned: {},
      poAdjustments: [],
    };

    try {
      // Update requisition status to CLOSED
      await this._updateRequisitionStatus(requisitionId, 'CLOSED', userId, transaction);
      result.documentsUpdated.push({ type: 'requisition', id: requisitionId, status: 'CLOSED' });

      // Determine validation path based on scenario
      let validationPath = 'CLOSED_PO_PATH'; // Default for most scenarios
      if (scenario === 'ACTIVE_PO_PARTIAL_DELIVERY') {
        validationPath = 'ACTIVE_PO_PATH';
      }

      // Execute scenario-specific operations
      switch (scenario) {
        case 'ACTIVE_PO_PARTIAL_DELIVERY':
          await this._handleActivePOPartialDeliveryScenario(requisitionId, userId, transaction, result);
          break;
        case 'CLOSED_PO_REMAINING_QTY':
          await this._handleClosedPORemainingQtyScenario(requisitionId, userId, transaction, result);
          break;
        case 'CLOSED_PO_PENDING_CS':
          await this._handleClosedPOPendingCSScenario(requisitionId, userId, transaction, result);
          break;
        default:
          throw new Error(`Unknown force close scenario: ${scenario}`);
      }

      // Add force close notes and audit trail
      await this._addForceCloseAuditTrail(requisitionId, userId, scenario, notes, transaction, {
        validationPath,
        quantitiesAffected: result.quantitiesReturned,
        documentsCancelled: result.documentsUpdated,
        poAdjustments: result.poAdjustments
      });

      this.fastify.log.info(`Force close workflow completed successfully for RS: ${requisitionId}`);
      return result;

    } catch (error) {
      this.fastify.log.error(`Force close workflow failed for RS: ${requisitionId} - ${error.message}`);
      throw error;
    }
  }

  /**
   * Update requisition status with audit trail
   */
  async _updateRequisitionStatus(requisitionId, status, userId, transaction) {
    await this.requisitionRepository.update(
      { id: requisitionId },
      { status },
      { transaction, userId }
    );
  }

  /**
   * Handle Scenario 1: Active Purchase Orders with Partial Deliveries
   * Update PO amounts and quantities to reflect delivered quantities only
   */
  async _handleActivePOPartialDeliveryScenario(requisitionId, userId, transaction, result) {
    this.fastify.log.info(`Handling active PO partial delivery scenario for RS: ${requisitionId}`);

    // Get purchase orders for this requisition
    const purchaseOrders = await this._getPurchaseOrdersForRequisition(requisitionId, transaction);

    for (const po of purchaseOrders) {
      // Get delivered quantities for this PO
      const deliveredData = await this._getDeliveredQuantitiesForPO(po.id, transaction);

      if (deliveredData.totalDelivered > 0) {
        // Update PO amount and quantity to reflect only delivered quantities
        const originalAmount = po.amount;
        const originalQuantity = po.quantity;
        const newAmount = deliveredData.totalAmount;
        const newQuantity = deliveredData.totalQuantity;

        await this._updatePOAmountAndQuantity(po.id, newAmount, newQuantity, userId, transaction);

        // Generate system notes for the PO modification
        const systemNotes = `Force Close: PO updated to reflect delivered quantities only. Original Amount: ${originalAmount}, New Amount: ${newAmount}. Original Quantity: ${originalQuantity}, New Quantity: ${newQuantity}.`;
        await this._addPOSystemNotes(po.id, systemNotes, transaction);

        result.poAdjustments.push({
          poId: po.id,
          originalAmount,
          newAmount,
          originalQuantity,
          newQuantity,
          systemNotes,
        });

        // Update PO status to CLOSED
        await this._updatePOStatus(po.id, 'CLOSED', userId, transaction);
        result.documentsUpdated.push({ type: 'purchase_order', id: po.id, status: 'CLOSED' });
      }
    }

    // Return unfulfilled quantities to GFQ for OFM items
    await this._returnUnfulfilledQuantitiesToGFQ(requisitionId, transaction, result);
  }

  /**
   * Return unfulfilled quantities to GFQ for OFM items
   */
  async _returnUnfulfilledQuantitiesToGFQ(requisitionId, transaction, result) {
    // Get remaining quantities that need to be returned to GFQ
    const remainingItems = await this._getRemainingQuantitiesToCanvass(requisitionId, transaction);

    for (const item of remainingItems) {
      if (item.remainingQty > 0 && (item.type === 'OFM' || item.type === 'OFM-TOM')) {
        await this._returnQuantityToGFQ(item, transaction);
        result.quantitiesReturned[item.id] = {
          itemName: item.name,
          returnedQty: item.remainingQty,
          type: item.type,
        };
      }
    }
  }

  /**
   * Handle Scenario 2: All Purchase Orders Closed/Cancelled with Remaining Quantities
   * Zero out remaining quantities and return to GFQ for OFM items
   */
  async _handleClosedPORemainingQtyScenario(requisitionId, userId, transaction, result) {
    this.fastify.log.info(`Handling closed PO remaining quantity scenario for RS: ${requisitionId}`);

    // Zero out all remaining quantities for canvassing
    const remainingItems = await this._getRemainingQuantitiesToCanvass(requisitionId, transaction);

    for (const item of remainingItems) {
      if (item.remainingQty > 0) {
        // Return quantities to GFQ for OFM and OFM-TOM items only
        if (item.type === 'OFM' || item.type === 'OFM-TOM') {
          await this._returnQuantityToGFQ(item, transaction);
          result.quantitiesReturned[item.id] = {
            itemName: item.name,
            returnedQty: item.remainingQty,
            type: item.type,
          };
        }

        // Zero out the remaining quantity
        await this._zeroOutRemainingQuantity(item.id, userId, transaction);
      }
    }
  }

  /**
   * Handle Scenario 3: Closed Purchase Orders with Pending Canvass Sheet Approvals
   * Cancel pending canvass sheets, zero out quantities, and return to GFQ
   */
  async _handleClosedPOPendingCSScenario(requisitionId, userId, transaction, result) {
    this.fastify.log.info(`Handling closed PO pending CS scenario for RS: ${requisitionId}`);

    // Get pending canvass sheets
    const pendingCanvassSheets = await this._getPendingCanvassSheets(requisitionId, transaction);

    // Cancel pending canvass sheets
    for (const cs of pendingCanvassSheets) {
      await this._cancelCanvassSheet(cs.id, userId, transaction);
      result.documentsUpdated.push({ type: 'canvass_sheet', id: cs.id, status: 'CANCELLED' });
    }

    // Zero out remaining quantities and return to GFQ (similar to scenario 2)
    await this._handleClosedPORemainingQtyScenario(requisitionId, userId, transaction, result);
  }

  /**
   * Add comprehensive audit trail for force close operation
   * Creates a detailed log entry in the force_close_logs table
   */
  async _addForceCloseAuditTrail(requisitionId, userId, scenario, notes, transaction, additionalData = {}) {
    try {
      // Create comprehensive force close log entry
      const forceCloseLog = await this.db.forceCloseLogModel.create({
        requisitionId,
        userId,
        scenarioType: scenario,
        validationPath: additionalData.validationPath || 'UNKNOWN_PATH',
        quantitiesAffected: additionalData.quantitiesAffected || null,
        documentsCancelled: additionalData.documentsCancelled || null,
        poAdjustments: additionalData.poAdjustments || null,
        forceCloseNotes: notes,
      }, { transaction });

      this.fastify.log.info(`Force close audit trail created with ID: ${forceCloseLog.id} for RS: ${requisitionId}`);

      return forceCloseLog;

    } catch (error) {
      this.fastify.log.error(`Failed to create force close audit trail for RS: ${requisitionId} - ${error.message}`);
      throw error;
    }
  }

  // Placeholder methods for specific database operations
  // These will be implemented in subsequent tasks when database schema is updated

  async _getRemainingQuantitiesToCanvass(requisitionId, transaction) {
    try {
      // Get requisition with items and related canvass data
      const requisition = await this.db.requisitionModel.findByPk(requisitionId, {
        include: [
          {
            model: this.db.requisitionItemListModel,
            as: 'requisitionItemLists',
            include: [
              {
                model: this.db.itemModel,
                as: 'item',
                attributes: ['id', 'itmDes', 'itemCd', 'unit']
              },
              {
                model: this.db.nonOfmItemModel,
                as: 'nonOfmItem',
                attributes: ['id', 'itemName', 'itemType', 'unit']
              }
            ]
          },
          {
            model: this.db.canvassRequisitionModel,
            as: 'canvassRequisitions',
            where: { status: ['CS_APPROVED', 'APPROVED'] },
            required: false,
            include: [
              {
                model: this.db.canvassItemModel,
                as: 'canvassItems'
              }
            ]
          }
        ],
        transaction
      });

      if (!requisition || !requisition.requisitionItemLists) {
        return [];
      }

      const remainingItems = [];

      for (const reqItem of requisition.requisitionItemLists) {
        const requestedQty = parseFloat(reqItem.quantity) || 0;
        let totalCanvassedQty = 0;

        // Calculate total canvassed quantity for this item
        for (const canvass of requisition.canvassRequisitions || []) {
          const canvassItems = canvass.canvassItems?.filter(ci =>
            ci.requisitionItemId === reqItem.id
          ) || [];

          for (const canvassItem of canvassItems) {
            totalCanvassedQty += parseFloat(canvassItem.quantity) || 0;
          }
        }

        const remainingQty = Math.max(0, requestedQty - totalCanvassedQty);

        if (remainingQty > 0) {
          // Get item name based on item type
          let itemName = 'Unknown Item';
          let itemType = reqItem.itemType || 'UNKNOWN';

          if (reqItem.itemType === 'ofm' || reqItem.itemType === 'ofm-tom') {
            itemName = reqItem.item?.itmDes || 'Unknown OFM Item';
          } else if (reqItem.itemType === 'non-ofm' || reqItem.itemType === 'non-ofm-tom') {
            itemName = reqItem.nonOfmItem?.itemName || 'Unknown Non-OFM Item';
          }

          remainingItems.push({
            id: reqItem.id,
            itemId: reqItem.itemId,
            name: itemName,
            type: itemType.toUpperCase(),
            requestedQty,
            canvassedQty: totalCanvassedQty,
            remainingQty,
            unitPrice: 0 // Will be calculated from canvass items if needed
          });
        }
      }

      return remainingItems;
    } catch (error) {
      this.fastify.log.error(`Failed to get remaining quantities for requisition ${requisitionId}: ${error.message}`);
      return [];
    }
  }

  async _returnQuantityToGFQ(item, transaction) {
    // TODO: Implement GFQ return logic for OFM items
  }

  async _zeroOutRemainingQuantity(itemId, userId, transaction) {
    // TODO: Implement quantity zeroing logic
  }

  async _getPendingCanvassSheets(requisitionId, transaction) {
    // TODO: Implement pending canvass sheet retrieval
    return [];
  }

  async _cancelCanvassSheet(csId, userId, transaction) {
    // TODO: Implement canvass sheet cancellation
  }

  async _getPurchaseOrdersForRequisition(requisitionId, transaction) {
    try {
      const purchaseOrders = await this.db.purchaseOrderModel.findAll({
        where: { requisitionId },
        include: [
          {
            model: this.db.purchaseOrderItemModel,
            as: 'purchaseOrderItems',
          }
        ],
        transaction
      });

      return purchaseOrders;
    } catch (error) {
      this.fastify.log.error(`Failed to get purchase orders for requisition ${requisitionId}: ${error.message}`);
      throw error;
    }
  }

  async _getDeliveredQuantitiesForPO(poId, transaction) {
    try {
      // Get all delivery receipts for this PO
      const deliveryReceipts = await this.db.deliveryReceiptModel.findAll({
        where: {
          purchaseOrderId: poId,
          status: 'APPROVED' // Only count approved deliveries
        },
        include: [
          {
            model: this.db.deliveryReceiptItemModel,
            as: 'deliveryReceiptItems',
            include: [
              {
                model: this.db.itemModel,
                as: 'item',
                attributes: ['id', 'itmDes', 'itemCd']
              },
              {
                model: this.db.nonOfmItemModel,
                as: 'nonOfmItem',
                attributes: ['id', 'itemName', 'itemType']
              }
            ]
          }
        ],
        transaction
      });

      let totalDelivered = 0;
      let totalAmount = 0;
      let totalQuantity = 0;

      for (const dr of deliveryReceipts) {
        for (const drItem of dr.deliveryReceiptItems || []) {
          const quantity = parseFloat(drItem.quantity) || 0;
          const unitPrice = parseFloat(drItem.unitPrice) || 0; // Use DR item unit price

          totalQuantity += quantity;
          totalAmount += quantity * unitPrice;
          totalDelivered += quantity;
        }
      }

      return {
        totalDelivered,
        totalAmount: parseFloat(totalAmount.toFixed(2)),
        totalQuantity: parseFloat(totalQuantity.toFixed(2))
      };
    } catch (error) {
      this.fastify.log.error(`Failed to get delivered quantities for PO ${poId}: ${error.message}`);
      return { totalDelivered: 0, totalAmount: 0, totalQuantity: 0 };
    }
  }

  async _updatePOAmountAndQuantity(poId, amount, quantity, userId, transaction) {
    try {
      // First, get the current PO to store original values
      const currentPO = await this.db.purchaseOrderModel.findByPk(poId, { transaction });

      if (!currentPO) {
        throw new Error(`Purchase Order ${poId} not found`);
      }

      // Store original values if not already stored
      const updateData = {
        amount: parseFloat(amount),
        quantity: parseFloat(quantity),
        updatedBy: userId,
        updatedAt: new Date()
      };

      // Store original values if this is the first force close modification
      if (!currentPO.originalAmount) {
        updateData.originalAmount = currentPO.amount;
      }
      if (!currentPO.originalQuantity) {
        updateData.originalQuantity = currentPO.quantity;
      }

      await this.db.purchaseOrderModel.update(updateData, {
        where: { id: poId },
        transaction
      });

      this.fastify.log.info(`Updated PO ${poId} - Amount: ${amount}, Quantity: ${quantity}`);
    } catch (error) {
      this.fastify.log.error(`Failed to update PO amount and quantity for PO ${poId}: ${error.message}`);
      throw error;
    }
  }

  async _addPOSystemNotes(poId, notes, transaction) {
    try {
      // Get current system notes
      const currentPO = await this.db.purchaseOrderModel.findByPk(poId, { transaction });

      if (!currentPO) {
        throw new Error(`Purchase Order ${poId} not found`);
      }

      const existingNotes = currentPO.systemGeneratedNotes || '';
      const timestamp = new Date().toISOString();
      const newNotes = existingNotes
        ? `${existingNotes}\n[${timestamp}] ${notes}`
        : `[${timestamp}] ${notes}`;

      await this.db.purchaseOrderModel.update({
        systemGeneratedNotes: newNotes
      }, {
        where: { id: poId },
        transaction
      });

      this.fastify.log.info(`Added system notes to PO ${poId}`);
    } catch (error) {
      this.fastify.log.error(`Failed to add system notes to PO ${poId}: ${error.message}`);
      throw error;
    }
  }

  async _updatePOStatus(poId, status, userId, transaction) {
    try {
      await this.db.purchaseOrderModel.update({
        status,
        updatedBy: userId,
        updatedAt: new Date()
      }, {
        where: { id: poId },
        transaction
      });

      this.fastify.log.info(`Updated PO ${poId} status to ${status}`);
    } catch (error) {
      this.fastify.log.error(`Failed to update PO status for PO ${poId}: ${error.message}`);
      throw error;
    }
  }

  async _getDraftCanvassSheets(requisitionId, transaction) {
    // TODO: Implement draft CS retrieval
    return [];
  }

  async _getDraftDeliveryReceipts(requisitionId, transaction) {
    // TODO: Implement draft DR retrieval
    return [];
  }

  async _getDraftInvoiceReports(requisitionId, transaction) {
    // TODO: Implement draft IR retrieval
    return [];
  }

  async _getDraftPaymentRequests(requisitionId, transaction) {
    // TODO: Implement draft PR retrieval
    return [];
  }

  async _cancelDeliveryReceipt(drId, userId, transaction) {
    // TODO: Implement DR cancellation
  }

  async _cancelInvoiceReport(irId, userId, transaction) {
    // TODO: Implement IR cancellation
  }

  async _cancelPaymentRequest(prId, userId, transaction) {
    // TODO: Implement PR cancellation
  }

  /**
   * Get force close history and audit trail for a requisition
   * Returns historical force close operations and related audit information
   */
  async getForceCloseHistory({ requisitionId, userId }) {
    this.fastify.log.info(`Retrieving force close history from repository for RS: ${requisitionId}`);

    try {
      // Get force close logs from the database
      const forceCloseLogs = await this.db.forceCloseLogModel.findAll({
        where: { requisitionId },
        order: [['createdAt', 'DESC']],
        include: [
          {
            model: this.db.userModel,
            as: 'user',
            attributes: ['id', 'username', 'email'],
          },
          {
            model: this.db.requisitionModel,
            as: 'requisition',
            attributes: ['id', 'rsNumber', 'rsLetter', 'status'],
          },
        ],
      });

      // Get related audit trail from general history (simplified for now)
      const auditTrail = [];

      // Calculate summary statistics
      const summary = {
        totalForceCloses: forceCloseLogs.length,
        lastForceCloseDate: forceCloseLogs.length > 0 ? forceCloseLogs[0].createdAt : null,
        scenarios: [...new Set(forceCloseLogs.map(log => log.scenarioType))],
        validationPaths: [...new Set(forceCloseLogs.map(log => log.validationPath))],
        totalDocumentsCancelled: forceCloseLogs.reduce((total, log) => {
          const docs = log.documentsCancelled;
          return total + (docs ? Object.keys(docs).length : 0);
        }, 0),
        totalQuantitiesAffected: forceCloseLogs.reduce((total, log) => {
          const quantities = log.quantitiesAffected;
          return total + (quantities ? Object.keys(quantities).length : 0);
        }, 0),
      };

      this.fastify.log.info(`Force close history retrieved from repository for RS: ${requisitionId} - Found ${forceCloseLogs.length} logs`);

      return {
        history: forceCloseLogs,
        auditTrail,
        summary,
      };

    } catch (error) {
      this.fastify.log.error(`Force close history retrieval failed in repository for RS: ${requisitionId} - ${error.message}`);
      throw error;
    }
  }
}

module.exports = ForceCloseRepository;
