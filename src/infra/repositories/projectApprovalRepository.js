const BaseRepository = require('./baseRepository');

class ProjectApprovalRepository extends BaseRepository {
  constructor ({ db, fastify }) {
    super(db.projectApprovalModel);
    this.fastify = fastify;
    this.Sequelize = db.Sequelize;
    this.db = db;
  }

  async getAllApprovals(payload) {
    const { where, limit, page, paginate } = payload;
    const allApprovals = await this.findAll({
      where,
      limit,
      page,
      paginate,
      order: [
        ['approvalTypeCode', 'ASC'],
        ['level', 'ASC'],
      ],
      include: [
        {
          association: 'approver',
          attributes: ['id', 'firstName', 'lastName'],
          include: [
            {
              association: 'role',
              attributes: ['id', 'name'],
            },
            // TODO: Include alternative approver here once ready
          ],
        },
      ],
      attributes: {
        exclude: ['approverId'],
      },
    });

    return allApprovals;
  }

  async getAdditionalApprovers({ where, projectId, transaction, optionalApproverItemIds }) {
    try {
      const additionalApprovers = await this.findAll(
        {
          where,
          include: [
            {
              model: this.db.userModel,
              as: 'approver',
              attributes: [],
              where: {
                status: 'active',
              },
              required: true,
            },
          ],
          attributes: {
            include: [
              [this.Sequelize.literal("'project'"), 'modelType'],
              [this.Sequelize.col('project_id'), 'modelId'],
              [
                this.Sequelize.literal(`CAST(CASE 
                  WHEN is_optional = true THEN 
                    (SELECT COUNT(*) FROM project_approvals 
                    WHERE project_id = ${projectId} AND is_optional = false) + 1
                  ELSE level
                END AS INTEGER)`),
                'level'
              ],
              'approverId',
              [
                this.Sequelize.literal(`CAST(CASE 
                  WHEN is_optional = true THEN true
                  ELSE false
                END AS BOOLEAN)`),
                'isOptionalApprover'
              ],
              [
                this.Sequelize.literal(`CAST(CASE 
                  WHEN is_optional = true THEN ARRAY[${optionalApproverItemIds}]::INTEGER[]
                  ELSE null
                END AS INTEGER[])`),
                'optionalApproverItemIds'
              ],
            ],
          },
        },
        { transaction },
      );

      this.fastify.log.info(`GET_PROJECT_APPROVERS_DATA: ${JSON.stringify(additionalApprovers)}`);

      return additionalApprovers;
    } catch (error) {
      this.fastify.log.error(`GET_PROJECT_APPROVERS_ERROR: ${error.message}`);
      throw error;
    }
  }
}

module.exports = ProjectApprovalRepository;
