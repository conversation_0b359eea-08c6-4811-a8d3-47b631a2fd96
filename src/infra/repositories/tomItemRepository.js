const BaseRepository = require('./baseRepository');

class TomItemRepository extends BaseRepository {
  constructor ({ db }) {
    super(db.tomItemModel);

    this.db = db;
    this.Sequelize = db.Sequelize;
  }

  async destroyById(id) {
    return await this.tableName.destroy({
      where: { id },
    });
  }

  async getAllRequisitionItems(payload) {
    let whereClause = {};
    const { requisitionId, type, search, limit, page, order = [
      ['createdAt', 'DESC'],
    ] } = payload;

    if (search) {
      whereClause.name = { [this.Sequelize.Op.iLike]: `%${search}%` };
    }

    return await this.findAll({
      limit,
      page,
      order,
      where: { requisitionId, ...whereClause },
    });
  }
}

module.exports = TomItemRepository;
