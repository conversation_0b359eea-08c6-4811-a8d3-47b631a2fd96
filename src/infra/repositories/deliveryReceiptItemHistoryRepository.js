const BaseRepository = require('./baseRepository');

class DeliveryReceiptItemHistoryRepository extends BaseRepository {
  constructor({ db }) {
    super(db.deliveryReceiptItemHistoryModel);
  }

  async createHistoryRecord(data) {
    return await this.tableName.create(data);
  }

  async getHistoryByDeliveryReceiptItemId(deliveryReceiptItemId, { limit, page }) {
    const offset = (page - 1) * limit;
    return await this.tableName.findAndCountAll({
      where: {
        deliveryReceiptItemId: deliveryReceiptItemId,
      },
      order: [['createdAt', 'DESC']],
      limit,
      offset,
    });
  }

  
  async getLatestRecord(deliveryReceiptItemId) {
    return await this.tableName.findOne({
      where: {
        deliveryReceiptItemId: deliveryReceiptItemId,
      },
      order: [['createdAt', 'DESC']],
    });
  }
}

module.exports = DeliveryReceiptItemHistoryRepository;
