const BaseRepository = require('./baseRepository');

class SteelbarsRepository extends BaseRepository {
  constructor({ db, clientErrors, fastify }) {
    super(db.steelbarsModel);
    
    this.Sequelize = db.Sequelize;
    this.clientErrors = clientErrors;
    this.fastify = fastify;
  }

  async getAllSteelbars(payload) {
    const { limit, page, paginate, order = [['id', 'ASC']], whereClause } = payload;
  
    return await this.findAll({
      paginate,
      limit,
      page,
      order,
      where: whereClause,
      include: [
        {
          model: this.tableName.sequelize.model('items'),
          as: 'item',
          required: false,
        },
      ],
    });
  }

  async getSteelbarByAcctCd(acctCd) {
    if (!acctCd) return null;
    
    return await this.findOne({
      where: { ofm_acctcd: acctCd },
      attributes: ['grade', 'diameter', 'length', 'weight', 'kg_per_meter']
    });
  }
}

module.exports = SteelbarsRepository;