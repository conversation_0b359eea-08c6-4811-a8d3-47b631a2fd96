const BaseRepository = require('./baseRepository');

class CanvassApproverRepository extends BaseRepository {
  constructor({ db }) {
    super(db.canvassApproverModel);
    this.db = db;
    this.Sequelize = db.Sequelize;
  }

  async getAllCanvassApprovers(canvassId) {
    const today = new Date(new Date().setHours(0, 0, 0, 0));

    const local = new Date(
      today.getTime() - today.getTimezoneOffset() * 60 * 1000,
    );
    const canvassApprovers = await this.findAll({
      paginate: false,
      where: { canvassRequisitionId: canvassId },
      include: [
        {
          association: 'approver',
          as: 'approver',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'username',
            [
              this.Sequelize.fn(
                'CONCAT',
                this.Sequelize.col('approver.first_name'),
                ' ',
                this.Sequelize.col('approver.last_name'),
              ),
              'fullName',
            ],
          ],
          include: [
            { association: 'role', as: 'role', attributes: ['id', 'name'] },
            {
              model: this.db.leaveModel,
              attributes: ['id', 'startDate', 'endDate', 'totalDays'],
              as: 'userLeaves',
              required: false,
              where: {
                [this.Sequelize.Op.and]: [
                  {
                    startDate: {
                      [this.Sequelize.Op.lte]: local,
                    },
                  },
                  {
                    endDate: {
                      [this.Sequelize.Op.gte]: local,
                    },
                  },
                ],
              },
            },
          ],
        },
        {
          model: this.db.userModel,
          as: 'altApprover',
          attributes: [
            'id',
            [
              this.Sequelize.fn(
                'CONCAT',
                this.Sequelize.col('altApprover.first_name'),
                ' ',
                this.Sequelize.col('altApprover.last_name'),
              ),
              'fullName',
            ],
          ],
          include: [
            { association: 'role', as: 'role', attributes: ['id', 'name'] },
          ],
        },
      ],
      order: [
        ['level', 'ASC'],
        ['isAdhoc', 'ASC'],
      ],
    });

    return canvassApprovers;
  }

  async getAllCanvassByUserId(request) {
    const {
      where = {},
      include = [],
      attributes = null,
      group = [],
      order = [['createdAt', 'DESC']],
      distinct = false,
      whereRequisition = {},
      whereCanvass = {},
    } = request;

    const queryOptions = {
      where,
      include,
      attributes,
      order,
      distinct,
      paginate: false,
      group,
      attributes: ['id', 'level', 'altApproverId', 'userId', 'canvassRequisitionId'],
      include: [
        {
          model: this.db.canvassRequisitionModel,
          attributes: [],
          where: whereCanvass,
          as: 'canvass',
          include: [
            {
              model: this.db.requisitionModel,
              where: whereRequisition,
              attributes: [],
              as: 'requisition',
            },
          ],
        },
        {
          model: this.db.userModel,
          attributes: ['id', 'firstName', 'lastName', 'username'],
          as: 'altApprover',
          include: [
            { model: this.db.roleModel, attributes: ['name'], as: 'role' },
          ],
        },
        {
          model: this.db.userModel,
          attributes: ['id', 'firstName', 'lastName', 'username'],
          as: 'approver',
          include: [
            { model: this.db.roleModel, attributes: ['name'], as: 'role' },
          ],
        },
      ],
    };

    const canvasses = await this.tableName.findAll(queryOptions);

    return canvasses;
  }
}

module.exports = CanvassApproverRepository;
