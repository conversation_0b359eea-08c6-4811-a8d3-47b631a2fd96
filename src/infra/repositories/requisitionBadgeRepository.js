const BaseRepository = require('./baseRepository');

class RequisitionBadgeRepository extends BaseRepository {
  constructor ({ db }) {
    super(db.requisitionBadgeModel);
  }

  async bulkCreate({ requisitionIds, createdBy, model, modelId, transaction }) {
    return await this.tableName.bulkCreate(
      requisitionIds.map((id) => ({
        requisitionId: id,
        createdBy,
        model,
        modelId,
      })),
      { transaction },
    );
  }
}

module.exports = RequisitionBadgeRepository;
