const BaseRepository = require('./baseRepository');

class DeliveryReceiptInvoiceRepository extends BaseRepository {
  constructor({db, clientErrors }) {
    super(db.deliveryReceiptInvoiceModel);
    this.db = db;
    this.clientErrors = clientErrors;
  }

  async createInvoice(data, options = {}) {
    return await this.create(data, options);
  }

  async get(id) {
    return await this.tableName.findByPk(id);
  }

  async update(id, data) {
    return await this.tableName.update(data, {
      where: {
        id,
      },
    });
  }

  async delete(id) {
    return await this.tableName.destroy({
      where: {
        id,
      },
    });
  }
}

module.exports = DeliveryReceiptInvoiceRepository;
