const BaseRepository = require('./baseRepository');

class CompanyRepository extends BaseRepository {
  constructor({ db }) {
    super(db.companyModel);

    this.db = db;
    this.Sequelize = db.Sequelize;
  }

  async getByCompanyId(companyId) {
    return await this.getById(companyId);
  }

  async getAllCompanies(payload) {
    let whereClause = {};
    const {
      search,
      limit,
      page,
      paginate,
      filterBy,
      include = false,
      order = [['name', 'ASC']],
    } = payload;

    const { name } = filterBy;

    if (search) {
      whereClause = {
        name: { [this.Sequelize.Op.iLike]: `%${search}%` },
      };
    }

    if (name) {
      whereClause[this.Sequelize.Op.or] = [
        { name: { [this.Sequelize.Op.iLike]: `%${name}%` } },
        // { code: { [this.Sequelize.Op.iLike]: `%${name}%` } },
        // { initial: { [this.Sequelize.Op.iLike]: `%${name}%` } },
        // { areaCode: { [this.Sequelize.Op.iLike]: `%${name}%` } },
      ];
    }

    return await this.findAll({
      limit,
      page,
      order,
      include,
      paginate,
      where: whereClause,
    });
  }

  async syncCompanies(payload) {
    return await this.tableName.bulkCreate(payload, {
      updateOnDuplicate: ['tin', 'name', 'initial'],
    });
  }

  async findCompaniesByIds(companyIds, options = {}) {
    return this.findAll({
      where: {
        id: {
          [this.Sequelize.Op.in]: companyIds,
        },
      },
      ...options,
    });
  }
}

module.exports = CompanyRepository;
