require('dotenv').config();
const path = require('path');
const logStream = require('./logStream');

const serializers = {
  req: (req) => {
    return {
      method: req.method,
      url: req.url,
      token: req.headers.authorization,
      query: req.query,
    };
  },
};

let logSettings = {
  transport: {
    targets: [
      {
        target: 'pino-pretty',
        level: 'info',
        options: {
          colorize: true,
        },
      },
    ],
  },
  serializers
};

if (process.env.NODE_ENV !== 'local') {
  logSettings = {
    level: 'info',
    stream: logStream({ baseFileName: 'app.log' }),
    serializers,
  };
}

module.exports = logSettings;
