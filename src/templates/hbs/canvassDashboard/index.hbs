<html lang='en'>
  <head>
    <meta charset='UTF-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <link rel='preconnect' href='https://rsms.me/' />
    <link rel='stylesheet' href='https://rsms.me/inter/inter.css' />
    <title>Canvass Sheet</title>
    <style>
      @media print { .page, .page-break { break-after: page; } .section-break {
      break-inside: avoid; page-break-inside: avoid; } } @page { size: legal;
      margin-top: 1in; } body { font-family: 'Inter', sans-serif; font-size:
      12px; line-height: 1.2; color: #000; margin: 0; padding: 0; width: 8.5in;
      height: 11in; box-sizing: border-box; } @supports
      (font-variation-settings: normal) { body { font-family: 'Inter var',
      sans-serif; } } .wrapper { max-width: 7.5in; margin: auto auto; padding:
      0; padding-bottom: 40px; } .header { display: flex; justify-content:
      space-between; align-items: flex-start; margin-bottom: 10px; } .title {
      color: #8b4513; font-size: 20px; font-weight: bold; } .project-name {
      font-size: 22px; font-weight: bold; margin-top: 5px; } .cs-number {
      display: flex; justify-content: space-between;} .cs-number-left {
      font-size: 18px; font-weight: bold; } .cs-number-id { font-weight: normal;
      } .date-section { display: flex; justify-content: space-between; }
      .main-content { display: flex; gap: 20px; margin-top: 20px; } .left-panel
      { flex: 2; border: 1px solid #ccc; border-radius: 5px; padding: 15px; }
      .right-panel { flex: 1; border: 1px solid #ccc; border-radius: 5px;
      padding: 15px; } .field-group { margin-bottom: 15px; } .field-label {
      font-size: 10px; } .field-value { font-weight: bold; margin-top: 3px;
      font-size: 12px; } .field-row { display: flex; justify-content:
      space-between; margin-bottom: 15px; } .field-col { flex: 1; } .route-line
      { display: flex; margin-bottom: 10px; } .route-number { width: 20px;
      font-weight: bold; } .route-input { flex: 1; border-bottom: 1px solid
      #333; margin-left: 5px; } .table-container { border-radius: 8px;
      font-size: 7px; overflow: hidden; border: 1px solid #ccc; margin-top: 5px;
      margin-bottom: 15px; padding: 1px; } table { width: 100%; border-collapse:
      collapse; margin: 0; border: none; } th, td { border: 1px solid #ccc;
      padding: 4px; text-align: left; font-size: 10px; } tr:first-child th {
      border-top: none; } tr:last-child td { border-bottom: none; }
      th:first-child, td:first-child { border-left: none; } th:last-child,
      td:last-child { border-right: none; } th, td { padding: 4px; text-align:
      left; font-size: 10px; } th { color: #4f575e; vertical-align: top;
      text-align: left; font-weight: bold; font-size: 8px; } td { height: 30px;
      } .header-row { display: flex; justify-content: space-between; margin-top:
      20px; margin-bottom: 5px; } .pagination { text-align: right; } .qty {
      text-align: center; width: 10%; max-width: 10%; } .unit { text-align:
      center; } .unit-price-wrapper, .discount-wrapper { margin-top: 5px; }
      .unit-price-label, .discount-label { font-size: 9px; } .supplier-col {
      width: 50%; max-width: 50%; } .description-col { width: 11%; max-width:
      11%; } .unit-col { width: 8%; max-width: 8%; } .supplier-cell { max-width:
      14%; width: 14%;} .supplier-cell-supplier-name { display: -webkit-box;
      -webkit-line-clamp: 3; -webkit-box-orient: vertical; overflow: hidden;
      text-overflow: ellipsis; word-break: break-all; } .item-num { max-width: 5%; width: 5%; }
      .discounted-price { color: #333; font-weight: bold; } .text-bold {
      font-weight: bold; } .checkbox-group { display: flex; align-items: center;
      gap: 10px; } .checkbox-container { display: flex; align-items: center;
      gap: 5px; } .approver-table {padding-top: 30px; } 
      .requisition-notes-cell { color: firebrick; display: -webkit-box; -webkit-line-clamp: 6; -webkit-box-orient: vertical; overflow: hidden; text-overflow: ellipsis; word-break: break-all;}
    </style>
  </head>

  <body>
    <div class='wrapper'>
      {{#each pagesData as |pageData pageIndex|}}
        <div class='cs-number'>
          <div class='cs-number-left'>
            &nbsp;
          </div>

          {{#if @first}}
            <div class='date-section'>
              <div class='field-label' style='margin-right: 75px'>Date Prepared:
                <span class='text-bold'>
                  {{convertDateToDDMMMYYYY @root.datePrepared}}
                </span>
              </div>
              <div class='field-label' style='margin-right: 75px'>Date Received:</div>
            </div>
          {{/if}}
        </div>

        {{#if @first}}
          <div class='main-content'>
            <div class='left-panel'>
              <div class='field-group'>
                <div class='field-label'>Purpose:</div>
                <div class='field-value'>
                  {{@root.purpose}}
                </div>
              </div>

              <div class='field-row'>
                <div class='field-col'>
                  <div class='field-label'>Department:</div>
                  <div class='field-value'>{{@root.department}}</div>
                </div>
                <div class='field-col'>
                  <div class='field-label'>Date Needed:</div>
                  <div class='field-value'>{{@root.dateRequired}}</div>
                </div>
                <div class='field-col'>
                  <div class='field-label'>Charge To:</div>
                  <div class='field-value'>{{@root.chargeToDetails}}</div>
                </div>
              </div>

              <div class='field-row'>
                <div class='field-col'>
                  <div class='field-label'>Request By:</div>
                  <div class='field-value'>{{@root.createdByUser}}</div>
                </div>
                <div class='field-col'>
                  <div class='field-label'>Noted By:</div>
                  <div class='field-value'></div>
                </div>

                <div class='field-col'>
                  <span>&nbsp;</span>
                </div>
              </div>
            </div>

            <div class='right-panel'>
              <div class='field-label'>Please Route To:</div>
              <div style='margin-top: 10px'>
                {{#each
                  @root.canvassApprovers
                  as |canvassApprover approverIndex|
                }}
                  <div class='route-line'>
                    <div class='route-number'>{{inc approverIndex}}.</div>
                    <div
                      class='route-input'
                    >{{canvassApprover.approverName}}</div>
                  </div>
                {{/each}}

              </div>
            </div>
          </div>
        {{/if}}

        <div class='header-row'>
          <div class='text-bold'>Items</div>
          <div>
            <span class='field-value text-bold'>NOTE:</span>
            Please be specific in your request.
          </div>
          <div class='pagination'>
            <span class='text-bold'>{{@pageData.from}}
              -
              {{@pageData.to}}</span>
            of
            {{@root.totalItems}}
          </div>
        </div>

        <div class='page'>
          <div class='table-container'>
            <table>
              <thead>
                <tr>
                  <th class='item-num'>#</th>
                  <th class='qty'>Qty</th>
                  <th class='unit-col'>Unit</th>
                  <th class='description-col'>Description</th>
                  <th colspan='4' class='supplier-col'>Suppliers</th>
                  <th>Requisition Notes</th>
                </tr>
              </thead>
              <tbody>
                {{#each @pageData.data as |canvassItem|}}
                  <tr style='height: 120px;'>
                    <td class='item-num'>{{canvassItem.itemNum}}</td>
                    <td class='qty'>{{formatCurrency
                        canvassItem.quantity
                        3
                        'decimal'
                      }}</td>
                    <td class='unit'>{{canvassItem.unit}}</td>
                    <td>{{canvassItem.itemName}}</td>
                    {{#each canvassItem.suppliers as |supplier supplierIndex|}}
                      {{#if (lt supplierIndex 4)}}
                        <td class='supplier-cell'>
                          <div
                            class='supplier-cell-supplier-name'
                          >{{supplier.supplierName}}</div>
                          <div class='unit-price-wrapper'>
                            <div class='unit-price-label'>Unit Price:</div>
                            <div class='price'>{{formatCurrency
                                supplier.unitPrice
                                3
                                'currency'
                              }}</div>
                          </div>
                          <div class='discount-wrapper'>
                            <div class='discount-label'>Discounted Unit Price:</div>
                            <div class='discounted-price'>{{formatCurrency
                                supplier.discountedPrice
                                3
                                'currency'
                              }}</div>
                          </div>
                        </td>
                      {{/if}}
                    {{/each}}
                    {{#times (subtract 4 (length canvassItem.suppliers))}}
                      <td class='supplier-cell'></td>
                    {{/times}}
                    <td><div class='requisition-notes-cell'>{{canvassItem.note}}</div></td>
                  </tr>
                {{/each}}
              </tbody>
            </table>
          </div>

          {{#if @last}}
            <div class='wrapper section-break approver-table'>
              <div class='header' id='canvass-table'>
                <div>
                  <label for='dateReceived' class='field-label text-bold'>Date
                    Received:</label>
                  <span
                    type='date'
                    id='dateReceived'
                    name='dateReceived'
                    class='field-value'
                  ></span>
                </div>
                <div class='checkbox-group'>
                  <span class='label-value'>DR Attached?</span>
                  <div class='checkbox-container'>
                    <input
                      type='checkbox'
                      id='drYes'
                      name='drAttached'
                      value='yes'
                    />
                    <label for='drYes'>Yes</label>
                  </div>
                  <div class='checkbox-container'>
                    <input
                      type='checkbox'
                      id='drNo'
                      name='drAttached'
                      value='no'
                    />
                    <label for='drNo'>No</label>
                  </div>
                </div>
              </div>

              <div class='table-container'>
                <table>
                  <thead>
                    <tr>
                      <th rowspan='2' class='row-number-header'>#</th>
                      <th colspan='6' class='section-header'>Approved</th>
                      <th colspan='5' class='section-header'>FMSD Notation</th>
                    </tr>
                    <tr>
                      <th>Supplier</th>
                      <th>Unit Cost</th>
                      <th>GFQ</th>
                      <th>Ordered</th>
                      <th>This Request</th>
                      <th>Percent</th>
                      <th>Qty</th>
                      <th>Date</th>
                      <th>CV No.</th>
                      <th>Amount</th>
                      <th>By</th>
                    </tr>
                  </thead>
                  <tbody class='section-break'>
                    {{#each @root.canvassItems as |canvassItem|}}
                      <tr>
                        <td class='row-number'>{{@canvassItem.itemNum}}</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                      </tr>
                    {{/each}}
                  </tbody>
                </table>
              </div>
            </div>

          {{/if}}

        </div>
      {{/each}}
    </div>
  </body>
</html>