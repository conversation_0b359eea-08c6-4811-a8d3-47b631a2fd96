const { z } = require('zod');
const {
  stringFieldError,
  createIdParamsSchema,
  createNumberSchema,
} = require('../../app/utils');

/**
 * Force Close Notes Validation Schema
 * Requirements:
 * - Required field (cannot be empty)
 * - Maximum 500 characters
 * - Alphanumeric + special characters allowed
 * - No emojis allowed
 */
const forceCloseNotesSchema = z
  .string(stringFieldError('Force close notes'))
  .trim()
  .min(1, 'Force close notes are required')
  .max(500, 'Force close notes must not exceed 500 characters')
  .regex(
    /^[a-zA-Z0-9Ññ #&*()\-[\]:;',.?!@$%^+=_{}|\\~`"<>/\s]+$/,
    'Force close notes can only contain alphanumeric characters and specific special characters'
  )
  .refine(
    (val) => {
      // Check for emojis using comprehensive emoji regex
      const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F900}-\u{1F9FF}]|[\u{1FA70}-\u{1FAFF}]/u;
      return !emojiRegex.test(val);
    },
    {
      message: 'Force close notes cannot contain emojis',
    }
  );

/**
 * Requisition ID Parameter Schema
 * Used in route parameters for force close endpoints
 */
const forceCloseParams = z
  .object({
    requisitionId: createIdParamsSchema('Requisition ID'),
  })
  .strict();

/**
 * Force Close Request Body Schema
 * Used for POST /api/requisitions/{id}/force-close endpoint
 * Updated to match requirements document
 */
const forceCloseRequestSchema = z
  .object({
    notes: forceCloseNotesSchema,
    confirmedScenario: z.enum(['ACTIVE_PO_PARTIAL_DELIVERY', 'CLOSED_PO_REMAINING_QTY', 'CLOSED_PO_PENDING_CS']),
    acknowledgedImpacts: z.array(z.string()),
  })
  .strict();

/**
 * Force Close Validation Response Schema
 * Used for POST /api/requisitions/{id}/validate-force-close endpoint response
 * Updated to match requirements document
 */
const forceCloseValidationResponseSchema = z
  .object({
    eligible: z.boolean(),
    scenario: z.enum(['ACTIVE_PO_PARTIAL_DELIVERY', 'CLOSED_PO_REMAINING_QTY', 'CLOSED_PO_PENDING_CS']).optional(),
    validationPath: z.string(),
    requiresPaymentCheck: z.boolean(),
    pendingValidations: z.array(z.string()),
    impactSummary: z.record(z.any()),
  })
  .strict();

/**
 * Force Close Success Response Schema
 * Used for POST /api/requisitions/{id}/force-close endpoint response
 * Updated to match requirements document
 */
const forceCloseSuccessResponseSchema = z
  .object({
    success: z.boolean(),
    requisitionStatus: z.string(),
    documentsAffected: z.array(z.object({
      type: z.string(),
      id: z.number(),
      status: z.string(),
    })),
    quantitiesReturned: z.record(z.object({
      itemName: z.string(),
      returnedQty: z.number(),
      type: z.string(),
    })),
    poAdjustments: z.array(z.object({
      poId: z.number(),
      originalAmount: z.number(),
      newAmount: z.number(),
      originalQuantity: z.number(),
      newQuantity: z.number(),
      systemNotes: z.string(),
    })),
  })
  .strict();

/**
 * Force Close History Response Schema
 * Used for GET /api/requisitions/{id}/force-close-history endpoint response
 * Updated to match requirements document
 */
const forceCloseHistoryResponseSchema = z
  .object({
    forceCloseLog: z.object({
      id: z.number(),
      requisitionId: z.number(),
      userId: z.number(),
      scenarioType: z.string(),
      validationPath: z.string(),
      forceCloseNotes: z.string(),
      createdAt: z.string(),
    }),
    systemChanges: z.array(z.object({
      type: z.string(),
      description: z.string(),
      timestamp: z.string(),
    })),
    auditTrail: z.array(z.object({
      action: z.string(),
      details: z.record(z.any()),
      timestamp: z.string(),
    })),
    impactedDocuments: z.array(z.object({
      type: z.string(),
      id: z.number(),
      status: z.string(),
      changes: z.record(z.any()),
    })),
  })
  .strict();

/**
 * Force Close Error Response Schema
 * Used for error responses from force close endpoints
 */
const forceCloseErrorResponseSchema = z
  .object({
    message: z.string(),
    details: z.union([z.string(), z.record(z.any())]).optional(),
    requisitionId: createNumberSchema('Requisition ID').optional(),
  })
  .strict();

/**
 * Force Close Validation Result Schema
 * Internal schema for validation results
 */
const forceCloseValidationResultSchema = z
  .object({
    isEligible: z.boolean(),
    buttonVisible: z.boolean(),
    scenario: z
      .enum(['ACTIVE_PO_PARTIAL_DELIVERY', 'CLOSED_PO_REMAINING_QTY', 'CLOSED_PO_PENDING_CS'])
      .optional(),
    reason: z.string(),
    details: z.record(z.any()).optional(),
  })
  .strict();

/**
 * Force Close Scenario Details Schema
 * Used for scenario-specific validation and execution details
 */
const forceCloseScenarioDetailsSchema = z
  .object({
    type: z.string(),
    hasRemainingQty: z.boolean().optional(),
    hasPendingApproval: z.boolean().optional(),
    isPartiallyDelivered: z.boolean().optional(),
    hasDraftDocs: z.boolean().optional(),
    remainingQuantities: z.record(z.any()).optional(),
    pendingApprovals: z.array(z.record(z.any())).optional(),
    deliveryStatus: z.record(z.any()).optional(),
    draftDocuments: z.array(z.record(z.any())).optional(),
  })
  .strict();

/**
 * Force Close Authorization Details Schema
 * Used for user authorization validation results
 */
const forceCloseAuthorizationSchema = z
  .object({
    isAuthorized: z.boolean(),
    details: z.union([
      z.string(),
      z
        .object({
          isRequester: z.boolean(),
          isAssignedStaff: z.boolean(),
          requisitionCreatedBy: createNumberSchema('Created By User ID'),
          requisitionAssignedTo: createNumberSchema('Assigned To User ID').optional(),
          currentUserId: createNumberSchema('Current User ID'),
        })
        .strict(),
    ]),
  })
  .strict();

/**
 * Force Close Execution Result Schema
 * Used for force close execution results
 */
const forceCloseExecutionResultSchema = z
  .object({
    requisitionId: createNumberSchema('Requisition ID'),
    scenario: z.enum(['ACTIVE_PO_PARTIAL_DELIVERY', 'CLOSED_PO_REMAINING_QTY', 'CLOSED_PO_PENDING_CS']),
    documentsUpdated: z.array(z.object({
      type: z.string(),
      id: z.number(),
      status: z.string(),
    })),
    quantitiesReturned: z.record(z.any()),
    poAdjustments: z.array(z.any()),
  })
  .strict();

module.exports = {
  forceCloseNotesSchema,
  forceCloseParams,
  forceCloseRequestSchema,
  forceCloseValidationResponseSchema,
  forceCloseSuccessResponseSchema,
  forceCloseHistoryResponseSchema,
  forceCloseErrorResponseSchema,
  forceCloseValidationResultSchema,
  forceCloseScenarioDetailsSchema,
  forceCloseAuthorizationSchema,
  forceCloseExecutionResultSchema,
};
