const { z } = require('zod');
const { sort, area, filter } = require('../constants');
const { sortSchema } = require('./sortEntity');
const { filterSchema } = require('./filterEntity');
const {
  stringFieldError,
  createNumberSchema,
  createIdParamsSchema,
} = require('../../app/utils');

const companySortSchema = sortSchema(sort.COMPANY_SORT_COLUMNS);
const companyFilterSchema = filterSchema(filter.COMPANY_FILTER_COLUMNS);

const companyAddressSchema = z
  .string(stringFieldError('Company Address'))
  .max(100, 'Company Address must be at most 100 characters')
  .regex(
    /^[a-zA-Z0-9Ññ #&*()\-[\]:;',.?]+$/,
    "Company Address can only contain alphanumeric characters and specific special characters (#&*()-[]:;',.?)",
  )
  .trim();

const contactNumberSchema = z
  .string(stringFieldError('Contact Number'))
  .regex(/^\+639\d{9}$/, 'Must be a valid mobile number')
  .trim();

const createCompanySchema = z
  .object({
    code: createNumberSchema('Company Code').max(
      9999999999,
      'Company Code must not exceed 10 characters.',
    ),
    name: z
      .string(stringFieldError('Company Name'))
      .max(100, 'Company Name must be at most 50 characters'),
    initial: z
      .string(stringFieldError('Company Initial'))
      .max(20, 'Company Initial must be at most 20 characters'),
    tin: z
      .string(stringFieldError('Company TIN'))
      .max(20, 'Company TIN must be at most 20 characters'),
    address: companyAddressSchema,
    contactNumber: contactNumberSchema,
    areaCode: z.enum(Object.keys(area.ASSOCIATION_AREAS), {
      message: 'Invalid area code',
    }),
    selectedProjectIdTags: z.array(createNumberSchema('Project ID')),
    category: z.enum(['company', 'association'], {
      message: 'Category must be either "company" or "association"',
    }).default('company'),
  })
  .strict();

const updateCompanySchema = z.object({
  name: createCompanySchema.shape.name.optional(),
  initial: createCompanySchema.shape.initial.optional(),
  tin: createCompanySchema.shape.tin.optional(),
  address: createCompanySchema.shape.address.optional(),
  contactNumber: createCompanySchema.shape.contactNumber.optional(),
  areaCode: createCompanySchema.shape.areaCode.optional(),
  selectedProjectIdTags: z.array(createNumberSchema('Project ID')),
  category: z.enum(['company', 'association'], {
    message: 'Category must be either "company" or "association"',
  }).optional(),
});

const getCompanyParams = z
  .object({
    companyId: createIdParamsSchema('Company ID'),
  })
  .strict();

const addDepartmentSchema = z
  .object({
    companyId: createNumberSchema('Company ID'),
    departmentHeadId: createNumberSchema('Department Head ID'),
    secretaryId: createNumberSchema('Secretary ID'),
    assistantManagerId: createNumberSchema('Assistant Manager ID'),
    departmentId: createNumberSchema('Department ID'),
  })
  .strict();

module.exports = {
  companySortSchema,
  getCompanyParams,
  addDepartmentSchema,
  createCompanySchema,
  updateCompanySchema,
  companyFilterSchema,
};
