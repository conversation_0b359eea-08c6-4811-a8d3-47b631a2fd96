const { z } = require('zod');
const { sort, filter } = require('../constants');
const { sortSchema } = require('./sortEntity');
const { filterSchema } = require('./filterEntity');

const historySortSchema = sortSchema(sort.HISTORY_SORT_COLUMNS);
const historyFilterSchema = filterSchema(filter.HISTORY_FILTER_COLUMNS);
const purchaseHistorySortSchema = sortSchema(sort.PURCHASE_HISTORY_SORT_COLUMNS);

const getHistorySchema = z
  .object({
    search: z.string().optional(),
    sortBy: historySortSchema.optional(),
    page: z.string().regex(/^\d+$/).transform(Number).optional(),
    limit: z.string().regex(/^\d+$/).transform(Number).optional(),
    filterBy: historyFilterSchema.optional(),
    type: z.enum(['ofm', 'non-ofm']).optional(),
  })
  .strict();

const getPurchaseHistorySchema = z
  .object({
    search: z.string().optional(),
    sortBy: purchaseHistorySortSchema.optional(),
    page: z.string().regex(/^\d+$/).transform(Number).optional(),
    limit: z.string().regex(/^\d+$/).transform(Number).optional(),
    filterBy: historyFilterSchema.optional(),
  })
  .strict();

module.exports = {
  getHistorySchema,
  historySortSchema,
  historyFilterSchema,
  purchaseHistorySortSchema,
  getPurchaseHistorySchema,
};
