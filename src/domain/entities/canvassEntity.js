const { z } = require('zod');
const {
  stringFieldError,
  createIdParamsSchema,
  createNumberSchema,
  positiveIntegerSchema,
  positiveDecimalSchema,
  positiveIntegerSchemaV2,
  positiveDecimalSchemaV2,
} = require('../../app/utils');
const { SUPPLIER_TYPE } = require('../constants/canvassConstants');
const { sort, filter } = require('../constants');
const { sortSchema } = require('./sortEntity');
const { filterSchema } = require('./filterEntity');

const isSteelbars = z.preprocess(
  (value) => {
    return Boolean(value);
  },
  z
    .boolean({ invalid_type_error: 'Invalid item tagging for steelbars' })
    .default(false),
);

const discountValue = z.preprocess(
  (value) => {
    const parsed = parseFloat(value);
    if (isNaN(parsed)) return value;

    return Number(parsed.toFixed(2));
  },
  z
    .number({
      required_error: 'Discount Value is required',
      invalid_type_error: 'Discount Value must be a number',
    })
    .transform((val) => Number(val.toFixed(2))),
);

const getCanvassParams = z
  .object({
    id: createIdParamsSchema('Canvass ID'),
  })
  .strict();

const canvassSupplier = z
  .object({
    id: createIdParamsSchema('Canvass Supplier ID').optional(),
    isSteelbars,
    supplierId: createIdParamsSchema('Supplier ID'),
    supplierType: z
      .enum(Object.values(SUPPLIER_TYPE), {
        message: 'Invalid supplier type',
      })
      .optional()
      .default(SUPPLIER_TYPE.SUPPLIER),
    term: z.string(stringFieldError('Term')),
    quantity: positiveDecimalSchemaV2('Quantity', false, 3),
    order: positiveIntegerSchema('Order'),
    unitPrice: positiveDecimalSchema('Unit Price'),
    discountValue,
    isSelected: z
      .enum(['true', 'false'], {
        invalid_type_error: 'Invalid draft status',
      })
      .optional()
      .transform((value) => value === 'true'),
    discountType: z.enum(['percent', 'fixed'], {
      message: 'Discount type must be either percent or fixed',
    }),
    attachmentIds: z
      .array(createIdParamsSchema('Attachment ID'), {
        message: 'Invalid supplier attachment',
      })
      .optional()
      .default([]),
    notes: z
      .string(stringFieldError('Supplier note'))
      .trim()
      .max(100, 'Notes must not exceed 100 characters')
      .regex(
        /^[a-zA-Z0-9Ññ!@#$%^&*()_+\-=\\[\]{};':"\\|,.<>\/?`~ \n]+$/,
        'Notes can only contain letters, numbers, spaces and special characters',
      )
      .optional(),
  })
  .strict()
  .superRefine((val, ctx) => {
    if (!val.isSteelbars && val.discountValue < 0) {
      ctx.addIssue({
        path: ['discountValue'],
        code: z.ZodIssueCode.too_small,
        minimum: 0,
        type: 'number',
        inclusive: true,
        message: 'Discount Value must be positive unless tagged as steelbars',
        fatal: true,
      });

      return z.NEVER;
    }
  });

const addCanvasItemsSchema = z.object({
  requisitionItemListId: createIdParamsSchema('Requisition List ID'),
  suppliers: z
    .array(canvassSupplier, {
      invalid_type_error: 'Invalid canvass supplier data',
    })
    .optional(),
});

const updateItemsSchema = z.object({
  id: createIdParamsSchema('Canvass Item ID'),
  requisitionItemListId: createIdParamsSchema('Requisition List ID'),
  suppliers: z
    .array(canvassSupplier, {
      invalid_type_error: 'Invalid canvass supplier data',
    })
    .optional(),
});

const deleteItemsSchema = z.object({
  id: createIdParamsSchema('Requisition Item ID'),
});

const createCanvassSchema = z
  .object({
    id: createIdParamsSchema('Canvass Item ID').optional(),
    purchaseOrderIds: z
      .array(createIdParamsSchema('Purchase Order IDs'))
      .optional(),
    isDraft: z
      .enum(['true', 'false'], {
        required_error: 'Draft status is required',
        invalid_type_error: 'Invalid draft status',
      })
      .transform((value) => value === 'true'),
    requisitionId: createIdParamsSchema('Requisition ID'),
    addItems: z
      .array(addCanvasItemsSchema, {
        invalid_type_error:
          'Add canvass items must be an array of requisition items',
      })
      .optional(),
    updateItems: z
      .array(updateItemsSchema, {
        invalid_type_error:
          'Update canvass items must be an array of requisition items',
      })
      .optional(),
    deleteItems: z
      .array(deleteItemsSchema, {
        invalid_type_error: 'Invalid Canvass Item ID',
      })
      .optional(),
    notes: z
      .string(stringFieldError('Canvass note'))
      .trim()
      .max(100, 'Notes must not exceed 100 characters')
      .regex(
        /^[a-zA-Z0-9Ññ!@#$%^&*()_+\-=\\[\]{};':"\\|,.<>\/?`~ \n]+$/,
        'Notes can only contain letters, numbers, spaces and special characters',
      )
      .optional(),
  })
  .strict();

const addAdhocApproverSchema = z
  .object({
    approverId: createNumberSchema('Approver Id'),
  })
  .strict();

const supplierSelectionSchema = z.object({
  id: createNumberSchema('Canvass supplier ID is required'),
  isSelected: z.boolean({
    invalid_type_error: 'Invalid supplier selection type',
    required_error: 'Supplier select type is required',
  }),
});

const approveSelectedSuppliers = z
  .object({
    suppliers: z
      .array(supplierSelectionSchema, {
        invalid_type_error: 'Invalid supplier selections',
      })
      .optional(),
    approveReason: z
      .string(stringFieldError('Approve reason'))
      .max(100, { message: 'Reason must not exceed 100 characters' })
      .regex(
        /^[\p{L}\p{N}\p{P}\p{Z}]*$/gu,
        'Notes can only contain letters, numbers, spaces and special characters',
      )
      .optional(),
  })
  .strict();

const rejectCanvassSchema = z
  .object({
    rejectReason: z
      .string(stringFieldError('Reject reason'))
      .min(1, { message: 'Reason must be 1 or more characters long.' })
      .max(100, { message: 'Reason must not exceed 100 characters' })
      .regex(
        /^[\p{L}\p{N}\p{P}\p{Z}]*$/gu,
        'Notes can only contain letters, numbers, spaces and special characters',
      ),
  })
  .strict();

const addCommentSchema = z
  .object({
    notes: z
      .string(stringFieldError('Note'))
      .min(1, { message: 'Note must be 1 or more characters long.' })
      .max(100, { message: 'Note must not exceed 100 characters' }),
    userType: z.string(stringFieldError('User Type')).optional(),
  })
  .strict();

const canvassSupplierV2 = z
  .object({
    id: createNumberSchema('Canvass Supplier ID').optional(),
    isSteelbars,
    supplierId: createNumberSchema('Supplier ID'),
    supplierType: z
      .enum(Object.values(SUPPLIER_TYPE), {
        message: 'Invalid supplier type',
      })
      .optional()
      .default(SUPPLIER_TYPE.SUPPLIER),
    term: z.string(stringFieldError('Term')),
    quantity: positiveDecimalSchemaV2('Quantity', false, 3),
    order: positiveIntegerSchemaV2('Order'),
    unitPrice: positiveDecimalSchemaV2('Unit Price'),
    discountValue,
    isSelected: z
      .boolean({
        message: 'Invalid draft status',
      })
      .optional()
      .transform((value) => value === 'true'),
    discountType: z.enum(['percent', 'fixed'], {
      message: 'Discount type must be either percent or fixed',
    }),
    attachmentIds: z
      .array(createNumberSchema('Attachment ID'), {
        message: 'Invalid supplier attachment',
      })
      .optional()
      .default([]),
    notes: z
      .string(stringFieldError('Supplier note'))
      .trim()
      .max(100, 'Notes must not exceed 100 characters')
      .regex(
        /^[a-zA-Z0-9Ññ!@#$%^&*()_+\-=\\[\]{};':"\\|,.<>\/?`~ \n]+$/,
        'Notes can only contain letters, numbers, spaces and special characters',
      )
      .optional(),
  })
  .strict()
  .superRefine((val, ctx) => {
    if (!val.isSteelbars && val.discountValue < 0) {
      ctx.addIssue({
        path: ['discountValue'],
        code: z.ZodIssueCode.too_small,
        minimum: 0,
        type: 'number',
        inclusive: true,
        message: 'Discount Value must be positive unless tagged as steelbars',
        fatal: true,
      });

      return z.NEVER;
    }
  });

// FOR SUPPLIER UPDATE
const updateItemsSchemaV2 = z.object({
  id: createNumberSchema('Canvass Item ID'),
  requisitionItemListId: createNumberSchema('Requisition List ID'),
  suppliers: z
    .array(canvassSupplierV2, {
      invalid_type_error: 'Invalid canvass supplier data',
    })
    .optional(),
});

const canvassSortSchema = sortSchema(sort.CANVASS_SORT_COLUMNS);
const canvassFilterSchema = filterSchema(filter.CANVASS_FILTER_COLUMNS);

module.exports = {
  getCanvassParams,
  addCommentSchema,
  updateItemsSchemaV2,
  rejectCanvassSchema,
  createCanvassSchema,
  addAdhocApproverSchema,
  approveSelectedSuppliers,
  canvassSortSchema,
  canvassFilterSchema,
};
