const { z } = require('zod');
const { sort, filter } = require('../constants');
const { sortSchema } = require('./sortEntity');
const { filterSchema } = require('./filterEntity');
const { createIdParamsSchema } = require('../../app/utils');

const getItemsSchema = z.object({
  search: z.string().optional(),
  sort: z.enum(['asc', 'desc']).optional(),
  page: z.string().regex(/^\d+$/).transform(Number).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
});

const getOfmListItemsSchema = z
  .object({
    search: z.string().optional(),
    sort: z.enum(['asc', 'desc']).optional(),
    sortBy: sortSchema(sort.OFM_LIST_ITEM_SORT_COLUMNS).optional(),
    page: z.string().regex(/^\d+$/).transform(Number).optional(),
    filterBy: filterSchema(filter.ITEM_FILTER_COLUMNS).optional(),
    limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  })
  .strict();

const itemSortSchema = sortSchema(sort.ITEM_SORT_COLUMNS);
const itemFilterSchema = filterSchema(filter.ITEM_FILTER_COLUMNS);
const itemListsFilterSchema = filterSchema(filter.ITEM_LIST_FILTER_COLUMNS);
const ofmItemListSortSchema = sortSchema(sort.OFM_ITEM_LIST_SORT_COLUMNS);
const ofmListItemSortSchema = sortSchema(sort.OFM_LIST_ITEM_SORT_COLUMNS);

const getItemParams = z
  .object({
    id: createIdParamsSchema('Item Id'),
  })
  .strict();

const updateItemSchema = z.object({
  itemCd: z.string().max(20).optional(),
  itmDes: z.string().max(255).optional(), 
  unit: z.string().max(20).optional(),
  acctCd: z.string().max(20).optional(),
  gfq: z.number().optional(),
  tradeCode: z.number().optional(),
  remainingGfq: z.number().optional(),
  isSteelbars: z.boolean().optional()
}).strict();

module.exports = {
  getItemsSchema,
  itemSortSchema,
  getItemParams,
  itemListsFilterSchema,
  itemFilterSchema,
  ofmItemListSortSchema,
  updateItemSchema,
  ofmListItemSortSchema,
};
