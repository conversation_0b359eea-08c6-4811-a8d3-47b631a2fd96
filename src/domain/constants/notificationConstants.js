const NOTIFICATION_TYPES = {
  PASSWORD_RESET_REQUEST: 'password_reset_request',
  REQUISITION_SLIP: 'requisition_slip',
  CANVASS: 'canvass',
  PURCHASE_ORDER: 'purchase_order',
  PAYMENT_REQUEST: 'payment_request',
  NON_RS: 'non_requisition',
  PURCHASE_ORDER: 'purchase_order',
};

const NOTIFICATION_DETAILS = {
  SUPPLIER_SUSPENDED: {
    title: 'Attached Supplier for your Request has been tagged as Suspended',
    message: `A Supplier that was added for your Requisition Slip has been 
    tagged as Suspended. Click here to check if your Request has been affected`,
  },
  SUPPLIER_SYNC: {
    title: 'A Supplier for your Requisition Slip has been updated',
    message: `A Supplier that was added for your Requisition Slip has some 
    updates. Click here if you wish to update the Supplier.`,
  },
  COMPANY_SYNC: {
    title: 'A Company Details for your Requisition Slip has been updated',
    message: `The Company that was tagged for your Requisition Slip has some updates. Click here if you wish to update the Company.`,
  },
  ITEM_SYNC: {
    title: `An OFM Item Details of your Requisition Slip has been updated`,
    message: `An OFM Item that was included for your Requisition Slip has some updates. Click here if you wish to update the OFM Item.`,
  },
  APPROVE_NON_RS: {
    title: `Non-RS Payment Request Approved`,
    message: `Non-RS Payment Request has been Approved by one of the Approvers. Click here to proceed in reviewing the Non-RS Payment Request`,
  },
  REJECT_NON_RS: {
    title: `Non-RS Payment Request Rejected`,
    message: `Non-RS Payment Request has been Rejected by one of the Approvers. Click here to proceed in reviewing the Non-RS Payment Request`,
  },
  CANCEL_RS: {
    title: `Requisition Slip Cancelled`,
    message: (number) =>
      `Requisition Slip with a RS Number of ${number} has been Cancelled by the Requester. Click here or through Dashboard to review the Requisition Slip`,
  },
  ASSIGNING_RS: {
    title: `New Requisition Slip Assignment`,
    message: `A New Requisition Slip has been assigned to you. Click here or access the Dashboard to proceed in reviewing the Requisition Slip.`,
  },
  UPDATE_NON_RS: {
    title: `Non-RS Payment Request updated by an Approver`,
    message: (number) =>
      `NRS-${number} has been updated by one of the Approvers. Click here to view the updates on the Non-RS Payment Request.`,
  },
  ADDITIONAL_APPROVER_PR: {
    title: `Assigned as an Additional Approver`,
    message: (name) =>
      `${name} has added you to review the Payment Request and have it Approved. Click here or access the Dashboard to proceed in reviewing the Payment Request`,
  },
  PURCHASE_ORDER_ADDITIONAL_APPROVER: {
    title: `Assigned as an Additional Approver`,
    message: (name) =>
      `${name} has added you to review the Purchase Order and have it Approved. Click here or access the Dashboard to proceed in reviewing the Purchase Order.
`,
  },
  REJECT_PAYMENT_REQUEST: {
    title: 'Payment Request Rejected',
    message: (prNumber) =>
      `One of the Approvers has Rejected your Payment Request with a PR Number of ${prNumber}. Click here or view the Payment Request to review and resubmit`,
  },
  RESUBMIT_PAYMENT_REQUEST: {
    title: 'Payment Request Resubmitted',
    message: (prNumber) =>
      `Payment Request ${prNumber} has been resubmitted and requires your approval.`,
  },
  APPROVER: {
    title: `Approval by the Next Approver`,
    message: (model) =>
      `A ${model} with your Pending Approval, has been Approved or Rejected by the next ${model === 'Canvass Sheet' ? `Purchase Head` : `Approver`} of the ${model}. Click here to view the Document`,
  },
  RESUBMIT_APPROVER: {
    title: (model) => `${model} has been resubmitted and for review`,
    message: (model) =>
      `Requester has resubmitted the ${model} that you have Rejected. Click here or access the Dashboard to proceed in reviewing the ${model}.`,
  },
};

module.exports = {
  NOTIFICATION_TYPES,
  NOTIFICATION_DETAILS,
};
