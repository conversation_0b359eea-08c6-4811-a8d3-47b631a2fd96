const CANVASS_STATUS = Object.freeze({
  DRAFT: 'draft',
  PARTIAL: 'partially_canvassed', // cascade to RS
  FOR_APPROVAL: 'for_approval', // cascade to RS
  REJECTED: 'rejected',
  APPROVED: 'approved',
});

const CANVASS_APPROVER_STATUS = Object.freeze({
  PENDING: 'pending',
  REJECTED: 'rejected',
  APPROVED: 'approved',
});

const CANVASS_ITEM_STATUS = Object.freeze({
  NEW: 'new',
  FOR_APPROVAL: 'for_approval',
  FOR_SUBMISSION: 'for_submission',
  CANCELLED: 'cancelled',
  APPROVED: 'approved',
});

const DISCOUNT_TYPE = Object.freeze({
  PERCENT: 'percent',
  FIXED: 'fixed',
});

const SUPPLIER_TYPE = Object.freeze({
  SUPPLIER: 'supplier',
  PROJECT: 'project',
  COMPANY: 'company',
});

const CANVASS_TYPE = Object.freeze({
  OFM: 'ofm',
  OFM_TOM: 'ofm-tom',
  NON_OFM: 'non-ofm',
  NON_OFM_TOM: 'non-ofm-tom',
});

module.exports = {
  CANVASS_TYPE,
  SUPPLIER_TYPE,
  DISCOUNT_TYPE,
  CANVASS_STATUS,
  CANVASS_ITEM_STATUS,
  CANVASS_APPROVER_STATUS,
};
