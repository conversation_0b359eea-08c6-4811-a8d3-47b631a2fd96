const CHARGE_TO_LIST = ['supplier', 'project', 'association', 'company'];

const REQUISITION_STATUS = Object.freeze({
  DRAFT: 'rs_draft',
  SUBMITTED: 'for_rs_approval',
  APPROVED: 'approved',
  ASSIGNED: 'assigned',
  ASSIGNING: 'assigning',
  CANVASS_FOR_APPROVAL: 'canvass_for_approval',
  FOR_DELIVERY: 'for_delivery',
  FOR_PO_REVIEW: 'for_po_review',
  FOR_PR_APPROVAL: 'for_pr_approval',
  PARTIALLY_CANVASSED: 'partially_canvassed',
  REJECTED: 'rs_rejected',
  CLOSED: 'rs_closed',
  RS_IN_PROGRESS: 'rs_in_progress',
});

const REQUISITION_CATEGORIES = [
  'association',
  'company',
  'project',
];

const REQUISITION_REQUEST_TYPES = [
  'ofm',
  'ofm-tom',
  'non-ofm',
  'non-ofm-tom',
];

const REQUISITION_CHARGE_TO = [
  'supplier',
  'project',
  'association',
  'company',
];

const GFQ_PERCENTAGE_FOR_OPTIONAL_APPROVERS = 0.8;

module.exports = {
  CHARGE_TO_LIST,
  REQUISITION_STATUS,
  REQUISITION_CATEGORIES,
  REQUISITION_REQUEST_TYPES,
  REQUISITION_CHARGE_TO,
  GFQ_PERCENTAGE_FOR_OPTIONAL_APPROVERS,
};
