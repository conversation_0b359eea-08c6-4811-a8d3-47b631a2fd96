async function requestHistoryRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const requestHistoryController = fastify.diScope.resolve('requestHistoryController');  

  // GET /v1/request-history/:id
  fastify.route({
    method: 'GET',
    url: '/:id/:type',
    schema: {
      params: entities.requestHistory.getRequestHistoryParams,
    },
    handler: requestHistoryController.getRequestHistoryById.bind(requestHistoryController),
  });
}

module.exports = requestHistoryRoutes;