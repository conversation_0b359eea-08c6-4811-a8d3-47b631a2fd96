async function rsPaymentRequestRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const gatePassController = fastify.diScope.resolve('gatePassController');
  const requisitionController = fastify.diScope.resolve(
    'requisitionController',
  );
  const purchaseOrderController = fastify.diScope.resolve(
    'purchaseOrderController',
  );
  const { PERMISSIONS } = fastify.diScope.resolve('constants').permission;
  const rsPaymentRequestController = fastify.diScope.resolve(
    'rsPaymentRequestController',
  );

  /* -----------------------GET---------------------------- */

  fastify.route({
    method: 'GET',
    url: '/:id',
    schema: {
      params: entities.rsPaymentRequest.paymentRequestIdParams,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.VIEW_PAYMENTS)]),
    handler: rsPaymentRequestController.getPaymentRequest.bind(
      rsPaymentRequestController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/po-lists',
    schema: {
      query:
        entities.rsPaymentRequest.getPaymentRequestsFromRequisitionParamsSchema,
    },
    handler: rsPaymentRequestController.getPOLists.bind(
      rsPaymentRequestController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:id/comments',
    schema: {
      params: entities.rsPaymentRequest.paymentRequestIdParams,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.VIEW_PAYMENTS)]),
    handler: rsPaymentRequestController.getPRComments.bind(
      rsPaymentRequestController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:id/attachments',
    schema: {
      params: entities.rsPaymentRequest.paymentRequestIdParams,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.VIEW_PAYMENTS)]),
    handler: rsPaymentRequestController.getPRAttachments.bind(
      rsPaymentRequestController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:id/approvers',
    schema: {
      params: entities.rsPaymentRequest.paymentRequestIdParams,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.VIEW_PAYMENTS)]),
    handler: rsPaymentRequestController.getPRApprovers.bind(
      rsPaymentRequestController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/po-details/:purchaseOrderId',
    schema: {
      params: entities.rsPaymentRequest.purchaseOrderIdParams,
    },
    handler: rsPaymentRequestController.getPurchaseOrderDetails.bind(
      rsPaymentRequestController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/purchase-order/:id/items',
    schema: {
      params: entities.rsPaymentRequest.paymentRequestIdParams,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.VIEW_PAYMENTS)]),
    handler: rsPaymentRequestController.getPRItems.bind(
      rsPaymentRequestController,
    ),
  });

  fastify.register((instance, _opts, done) => {
    instance.useTransaction();

    /* -----------------------POST---------------------------- */

    // POST /v1/rs-payment-request
    instance.route({
      method: 'POST',
      preHandler: [fastify.uploadFile],
      url: '/',
      handler: rsPaymentRequestController.createRsPaymentRequest.bind(
        rsPaymentRequestController,
      ),
    });

    // POST /v1/rs-payment-request/:id/comment
    instance.route({
      method: 'POST',
      url: '/:id/comment',
      schema: {
        params: entities.rsPaymentRequest.paymentRequestIdParams,
        body: entities.rsPaymentRequest.addCommentSchema,
      },
      handler: rsPaymentRequestController.addPRComment.bind(
        rsPaymentRequestController,
      ),
    });

    // POST /v1/rs-payment-request/:id/approve
    instance.route({
      method: 'POST',
      url: '/:id/approve',
      schema: {
        params: entities.rsPaymentRequest.paymentRequestIdParams,
        body: entities.note.approveReasonSchema,
      },
      handler: rsPaymentRequestController.approvePurchaseRequest.bind(
        rsPaymentRequestController,
      ),
      onResponse: async (req) =>
        await purchaseOrderController.closePurchaseOrder(req),
    });

    // POST /v1/rs-payment-request/:id/generate-gatepass
    instance.route({
      method: 'POST',
      url: '/:id/generate-gatepass',
      schema: {
        params: entities.rsPaymentRequest.paymentRequestIdParams,
      },
      handler: gatePassController.generateGatePass.bind(gatePassController),
    });

    // POST /v1/rs-payment-request/:id/add-adhoc-approver
    instance.route({
      method: 'POST',
      url: '/:id/add-adhoc-approver',
      schema: {
        params: entities.rsPaymentRequest.paymentRequestIdParams,
        body: entities.rsPaymentRequest.addAdhocApproverSchema,
      },
      handler: rsPaymentRequestController.addAdhocApprover.bind(
        rsPaymentRequestController,
      ),
    });

    // POST /v1/rs-payment-request/:id/reject
    instance.route({
      method: 'POST',
      url: '/:id/reject',
      schema: {
        params: entities.rsPaymentRequest.paymentRequestIdParams,
        body: entities.rsPaymentRequest.rejectPRSchema,
      },
      handler: rsPaymentRequestController.rejectPaymentRequest.bind(
        rsPaymentRequestController,
      ),
    });

    // POST /v1/rs-payment-request/:id/resubmit
    instance.route({
      method: 'POST',
      url: '/:id/resubmit',
      preHandler: [fastify.uploadFile],
      schema: {
        params: entities.rsPaymentRequest.paymentRequestIdParams,
      },
      handler: rsPaymentRequestController.resubmitRejectedPaymentRequest.bind(
        rsPaymentRequestController,
      ),
    });

    /* -----------------------PUT---------------------------- */

    // PUT /v1/rs-payment-request/submit
    instance.route({
      method: 'PUT',
      preHandler: [fastify.uploadFile],
      url: '/submit',
      handler: rsPaymentRequestController.submitPaymentRequest.bind(
        rsPaymentRequestController,
      ),
    });

    /* -----------------------DELETE---------------------------- */

    // DELETE /v1/rs-payment-request/:id/remove-adhoc-approver
    instance.route({
      method: 'DELETE',
      url: '/:id/remove-adhoc-approver',
      schema: {
        params: entities.rsPaymentRequest.paymentRequestIdParams,
      },
      handler: rsPaymentRequestController.removeAdhocApprover.bind(
        rsPaymentRequestController,
      ),
    });

    done();
  });

  fastify.route({
    method: 'GET',
    url: '/:id/invoice-reports',
    schema: {
      params: entities.rsPaymentRequest.paymentRequestIdParams,
      query:
        entities.invoiceReport.getInvoiceReportsByPaymentRequestQuerySchema,
    },
    handler: rsPaymentRequestController.getInvoicesByPaymentRequestId.bind(
      rsPaymentRequestController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/monitoring/:purchaseOrderId',
    schema: {
      params: entities.rsPaymentRequest.purchaseOrderIdParams,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.VIEW_PAYMENTS)]),
    handler: rsPaymentRequestController.getPaymentRequestMonitoring.bind(
      rsPaymentRequestController,
    ),
  });
}

module.exports = rsPaymentRequestRoutes;
