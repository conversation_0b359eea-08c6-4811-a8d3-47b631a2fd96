async function nonRequisitionRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const { PERMISSIONS } = fastify.diScope.resolve('constants').permission;
  const nonRequisitionController = fastify.diScope.resolve(
    'nonRequisitionController',
  );

  fastify.route({
    method: 'POST',
    url: '/',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.CREATE_NON_RS_PAYMENTS),
    ]),
    schema: {
      body: entities.nonRequisition.createNonRsSchema,
    },
    handler: nonRequisitionController.createNonRS.bind(
      nonRequisitionController,
    ),
  });

  fastify.route({
    method: 'DELETE',
    url: '/:id/cancel',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.DELETE_NON_RS_PAYMENTS),
    ]),
    schema: {
      params: entities.nonRequisition.nonRsParams,
    },
    handler: nonRequisitionController.cancelNonRS.bind(
      nonRequisitionController,
    ),
  });

  fastify.route({
    method: 'POST',
    url: '/:id/approve',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.APPROVAL_NON_RS_PAYMENTS),
    ]),
    schema: {
      params: entities.nonRequisition.nonRsParams,
      body: entities.nonRequisition.approveNonRsSchema,
    },
    handler: nonRequisitionController.approveNonRs.bind(
      nonRequisitionController,
    ),
  });

  fastify.route({
    method: 'POST',
    url: '/:id/reject',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.APPROVAL_NON_RS_PAYMENTS),
    ]),
    schema: {
      params: entities.nonRequisition.nonRsParams,
      body: entities.nonRequisition.rejectNonRsSchema,
    },
    handler: nonRequisitionController.rejectNonRs.bind(
      nonRequisitionController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.VIEW_NON_RS_PAYMENTS),
    ]),
    handler: nonRequisitionController.getAllNonRs.bind(
      nonRequisitionController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:id',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.VIEW_NON_RS_PAYMENTS),
    ]),
    schema: {
      params: entities.nonRequisition.nonRsParams,
    },
    handler: nonRequisitionController.getNonRSDetails.bind(
      nonRequisitionController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:id/history',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.GET_NON_RS_PAYMENTS),
    ]),
    schema: {
      params: entities.nonRequisition.nonRsParams,
    },
    handler: nonRequisitionController.getAllNonRSHistoryById.bind(
      nonRequisitionController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:id/item-list',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.VIEW_NON_RS_PAYMENTS),
    ]),
    schema: {
      params: entities.nonRequisition.nonRsParams,
    },
    handler: nonRequisitionController.getNonRSItemList.bind(
      nonRequisitionController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:id/approvers',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.VIEW_NON_RS_PAYMENTS),
    ]),
    schema: {
      params: entities.nonRequisition.nonRsParams,
    },
    handler: nonRequisitionController.getNonRSApprovers.bind(
      nonRequisitionController,
    ),
  });

  fastify.route({
    method: 'DELETE',
    url: '/:id/remove-adhoc-approver',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.APPROVAL_NON_RS_PAYMENTS),
    ]),
    schema: {
      params: entities.nonRequisition.nonRsParams,
    },
    handler: nonRequisitionController.removeAdhocApprover.bind(
      nonRequisitionController,
    ),
  });

  fastify.route({
    method: 'POST',
    url: '/:id/add-adhoc-approver',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.APPROVAL_NON_RS_PAYMENTS),
    ]),
    schema: {
      params: entities.nonRequisition.nonRsParams,
      body: entities.nonRequisition.addAdhocApproverSchema,
    },
    handler: nonRequisitionController.addAdhocApprover.bind(
      nonRequisitionController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/units',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.APPROVAL_NON_RS_PAYMENTS),
    ]),
    handler: nonRequisitionController.getNonRSItemUnits.bind(
      nonRequisitionController,
    ),
  });
}

module.exports = nonRequisitionRoutes;
