async function forceCloseRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const forceCloseController = fastify.diScope.resolve('forceCloseController');
  const { PERMISSIONS } = fastify.diScope.resolve('constants').permission;

  // Validate force close eligibility and determine scenario
  fastify.route({
    method: 'GET',
    url: '/:requisitionId/validate-force-close',
    schema: {
      params: entities.forceClose.forceCloseParams,
    },
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.GET_DASHBOARD),
    ]),
    handler: forceCloseController.checkForceCloseEligibility.bind(forceCloseController),
  });

  // Execute force close operation
  fastify.route({
    method: 'POST',
    url: '/:requisitionId/force-close',
    schema: {
      params: entities.forceClose.forceCloseParams,
      body: entities.forceClose.forceCloseRequestSchema,
    },
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.UPDATE_DASHBOARD),
    ]),
    handler: forceCloseController.forceCloseRequisition.bind(forceCloseController),
  });

  // Get force close history for a requisition
  fastify.route({
    method: 'GET',
    url: '/:requisitionId/force-close-history',
    schema: {
      params: entities.forceClose.forceCloseParams,
    },
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.GET_DASHBOARD),
    ]),
    handler: forceCloseController.getForceCloseHistory.bind(forceCloseController),
  });
}

module.exports = forceCloseRoutes;
