async function notificationRoutes(fastify) {
  const notificationController = fastify.diScope.resolve(
    'notificationController',
  );

  /* User specific notifications - no permission needed */
  fastify.route({
    method: 'GET',
    url: '/',
    handler: notificationController.getNotifications.bind(
      notificationController,
    ),
  });

  // seen notification
  fastify.route({
    method: 'PUT',
    url: '/:id/seen',
    handler: notificationController.seenNotification.bind(notificationController),
  });
}

module.exports = notificationRoutes;
