async function steelbarsRoutes(fastify) {
    const entities = fastify.diScope.resolve('entities');
    const steelbarsController = fastify.diScope.resolve('steelbarsController');
    const { PERMISSIONS } = fastify.diScope.resolve('constants').permission;
  
    fastify.route({
        method: 'GET',
        url: '/',
        schema: {
          query: entities.steelbars.getSteelbarsSchema,
        },
        // preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_OFM_ITEMS)]),
        handler: steelbarsController.getSteelbars.bind(steelbarsController),
      });

      fastify.route({
        method: 'PUT',
        url: '/update-ofm-acctcd',
        schema: {
          body: entities.steelbars.updateOfmAcctCdSchema,
        },
        // preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_STEELBARS)]),
        handler: steelbarsController.updateOfmAcctCd.bind(steelbarsController),
      });

      fastify.route({
        method: 'POST',
        url: '/',
        schema: {
          body: entities.steelbars.createSteelbarSchema,
        },
        // preHandler: fastify.auth([fastify.authorize(PERMISSIONS.CREATE_STEELBARS)]),
        handler: steelbarsController.createSteelbar.bind(steelbarsController),
      });

  }
  
  module.exports = steelbarsRoutes;
  