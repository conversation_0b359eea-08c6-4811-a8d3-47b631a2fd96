const chai = require('chai');
const sinonChai = require('sinon-chai');
const sinon = require('sinon');

chai.use(sinonChai);

const { expect } = chai;
const { restore, stub } = sinon;

describe('Force Close Database Schema Migration', () => {
  let mockQueryInterface;
  let mockSequelize;
  let migration;

  beforeEach(() => {
    // Mock Sequelize types
    mockSequelize = {
      DATE: 'DATE',
      INTEGER: 'INTEGER',
      STRING: 'STRING',
      TEXT: 'TEXT',
      ENUM: 'ENUM',
    };

    // Mock QueryInterface
    mockQueryInterface = {
      addColumn: stub().resolves(),
      removeColumn: stub().resolves(),
      createTable: stub().resolves(),
      dropTable: stub().resolves(),
      addIndex: stub().resolves(),
      removeIndex: stub().resolves(),
    };

    // Import migration
    migration = require('../../../../../../src/infra/database/migrations/20250610120000-add-force-close-schema');
  });

  afterEach(() => {
    restore();
  });

  describe('Migration Up', () => {
    it('should add force close fields to requisitions table', async () => {
      await migration.up(mockQueryInterface, mockSequelize);

      // Verify requisitions table columns
      expect(mockQueryInterface.addColumn).to.have.been.calledWith('requisitions', 'force_closed_at');
      expect(mockQueryInterface.addColumn).to.have.been.calledWith('requisitions', 'force_closed_by');
      expect(mockQueryInterface.addColumn).to.have.been.calledWith('requisitions', 'force_close_reason');
      expect(mockQueryInterface.addColumn).to.have.been.calledWith('requisitions', 'force_close_scenario');
    });

    it('should add PO tracking fields to purchase_orders table', async () => {
      await migration.up(mockQueryInterface, mockSequelize);

      // Verify purchase_orders table columns
      expect(mockQueryInterface.addColumn).to.have.been.calledWith('purchase_orders', 'system_generated_notes');
      expect(mockQueryInterface.addColumn).to.have.been.calledWith('purchase_orders', 'original_amount');
      expect(mockQueryInterface.addColumn).to.have.been.calledWith('purchase_orders', 'original_quantity');
    });

    it('should add cancellation fields to all document tables', async () => {
      await migration.up(mockQueryInterface, mockSequelize);

      // Verify cancellation fields for all document types
      const documentTables = [
        'canvass_requisitions',
        'invoice_reports', 
        'delivery_receipts',
        'rs_payment_requests'
      ];

      for (const table of documentTables) {
        expect(mockQueryInterface.addColumn).to.have.been.calledWith(table, 'cancelled_at');
        expect(mockQueryInterface.addColumn).to.have.been.calledWith(table, 'cancelled_by');
        expect(mockQueryInterface.addColumn).to.have.been.calledWith(table, 'cancellation_reason');
      }
    });

    it('should create force_close_logs audit table', async () => {
      await migration.up(mockQueryInterface, mockSequelize);

      expect(mockQueryInterface.createTable).to.have.been.calledWith('force_close_logs', sinon.match({
        id: sinon.match.object,
        requisition_id: sinon.match.object,
        user_id: sinon.match.object,
        scenario_type: sinon.match.object,
        validation_path: sinon.match.object,
        force_close_notes: sinon.match.object,
        system_changes: sinon.match.object,
        created_at: sinon.match.object,
        updated_at: sinon.match.object,
      }));
    });

    it('should add performance indices', async () => {
      await migration.up(mockQueryInterface, mockSequelize);

      // Verify key indices are created
      expect(mockQueryInterface.addIndex).to.have.been.calledWith('requisitions', ['force_closed_at']);
      expect(mockQueryInterface.addIndex).to.have.been.calledWith('requisitions', ['force_closed_by']);
      expect(mockQueryInterface.addIndex).to.have.been.calledWith('force_close_logs', ['requisition_id']);
      expect(mockQueryInterface.addIndex).to.have.been.calledWith('invoice_reports', ['cancelled_at']);
      expect(mockQueryInterface.addIndex).to.have.been.calledWith('delivery_receipts', ['cancelled_at']);
      expect(mockQueryInterface.addIndex).to.have.been.calledWith('rs_payment_requests', ['cancelled_at']);
    });

    it('should handle migration errors gracefully', async () => {
      mockQueryInterface.addColumn.rejects(new Error('Database error'));

      try {
        await migration.up(mockQueryInterface, mockSequelize);
        expect.fail('Should have thrown error');
      } catch (error) {
        expect(error.message).to.include('Database error');
      }
    });
  });

  describe('Migration Down (Rollback)', () => {
    it('should remove all added columns in reverse order', async () => {
      await migration.down(mockQueryInterface, mockSequelize);

      // Verify columns are removed
      expect(mockQueryInterface.removeColumn).to.have.been.calledWith('requisitions', 'force_close_scenario');
      expect(mockQueryInterface.removeColumn).to.have.been.calledWith('requisitions', 'force_close_reason');
      expect(mockQueryInterface.removeColumn).to.have.been.calledWith('requisitions', 'force_closed_by');
      expect(mockQueryInterface.removeColumn).to.have.been.calledWith('requisitions', 'force_closed_at');
    });

    it('should remove cancellation fields from all document tables', async () => {
      await migration.down(mockQueryInterface, mockSequelize);

      const documentTables = [
        'rs_payment_requests',
        'delivery_receipts', 
        'invoice_reports',
        'canvass_requisitions'
      ];

      for (const table of documentTables) {
        expect(mockQueryInterface.removeColumn).to.have.been.calledWith(table, 'cancellation_reason');
        expect(mockQueryInterface.removeColumn).to.have.been.calledWith(table, 'cancelled_by');
        expect(mockQueryInterface.removeColumn).to.have.been.calledWith(table, 'cancelled_at');
      }
    });

    it('should drop force_close_logs table', async () => {
      await migration.down(mockQueryInterface, mockSequelize);

      expect(mockQueryInterface.dropTable).to.have.been.calledWith('force_close_logs');
    });

    it('should remove all indices', async () => {
      await migration.down(mockQueryInterface, mockSequelize);

      // Verify indices are removed
      expect(mockQueryInterface.removeIndex).to.have.been.calledWith('force_close_logs', 'idx_force_close_logs_requisition_id');
      expect(mockQueryInterface.removeIndex).to.have.been.calledWith('requisitions', 'idx_requisitions_force_closed_at');
      expect(mockQueryInterface.removeIndex).to.have.been.calledWith('invoice_reports', 'idx_invoice_reports_cancelled_at');
    });
  });

  describe('Schema Validation', () => {
    it('should have proper foreign key constraints', async () => {
      await migration.up(mockQueryInterface, mockSequelize);

      // Check that foreign key columns reference correct tables
      const forceClosedByCall = mockQueryInterface.addColumn.getCalls().find(
        call => call.args[1] === 'force_closed_by'
      );
      
      expect(forceClosedByCall.args[2].references).to.deep.equal({
        model: 'users',
        key: 'id',
      });
    });

    it('should have appropriate field lengths and constraints', async () => {
      await migration.up(mockQueryInterface, mockSequelize);

      // Check force_close_reason length constraint
      const reasonCall = mockQueryInterface.addColumn.getCalls().find(
        call => call.args[1] === 'force_close_reason'
      );
      
      expect(reasonCall.args[2].type).to.include('500'); // VARCHAR(500)
    });

    it('should have proper enum values for scenario', async () => {
      await migration.up(mockQueryInterface, mockSequelize);

      const scenarioCall = mockQueryInterface.addColumn.getCalls().find(
        call => call.args[1] === 'force_close_scenario'
      );
      
      expect(scenarioCall.args[2].type).to.include('ACTIVE_PO_PARTIAL_DELIVERY');
      expect(scenarioCall.args[2].type).to.include('CLOSED_PO_REMAINING_QTY');
      expect(scenarioCall.args[2].type).to.include('CLOSED_PO_PENDING_CS');
    });
  });

  describe('Data Integrity', () => {
    it('should allow null values for optional fields', async () => {
      await migration.up(mockQueryInterface, mockSequelize);

      const nullableFields = [
        'force_closed_at',
        'force_closed_by', 
        'force_close_reason',
        'force_close_scenario',
        'cancelled_at',
        'cancelled_by',
        'cancellation_reason'
      ];

      for (const field of nullableFields) {
        const fieldCall = mockQueryInterface.addColumn.getCalls().find(
          call => call.args[1] === field
        );
        
        if (fieldCall) {
          expect(fieldCall.args[2].allowNull).to.be.true;
        }
      }
    });

    it('should maintain referential integrity', async () => {
      await migration.up(mockQueryInterface, mockSequelize);

      // Verify foreign key fields have proper constraints
      const foreignKeyFields = ['force_closed_by', 'cancelled_by'];
      
      for (const field of foreignKeyFields) {
        const calls = mockQueryInterface.addColumn.getCalls().filter(
          call => call.args[1] === field
        );
        
        for (const call of calls) {
          expect(call.args[2].references).to.exist;
          expect(call.args[2].references.model).to.equal('users');
          expect(call.args[2].references.key).to.equal('id');
        }
      }
    });
  });
});
