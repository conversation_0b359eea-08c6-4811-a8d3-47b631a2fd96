const chai = require('chai');
const sinonChai = require('sinon-chai');
const sinon = require('sinon');

chai.use(sinonChai);

const { expect } = chai;
const { restore, stub, createStubInstance } = sinon;

describe('ForceCloseController', () => {
  let forceCloseController;
  let mockContainer;
  let mockFastify;
  let mockForceCloseService;
  let mockRequest;
  let mockReply;

  beforeEach(() => {
    // Mock fastify logger
    mockFastify = {
      log: {
        info: stub(),
        warn: stub(),
        error: stub(),
      },
    };

    // Mock force close service
    mockForceCloseService = {
      validateForceCloseEligibility: stub(),
      executeForceClose: stub(),
      getForceCloseHistory: stub(),
    };

    // Mock entities
    const mockEntities = {
      forceClose: {
        validateForceCloseRequest: stub(),
        validateForceCloseResponse: stub(),
      },
    };

    // Mock container
    mockContainer = {
      fastify: mockFastify,
      forceCloseService: mockForceCloseService,
      entities: mockEntities,
    };

    // Mock request and reply
    mockRequest = {
      params: { requisitionId: '123' },
      body: { notes: 'Test force close notes' },
      userFromToken: { id: 1, username: 'testuser' },
    };

    mockReply = {
      code: stub().returnsThis(),
      send: stub(),
    };

    // Import and instantiate ForceCloseController
    const ForceCloseController = require('../../../../../../src/app/handlers/controllers/forceCloseController');
    forceCloseController = new ForceCloseController(mockContainer);
  });

  afterEach(() => {
    restore();
  });

  describe('Constructor', () => {
    it('should initialize with container dependencies', () => {
      expect(forceCloseController.fastify).to.equal(mockFastify);
      expect(forceCloseController.forceCloseService).to.equal(mockForceCloseService);
    });
  });

  describe('validateForceClose', () => {
    it('should return successful validation result', async () => {
      const mockValidationResult = {
        isEligible: true,
        scenario: 'ACTIVE_PO_PARTIAL_DELIVERY',
        details: { type: 'Active PO with partial delivery' },
        buttonVisible: true,
      };

      mockForceCloseService.validateForceCloseEligibility.resolves(mockValidationResult);

      await forceCloseController.validateForceClose(mockRequest, mockReply);

      expect(mockForceCloseService.validateForceCloseEligibility).to.have.been.calledWith({
        requisitionId: 123,
        userFromToken: mockRequest.userFromToken,
        notes: mockRequest.body.notes,
        checkOnly: true,
      });

      expect(mockReply.code).to.have.been.calledWith(200);
      expect(mockReply.send).to.have.been.calledWith({
        success: true,
        data: mockValidationResult,
      });
    });

    it('should return validation error for ineligible requisition', async () => {
      const mockValidationResult = {
        isEligible: false,
        errorType: 'ERROR1_AUTHORIZATION',
        reason: 'Access denied',
        buttonVisible: false,
      };

      mockForceCloseService.validateForceCloseEligibility.resolves(mockValidationResult);

      await forceCloseController.validateForceClose(mockRequest, mockReply);

      expect(mockReply.code).to.have.been.calledWith(200);
      expect(mockReply.send).to.have.been.calledWith({
        success: false,
        data: mockValidationResult,
      });
    });

    it('should handle service errors gracefully', async () => {
      const error = new Error('Service error');
      mockForceCloseService.validateForceCloseEligibility.rejects(error);

      await forceCloseController.validateForceClose(mockRequest, mockReply);

      expect(mockFastify.log.error).to.have.been.calledWith(
        'Force close validation failed for RS: 123 - Service error'
      );
      expect(mockReply.code).to.have.been.calledWith(500);
      expect(mockReply.send).to.have.been.calledWith({
        success: false,
        message: 'Force close validation failed',
        error: 'Service error',
      });
    });

    it('should handle invalid requisition ID', async () => {
      mockRequest.params.requisitionId = 'invalid';

      await forceCloseController.validateForceClose(mockRequest, mockReply);

      expect(mockReply.code).to.have.been.calledWith(400);
      expect(mockReply.send).to.have.been.calledWith({
        success: false,
        message: 'Invalid requisition ID',
      });
    });
  });

  describe('executeForceClose', () => {
    beforeEach(() => {
      mockRequest.body = {
        notes: 'Test force close notes',
        confirmedScenario: 'ACTIVE_PO_PARTIAL_DELIVERY',
        acknowledgedImpacts: ['Impact 1', 'Impact 2'],
      };
    });

    it('should execute force close successfully', async () => {
      const mockExecutionResult = {
        success: true,
        requisitionId: 123,
        scenario: 'ACTIVE_PO_PARTIAL_DELIVERY',
        forceCloseId: 456,
        documentsUpdated: [{ type: 'requisition', id: 123, status: 'CLOSED' }],
        quantitiesReturned: {},
        poAdjustments: [],
      };

      mockForceCloseService.executeForceClose.resolves(mockExecutionResult);

      await forceCloseController.executeForceClose(mockRequest, mockReply);

      expect(mockForceCloseService.executeForceClose).to.have.been.calledWith({
        requisitionId: 123,
        userFromToken: mockRequest.userFromToken,
        notes: mockRequest.body.notes,
        scenario: mockRequest.body.confirmedScenario,
        acknowledgedImpacts: mockRequest.body.acknowledgedImpacts,
      });

      expect(mockReply.code).to.have.been.calledWith(200);
      expect(mockReply.send).to.have.been.calledWith({
        success: true,
        data: mockExecutionResult,
      });
    });

    it('should return execution error for failed force close', async () => {
      const mockExecutionResult = {
        success: false,
        error: 'Execution failed',
        reason: 'Validation error',
      };

      mockForceCloseService.executeForceClose.resolves(mockExecutionResult);

      await forceCloseController.executeForceClose(mockRequest, mockReply);

      expect(mockReply.code).to.have.been.calledWith(400);
      expect(mockReply.send).to.have.been.calledWith({
        success: false,
        data: mockExecutionResult,
      });
    });

    it('should handle missing required fields', async () => {
      mockRequest.body = { notes: 'Test notes' }; // Missing confirmedScenario

      await forceCloseController.executeForceClose(mockRequest, mockReply);

      expect(mockReply.code).to.have.been.calledWith(400);
      expect(mockReply.send).to.have.been.calledWith({
        success: false,
        message: 'Missing required fields: confirmedScenario',
      });
    });

    it('should handle service errors during execution', async () => {
      const error = new Error('Execution service error');
      mockForceCloseService.executeForceClose.rejects(error);

      await forceCloseController.executeForceClose(mockRequest, mockReply);

      expect(mockFastify.log.error).to.have.been.calledWith(
        'Force close execution failed for RS: 123 - Execution service error'
      );
      expect(mockReply.code).to.have.been.calledWith(500);
      expect(mockReply.send).to.have.been.calledWith({
        success: false,
        message: 'Force close execution failed',
        error: 'Execution service error',
      });
    });
  });

  describe('getForceCloseHistory', () => {
    it('should return force close history successfully', async () => {
      const mockHistoryResult = {
        history: [
          {
            id: 1,
            requisitionId: 123,
            scenarioType: 'ACTIVE_PO_PARTIAL_DELIVERY',
            forceCloseNotes: 'Test notes',
            createdAt: new Date(),
          },
        ],
        auditTrail: [],
        summary: {
          totalForceCloses: 1,
          lastForceCloseDate: new Date(),
        },
      };

      mockForceCloseService.getForceCloseHistory.resolves(mockHistoryResult);

      await forceCloseController.getForceCloseHistory(mockRequest, mockReply);

      expect(mockForceCloseService.getForceCloseHistory).to.have.been.calledWith({
        requisitionId: 123,
        userId: mockRequest.userFromToken.id,
      });

      expect(mockReply.code).to.have.been.calledWith(200);
      expect(mockReply.send).to.have.been.calledWith({
        success: true,
        data: mockHistoryResult,
      });
    });

    it('should handle history retrieval errors', async () => {
      const error = new Error('History service error');
      mockForceCloseService.getForceCloseHistory.rejects(error);

      await forceCloseController.getForceCloseHistory(mockRequest, mockReply);

      expect(mockFastify.log.error).to.have.been.calledWith(
        'Force close history retrieval failed for RS: 123 - History service error'
      );
      expect(mockReply.code).to.have.been.calledWith(500);
      expect(mockReply.send).to.have.been.calledWith({
        success: false,
        message: 'Force close history retrieval failed',
        error: 'History service error',
      });
    });
  });
});
