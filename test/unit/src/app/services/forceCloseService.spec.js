const chai = require('chai');
const sinonChai = require('sinon-chai');
const sinon = require('sinon');

chai.use(sinonChai);

const { expect } = chai;
const { restore, stub, createStubInstance } = sinon;

describe('ForceCloseService', () => {
  let forceCloseService;
  let mockContainer;
  let mockFastify;
  let mockConstants;
  let mockRepositories;

  beforeEach(() => {
    // Mock fastify logger
    mockFastify = {
      log: {
        info: stub(),
        warn: stub(),
        error: stub(),
      },
    };

    // Mock constants
    mockConstants = {
      forceClose: {
        FORCE_CLOSE_SCENARIOS: {
          ACTIVE_PO_PARTIAL_DELIVERY: 'ACTIVE_PO_PARTIAL_DELIVERY',
          CLOSED_PO_REMAINING_QTY: 'CLOSED_PO_REMAINING_QTY',
          CLOSED_PO_PENDING_CS: 'CLOSED_PO_PENDING_CS',
        },
        FORCE_CLOSE_ERRORS: {
          ACCESS_DENIED: 'Access denied',
          RS_NOT_APPROVED: 'Requisition must be fully approved',
          INVALID_PO_STATUS: 'Invalid PO status',
          NO_VALID_SCENARIO: 'No valid scenario',
          UNPAID_DELIVERIES: 'Unpaid deliveries',
          AUTO_CLOSE_DETECTED: 'Auto-close detected',
        },
        FORCE_CLOSE_ACTIONS: {
          UPDATE_PO_AMOUNTS: 'Update PO amounts',
          ZERO_REMAINING_QTY: 'Zero remaining quantities',
          CANCEL_PENDING_CS: 'Cancel pending CS',
        },
      },
    };

    // Mock repositories
    mockRepositories = {
      forceCloseRepository: {
        executeForceCloseWorkflow: stub(),
      },
      requisitionRepository: {
        getById: stub(),
        update: stub(),
      },
      purchaseOrderRepository: {
        findAll: stub(),
      },
      canvassRequisitionRepository: {
        findAll: stub(),
      },
    };

    // Mock container
    mockContainer = {
      fastify: mockFastify,
      constants: mockConstants,
      ...mockRepositories,
    };

    // Import and instantiate ForceCloseService
    const ForceCloseService = require('../../../../../src/app/services/forceCloseService');
    forceCloseService = new ForceCloseService(mockContainer);
  });

  afterEach(() => {
    restore();
  });

  describe('Constructor', () => {
    it('should initialize with container dependencies', () => {
      expect(forceCloseService.fastify).to.equal(mockFastify);
      expect(forceCloseService.constants).to.equal(mockConstants);
      expect(forceCloseService.forceCloseRepository).to.equal(mockRepositories.forceCloseRepository);
    });
  });

  describe('validateForceCloseEligibility', () => {
    const mockParams = {
      requisitionId: 123,
      userFromToken: { id: 1, username: 'testuser' },
      notes: 'Test force close notes',
      checkOnly: false,
    };

    it('should return eligible result for valid scenario', async () => {
      // Mock successful validation chain
      stub(forceCloseService, '_validateUserAuthorization').resolves({
        isAuthorized: true,
        details: 'User authorized',
      });
      
      stub(forceCloseService, '_validateRequisitionStatus').resolves({
        isValid: true,
        showButton: true,
      });
      
      stub(forceCloseService, '_determineForceCloseScenario').resolves({
        isEligible: true,
        scenario: 'ACTIVE_PO_PARTIAL_DELIVERY',
        details: { type: 'Active PO with partial delivery' },
      });

      const result = await forceCloseService.validateForceCloseEligibility(mockParams);

      expect(result.isEligible).to.be.true;
      expect(result.scenario).to.equal('ACTIVE_PO_PARTIAL_DELIVERY');
      expect(mockFastify.log.info).to.have.been.calledWith(
        'Validating force close eligibility for RS: 123'
      );
    });

    it('should return authorization error for unauthorized user', async () => {
      stub(forceCloseService, '_validateUserAuthorization').resolves({
        isAuthorized: false,
        details: 'User not authorized',
      });

      const result = await forceCloseService.validateForceCloseEligibility(mockParams);

      expect(result.isEligible).to.be.false;
      expect(result.errorType).to.equal('ERROR1_AUTHORIZATION');
      expect(result.buttonVisible).to.be.false;
    });

    it('should return requisition status error for invalid RS status', async () => {
      stub(forceCloseService, '_validateUserAuthorization').resolves({
        isAuthorized: true,
        details: 'User authorized',
      });
      
      stub(forceCloseService, '_validateRequisitionStatus').resolves({
        isValid: false,
        reason: 'RS not approved',
        showButton: false,
      });

      const result = await forceCloseService.validateForceCloseEligibility(mockParams);

      expect(result.isEligible).to.be.false;
      expect(result.errorType).to.equal('ERROR2_RS_STATUS');
      expect(result.reason).to.equal('RS not approved');
    });
  });

  describe('_checkPOStatus', () => {
    it('should return active PO path for FOR_DELIVERY status', async () => {
      const mockPOs = [
        {
          id: 1,
          status: 'FOR_DELIVERY',
          amount: 1000,
          quantity: 10,
          deliveryReceipts: [{ id: 1 }],
          rsPaymentRequests: [{ id: 1 }],
        },
      ];

      mockRepositories.purchaseOrderRepository.findAll.resolves(mockPOs);

      const result = await forceCloseService._checkPOStatus(123);

      expect(result.isValid).to.be.true;
      expect(result.hasActivePOs).to.be.true;
      expect(result.allClosed).to.be.false;
      expect(result.activePOs).to.have.length(1);
      expect(result.details.validationPath).to.equal('ACTIVE_PO_PATH');
    });

    it('should return closed PO path for CLOSED/CANCELLED status', async () => {
      const mockPOs = [
        {
          id: 1,
          status: 'CLOSED',
          amount: 1000,
          quantity: 10,
          deliveryReceipts: [{ id: 1 }],
          rsPaymentRequests: [{ id: 1 }],
        },
        {
          id: 2,
          status: 'CANCELLED',
          amount: 500,
          quantity: 5,
          deliveryReceipts: [],
          rsPaymentRequests: [],
        },
      ];

      mockRepositories.purchaseOrderRepository.findAll.resolves(mockPOs);

      const result = await forceCloseService._checkPOStatus(123);

      expect(result.isValid).to.be.true;
      expect(result.hasActivePOs).to.be.false;
      expect(result.allClosed).to.be.true;
      expect(result.closedPOs).to.have.length(2);
      expect(result.details.validationPath).to.equal('CLOSED_PO_PATH');
    });

    it('should return invalid for unsupported PO status', async () => {
      const mockPOs = [
        {
          id: 1,
          status: 'FOR_PO_REVIEW',
          amount: 1000,
          quantity: 10,
        },
      ];

      mockRepositories.purchaseOrderRepository.findAll.resolves(mockPOs);

      const result = await forceCloseService._checkPOStatus(123);

      expect(result.isValid).to.be.false;
      expect(result.details.error).to.include('invalid status');
      expect(result.details.invalidPOs).to.have.length(1);
    });
  });

  describe('_checkPaymentPrerequisite', () => {
    it('should return allPaid true when all deliveries are paid', async () => {
      const mockPOs = [
        {
          id: 1,
          status: 'FOR_DELIVERY',
          deliveryReceipts: [
            {
              id: 1,
              status: 'DR_APPROVED',
              deliveryReceiptItems: [
                { quantity: 5, unitPrice: 100 }, // 500 total
              ],
            },
          ],
          rsPaymentRequests: [
            {
              id: 1,
              deliveryReceiptId: 1,
              status: 'PR_APPROVED',
              amount: 500,
            },
          ],
        },
      ];

      mockRepositories.purchaseOrderRepository.findAll.resolves(mockPOs);

      const result = await forceCloseService._checkPaymentPrerequisite(123);

      expect(result.allPaid).to.be.true;
      expect(result.details.unpaidDeliveries).to.have.length(0);
    });

    it('should return allPaid false when deliveries are unpaid', async () => {
      const mockPOs = [
        {
          id: 1,
          status: 'FOR_DELIVERY',
          deliveryReceipts: [
            {
              id: 1,
              status: 'DR_APPROVED',
              deliveryReceiptItems: [
                { quantity: 5, unitPrice: 100 }, // 500 total
              ],
            },
          ],
          rsPaymentRequests: [], // No payments
        },
      ];

      mockRepositories.purchaseOrderRepository.findAll.resolves(mockPOs);

      const result = await forceCloseService._checkPaymentPrerequisite(123);

      expect(result.allPaid).to.be.false;
      expect(result.details.unpaidDeliveries).to.have.length(1);
      expect(result.details.unpaidDeliveries[0].reason).to.include('No corresponding payment');
    });
  });

  describe('_checkPendingCanvassSheets', () => {
    it('should detect pending canvass sheets', async () => {
      const mockCS = [
        {
          id: 1,
          status: 'CS_PENDING_APPROVAL',
          createdAt: new Date(),
          canvassItems: [{ id: 1 }],
          approvals: [],
        },
        {
          id: 2,
          status: 'CS_APPROVED',
          updatedAt: new Date(),
        },
      ];

      mockRepositories.canvassRequisitionRepository.findAll.resolves(mockCS);

      const result = await forceCloseService._checkPendingCanvassSheets(123);

      expect(result.hasPending).to.be.true;
      expect(result.details.pendingCount).to.equal(1);
      expect(result.details.approvedCount).to.equal(1);
    });

    it('should return no pending when all sheets are processed', async () => {
      const mockCS = [
        {
          id: 1,
          status: 'CS_APPROVED',
          updatedAt: new Date(),
        },
        {
          id: 2,
          status: 'CS_REJECTED',
          updatedAt: new Date(),
        },
      ];

      mockRepositories.canvassRequisitionRepository.findAll.resolves(mockCS);

      const result = await forceCloseService._checkPendingCanvassSheets(123);

      expect(result.hasPending).to.be.false;
      expect(result.details.pendingCount).to.equal(0);
      expect(result.details.approvedCount).to.equal(1);
      expect(result.details.rejectedCount).to.equal(1);
    });
  });
});
